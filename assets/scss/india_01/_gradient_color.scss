$pv1-color: map-get($colors, primary-variant-1);
$pv2-color: map-get($colors, primary-variant-2);
$pv3-color: map-get($colors, primary-variant-3);
$grey-1-color: map-get($colors, grey-1);
$grey-2-color: map-get($colors, grey-2);
$grey-3-color: map-get($colors, grey-3);
$grey-4-color: map-get($colors, grey-4);
$grey-5-color: map-get($colors, grey-5);
$primary-color: map-get($colors, primary);

.gradient-primary {
  background: linear-gradient(180deg, #fcf3dd 0%, #dfc181 100%);
}

.gradient-primary-reverse {
  background: linear-gradient(180deg, #dfc181 0%, #fcf3dd 100%);
}

.gradient-primary-left {
  background: linear-gradient(270deg, #857555 0%, #dfc181 100%);
}

.gradient-light-primary {
  background: linear-gradient(180deg, rgba(102, 191, 255, 0.8) 0%, rgba(65, 118, 250, 1) 100%);
}

.gradient-white-card {
  background: linear-gradient(180deg, $white-color 29.17%, $grey-4-color 92.71%, $grey-5-color 100%);
}

.gradient-button {
  background: linear-gradient(180deg, #ffedd4 0%, #dfc181 100%);
}

.gradient-game-maintenance {
  background: linear-gradient(180deg, rgba(181, 186, 197, 0.5) 0%, #222730 100%);
}

.gradient-primary-box {
  background: linear-gradient(270.47deg, #cbd8ec -0.45%, #253755 99.59%);
}

.gradient-title--text {
  background: linear-gradient(180deg, #ffe6c2 0%, #dfc181 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}
.gradient-subtitle--text {
  background: linear-gradient(180deg, $grey-2-color 61.46%, $grey-4-color 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
}

.gradient-primary--text {
  background: linear-gradient(180deg, #fcf3dd 0%, #dfc181 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-radius: 4px;
}

.gradient-primary-border {
  border: 3px solid;
  border-image: linear-gradient(180deg, rgba(255, 231, 189, 1), rgba(247, 182, 117, 1)) 1;
}

.app-bar-gradient {
  background: #162e5d !important;
}
.bottom-bar-gradient {
  background: #162e5d !important;
}

.app-bar-button {
  background: linear-gradient(180deg, #fcf3dd 0%, #dfc181 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-line {
  width: 100%;
  height: 100%;
  border: solid 1px transparent;
  background-image: linear-gradient(360deg, #d39f43 0%, #ffffff 50.52%, #d39f43 100%);
  background-origin: border-box;
}

.card-gradient-1 {
  background: linear-gradient(180deg, #ac2335 29.17%, #550d10 92.71%, #a0574c 100%);
}

.card-gradient-2 {
  background: linear-gradient(180deg, #09315e 0%, #091842 100%);
}

.background {
  background: #051f42;
}

.info-card {
  background: linear-gradient(180deg, #3a6392 0%, #051f42 100%);
}

.vip-title {
  background: linear-gradient(180deg, #fec600 0%, #dc1646 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-game-maintenance-2 {
  background: linear-gradient(180deg, rgba(181, 186, 197, 0) 0%, #222730 100%);
}

.gradient-side-bar-game {
  background: linear-gradient(99.09deg, #083591 -2.56%, #0c65cc 37.71%, #c4cbff 109.3%);
}

.game-intro-grade-bg {
  background: linear-gradient(180deg, #4169a9 0%, rgba(73, 124, 160, 0.4) 50%, rgba(113, 159, 198, 0) 100%) !important;
}

.game-intro-grade-border {
  background: linear-gradient(270deg, #283f65 0%, #3a6392 28%, rgba(69, 114, 161, 0.2) 76%, rgba(18, 40, 84, 0.2) 100%);
  &::before {
    border: 2px solid;
    border-image: linear-gradient(
        137.18deg,
        #bac8d6 -0.44%,
        #4572a1 49.78%,
        rgba(43, 68, 114, 0.2) 74.89%,
        #cbd8ec 87.45%,
        rgba(43, 68, 114, 0.2) 100%
      )
      1;
  }
}
.mail-attachment-gradient-bg {
  background: linear-gradient(180deg, #6573f2 0%, rgba(101, 115, 242, 0.4) 50%, rgba(101, 115, 242, 0) 100%);
}

.gift-pack-reward-card-fill {
  background: #4572a1;
  background-blend-mode: multiply;
  background: conic-gradient(from -44.35deg at 100% 100%, rgba(69, 114, 161, 0) 0deg, #4572a1 360deg);
  background-blend-mode: screen;
  background: radial-gradient(98.24% 20.9% at 50% 100%, #4572a1 0%, rgba(69, 114, 161, 0) 100%);
  background-blend-mode: screen;
  background: radial-gradient(95.52% 27.88% at 50% -12.07%, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}
.gift-pack-reward-card-fill-vip {
  background: #224273;
  background-blend-mode: multiply;
  background: conic-gradient(from -44.35deg at 100% 100%, rgba(34, 66, 115, 0) 0deg, #224273 360deg);
  background-blend-mode: screen;
  background: radial-gradient(98.24% 20.9% at 50% 100%, #224273 0%, rgba(34, 66, 115, 0) 100%);
  background-blend-mode: screen;
  background: radial-gradient(95.52% 27.88% at 50% -12.07%, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}

.gift-pack-frame {
  background: linear-gradient(270deg, rgba(173, 196, 255, 0) 0%, rgba(173, 196, 255, 0.8) 51%, rgba(173, 196, 255, 0) 100%);
}
.gift-pack-frame-vip {
  background: linear-gradient(270deg, rgba(50, 89, 138, 0) 0%, rgba(50, 89, 138, 0.8) 51%, rgba(50, 89, 138, 0) 100%);
}

.gradient-table {
  background: linear-gradient(270deg, #000a24 0%, #224273 100%);
}

.player-ranking-hint-bg-fill{
  background:  linear-gradient(90deg, rgba(26, 11, 11, 0.00) 0%, rgba(26, 11, 11, 0.42) 50%);
  }

  .player-ranking-bn-bg-fill-1{
    background:  linear-gradient(98deg, #7E1635 51.8%, rgba(126, 22, 53, 0.00) 86.67%);
  }

  .player-ranking-button-fill{
    background:  linear-gradient(90deg, rgba(168, 50, 84, 0.00) 0%, rgba(168, 50, 84, 0.30) 25%, rgba(168, 50, 84, 0.80) 50%, rgba(168, 50, 84, 0.30) 75%, rgba(168, 50, 84, 0.00) 100%);
  }

  .player-ranking-winners-fill--text{
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 30%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.2) 70%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .player-ranking-top-three-bg-fill-1{
    background: linear-gradient(90deg, #6D422D -0.01%, #A57259 49.76%, #A57259 69.82%, #6D422D 99.99%);
  }
