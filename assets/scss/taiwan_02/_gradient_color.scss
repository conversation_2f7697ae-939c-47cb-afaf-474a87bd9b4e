$pv1-color: map-get($colors, primary-variant-1);
$pv2-color: map-get($colors, primary-variant-2);
$pv3-color: map-get($colors, primary-variant-3);
$grey-1-color: map-get($colors, grey-1);
$grey-2-color: map-get($colors, grey-2);
$grey-3-color: map-get($colors, grey-3);
$grey-4-color: map-get($colors, grey-4);
$grey-5-color: map-get($colors, grey-5);
$primary-color: map-get($colors, primary);
$primary-variant-1: map-get($colors, primary-variant-1);
$primary-variant-2: map-get($colors, primary-variant-2);
$primary-variant-3: map-get($colors, primary-variant-3);

.gradient-primary {
  background: linear-gradient(180deg, $primary-variant-3 0%, $primary-variant-2 100%);
}

.gradient-primary-reverse {
  background: linear-gradient(180deg, $primary-variant-2 0%, $primary-variant-3 100%);
}

.gradient-primary-left {
  background: linear-gradient(270deg, $primary-variant-3 0%, $primary-variant-1 100%);
}

.gradient-light-primary {
  background: linear-gradient(180deg, rgba(102, 191, 255, 0.8) 0%, rgba(65, 118, 250, 1) 100%);
}

.gradient-white-card {
  background: linear-gradient(180deg, $white-color 29.17%, $grey-4-color 92.71%, $grey-5-color 100%);
}

.gradient-button {
  background: linear-gradient(180deg, $primary-variant-3 0%, $primary-variant-2 100%);
}

.gradient-game-maintenance {
  background: linear-gradient(180deg, rgba(150, 144, 153, 0.5) 0%, #1c1621 100%);
}

.gradient-primary-box {
  background: linear-gradient(270.47deg, #512e6b -0.45%, #984b9a 99.59%);
}

.gradient-title {
  background: linear-gradient(180deg, #fff46c 0%, #ffa620 100%);
}

.gradient-title--text {
  background: linear-gradient(180deg, #fff46c 0%, #ffa620 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}
.gradient-subtitle--text {
  background: linear-gradient(180deg, $grey-2-color 61.46%, $grey-4-color 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
}

.gradient-primary--text {
  background: linear-gradient(180deg, $primary-variant-3 0%, $primary-variant-2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-radius: 4px;
}

.gradient-primary-border {
  border: 3px solid;
  border-image: linear-gradient(180deg, rgba(255, 231, 189, 1), rgba(247, 182, 117, 1)) 1;
}

.app-bar-gradient {
  background: linear-gradient(180deg, #070a29 0%, #36083e 100%);
}
.bottom-bar-gradient {
  background: linear-gradient(180deg, #070a29 0%, #36083e 100%);
}

.app-bar-button {
  background: linear-gradient(180deg, #fff576 0%, #ffaf37 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-line {
  width: 100%;
  height: 100%;
  border: solid 0.14159rem transparent;
  background-image: linear-gradient(360deg, #e9b94f 0%, #ffffff 50.52%, #e9b94f 100%);
  background-origin: border-box;
}

.card-gradient-1 {
  background: linear-gradient(180deg, #ac2335 29.17%, #550d10 92.71%, #a0574c 100%);
}

.card-gradient-2 {
  background: linear-gradient(180deg, #4d2161 0%, #1f0e2d 100%);
}

.background {
  background: linear-gradient(180deg, #420f4b 0%, #070a29 100%);
}

.info-card {
  background: linear-gradient(180deg, #673d88 0%, #461d7a 100%);
}

.vip-title {
  background: linear-gradient(180deg, #fe7a00 0%, #ac2335 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-game-maintenance-2 {
  background: linear-gradient(180deg, rgba(150, 144, 153, 0) 0%, #1c1621 100%);
}

.gradient-border-right {
  background: linear-gradient(270deg, rgba(255, 175, 55, 0) 0%, #ffaf37 100%);
}

.gradient-border-left {
  background: linear-gradient(270deg, #ffaf37 0%, rgba(255, 175, 55, 0) 100%);
}

.gradient-primary-down {
  background: linear-gradient(180deg, #fff576 0%, #ff9900 100%);
}

.v-application .footer-fill {
  background-color: rgba(52, 15, 100, 0) !important;
  background: linear-gradient(180deg, rgba(52, 15, 100, 0), rgba(52, 15, 100, 1));
}

.bottom-bar-gradient {
  background: linear-gradient(180deg, #510461 0%, #070a29 100%);
}

.app-bar-line {
  background: linear-gradient(90deg, #007cfb 0.02%, #9b33d6 99.98%);
}

.gradient-side-bar-game {
  background: linear-gradient(100deg, #8754ad 25.77%, #6a2c94 71.04%, #5b3573 103.52%);
}

.game-intro-grade-bg {
  background: linear-gradient(180deg, #5847c1 0%, rgba(85, 62, 162, 0.4) 50%, rgba(82, 52, 131, 0) 100%) !important;
}

.game-intro-grade-border {
  background: linear-gradient(270deg, rgba(68, 41, 105, 0.2) 0%, rgba(68, 42, 107, 0.2) 24%, #6a3aa8 72%, #452460 100%);
  &::before {
    border: 2px solid;
    border-image: linear-gradient(
        to right bottom,
        #dbdaff 0%,
        #e2c0fd 50%,
        rgba(74, 44, 85, 0.2) 75%,
        #6d548c 87.5%,
        rgba(255, 255, 255, 0.2) 100%
      )
      1;
  }
}

.gift-pack-reward-card-fill {
  background: linear-gradient(270deg,rgba(183, 88, 0, 0) 0%,rgba(183, 88, 0, 0.5) 12.86%,#fe7a00 50%,rgba(183, 88, 0, 0.5) 85%,rgba(183, 88, 0, 0) 100%);
}

.gift-pack-frame {
  background: linear-gradient(270deg,rgba(185, 167, 237, 0) 0%,rgba(185, 167, 237, 0.8) 51%,rgba(185, 167, 237, 0) 100%);
}
.gift-pack-frame-vip {
  background: linear-gradient(270deg, rgba(181, 167, 237, 0) 0%, rgba(181, 167, 237, 0.8) 51%, rgba(181, 167, 237, 0) 100%);
}
