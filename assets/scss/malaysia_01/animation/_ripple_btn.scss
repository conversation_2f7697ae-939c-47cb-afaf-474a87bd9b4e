.ripple-animation-btn {
  width: 64px;
  height: 64px;
  text-transform: uppercase;
  letter-spacing: 1.3px;
  font-weight: 700;
  background: #FFC700;
  background: linear-gradient(270deg, rgba(255, 199, 0, 1) 0%, rgba(255, 241, 104, 1) 100%);
  border: none;
  border-radius: 50%;
  box-shadow: 0px 12px 24px rgba(254, 166, 0, 0.64) !important;
  transition: all 0.3s ease-in-out 0s;
  cursor: pointer;
  outline: none;
  position: relative;
  padding: 10px;

  &::before {
    content: "";
    border-radius: 50%;
    min-width: calc(64px + 12px);
    min-height: calc(64px + 12px);
    border: 1px solid #FFC700;
    box-shadow: 0 0 60px rgba(255, 189, 63, 0.64);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.3s ease-in-out 0s;
  }

  &::after {
    content: "";
    width: 30px;
    height: 30px;
    border-radius: 100%;
    border: 1px solid #FFC700;
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: ring 2.0s infinite;
  }

  &:hover::after,
  &:focus::after {
    animation: none;
    display: none;
  }
}

@keyframes ring {
  0% {
    width: 64px;
    height: 64px;
    opacity: 0;
  }

  40% {
    width: 64px;
    height: 64px;
    opacity: 1;
  }

  100% {
    width: 95px;
    height: 95px;
    opacity: 0;
  }
}
