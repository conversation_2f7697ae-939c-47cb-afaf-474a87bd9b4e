.components-avatar-layout {
  position: relative;
  height: 180px;
  width: 100%;

  .avatar-border {
    // border-radius: 50%; //圖片正方形
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .v-responsive__content {
      display: flex;
      justify-items: center;
      align-items: center;
    }

    &:not(.no-upload) {
      cursor: pointer;

      .upload-icon {
        display: none;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }

      &:hover {
        &::before {
          content: "";
          position: absolute;
          width: 120px;
          height: 120px;
          left: 50%;
          top: 50%;
          // border-radius: 50%; //圖片正方形
          transform: translate(-50%, -50%);
          background-color: rgba(0, 0, 0, 0.7);
          cursor: pointer;
        }

        .upload-icon {
          display: initial;
        }
      }
    }
  }

  .avatar {
    // border-radius: 50%; //圖片正方形

    &.brand {
      top: 50%;
      left: 50%;
      overflow: hidden;
      position: absolute;
      transform: translate(-50%, -50%);

      .v-responsive__content {
        position: absolute;
        height: 100%;
        top: 0;
        right: 0;
      }

      &:not(.no-upload) {
        cursor: pointer;

        .upload-icon {
          display: none;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }

        &:hover {
          position: absolute;

          &::before {
            content: "";
            position: absolute;
            width: 130px;
            height: 130px;
            left: 50%;
            top: 50%;
            // border-radius: 50%; //圖片正方形
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.7);
            cursor: pointer;
          }

          .upload-icon {
            display: initial;
          }
        }
      }
    }
  }

  &.small {
    width: 50px;
    height: 50px;
    overflow: hidden;
  }

  &.middle {
    width: 100px;
    height: 100px;
    overflow: hidden;
  }
}
