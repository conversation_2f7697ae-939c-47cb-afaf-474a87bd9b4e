<template>
  <div>
    <v-alert
      v-model="installPwaStatusTmp"
      dense
      rounded="0"
      type="info"
      transition="scale-transition"
    >
      <v-row align="center">
        <v-col cols="8" sm="10" class="grow default-content--text">
          {{ $t('change_browser_noty') }}
          <v-progress-circular :rotate="180" :size="30" :width="3" :value="timer">
            {{ timer / 10 }}
          </v-progress-circular>
        </v-col>
        <v-col cols="4" sm="2" class="shrink">
          <v-btn color="default-content" class="info--text" @click="download">{{
            $t('install')
          }}</v-btn>
        </v-col>
      </v-row>
    </v-alert>
  </div>
</template>
<script>
  export default {
    name: 'installPwa',
    props: {
      installPwaStatus: {
        type: Boolean
      }
    },
    data() {
      return {
        installPwaStatusTmp: this.installPwaStatus,
        interval: {},
        timer: 300,
        prompt: null
      }
    },
    watch: {
      installPwaStatus: {
        immediate: true,
        handler(val) {
          this.installPwaStatusTmp = val
        }
      },
      timer: {
        handler(val) {
          if (val === 0) {
            this.installPwaStatusTmp = false
            this.$emit('update:installPwaStatus', false)
            clearInterval(this.interval)
          }
        }
      }
    },
    beforeDestroy() {
      clearInterval(this.interval)
    },
    mounted() {
      window.addEventListener('beforeinstallprompt', this.beforeinstallprompt)
      this.interval = setInterval(() => {
        if (this.timer === 0) {
          return (this.timer = 300)
        }
        this.timer -= 10
      }, 1000)
    },
    methods: {
      beforeinstallprompt(e) {
        this.prompt = e
        e.preventDefault()
        //檢查是否有安裝PWA
        if ('getInstalledRelatedApps' in navigator) {
          navigator
            .getInstalledRelatedApps()
            .then((relatedApps) => {
              if (relatedApps.length <= 0) {
                //未安裝
                this.installPwaStatusTmp = true
                this.$emit('update:installPwaStatus', true)
              }
            })
            .catch((error) => {
              console.error('獲取相關應用程式信息時出錯：', error)
            })
        } else {
          console.log('瀏覽器不支援 getInstalledRelatedApps API')
        }
        return false
      },
      download() {
        if (this.prompt) {
          // 异步触发横幅显示，弹出选择框，代替浏览器默认动作
          this.prompt.prompt()
          // 接收选择结果
          this.prompt.userChoice
            .then((result) => {
              //用戶選擇安裝 PWA
              if (result.outcome === 'accepted') {
                //安裝之後隱藏   不然會繼續倒數
                this.installPwaStatusTmp = false
                this.$emit('update:installPwaStatus', false)
              }
              // {outcome: "dismissed", platform: ""} // 取消添加
              // {outcome: "accepted", platform: "web"} // 完成添加
            })
            .catch((err) => {
              console.log(err)
            })
        }
      }
    }
  }
</script>
