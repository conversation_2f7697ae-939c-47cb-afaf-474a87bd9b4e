<template>
  <div>
    <v-dialog
      v-model="showChatRoomDialogStatusTemp"
      max-width="800"
      :fullscreen="
        $vuetify.breakpoint.xsOnly || ($vuetify.breakpoint.smAndDown && orientation !== 0)
      "
      persistent
      transition="dialog-transition"
      content-class="transparent chatroom-dialog"
    >
      <v-card
        :class="{
          'fill-height':
            $vuetify.breakpoint.xsOnly || ($vuetify.breakpoint.smOnly && orientation !== 0)
        }"
        :style="{ 'background-color': bgColor }"
      >
        <customDialogTitle
          :second-icon="cardTitle.second.icon"
          :second-icon-action="cardTitle.second.action"
          :noty="allChatNoty"
          :title="$t('chat_session').toUpperCase()"
          @closeDialog="closeDialog"
        />
        <v-card-text :class="['chatroom', 'pa-0']">
          <!-- channel list -->
          <v-row no-gutters class="fill-height">
            <v-col
              v-show="$vuetify.breakpoint.smAndUp || !showChatMobile"
              xl="4"
              lg="4"
              md="4"
              sm="4"
              cols="12"
              class="fill-height"
            >
              <div class="d-flex flex-column left-side fill-height">
                <!-- tab -->
                <div class="dialog-fill-2 left-side-tab">
                  <v-tabs
                    v-model="tab"
                    fixed-tabs
                    background-color="dialog-fill-2"
                    class="topbar-height"
                  >
                    <v-tabs-slider color="primary"></v-tabs-slider>

                    <v-tab
                      v-for="chatTabItem in chatTabItems"
                      :key="chatTabItem.id"
                      @click="selectTabEvent"
                    >
                      <span class="custom-text-noto text-button">{{ $t(chatTabItem.title) }}</span>
                    </v-tab>
                  </v-tabs>
                </div>
                <!-- list & textfield -->
                <div class="d-flex flex-column left-side-content">
                  <!-- text-field -->
                  <div
                    class="left-side-content-textfield px-3 pt-4"
                    :style="{ 'background-color': bgColor }"
                  >
                    <v-text-field
                      v-model="chatListKeyword"
                      :label="$t('input_secret_nickname')"
                      :hint="isNoTarget ? $t('use_magnifying_glass_for_further_search') : ''"
                      single-line
                      rounded
                      outlined
                      dense
                      maxlength="12"
                      @input="selectFirstChat"
                    >
                      <template v-slot:append>
                        <span
                          v-show="chatListKeyword.length > 0"
                          class="material-symbols-outlined cursor-pointer mr-1"
                          @click="chatListKeyword = ''"
                        >
                          close
                        </span>
                        <span
                          class="material-symbols-outlined cursor-pointer"
                          @click="searchPlayer"
                        >
                          search
                        </span>
                      </template>
                    </v-text-field>
                  </div>
                  <!-- list -->
                  <div class="left-side-content-list" :style="{ 'background-color': bgColor }">
                    <!-- chat list -->
                    <chatList
                      ref="chatList"
                      class="scrollable"
                      :keyword="chatListKeyword"
                      :selected-chat.sync="selectedChat"
                      :selected-friend-chat.sync="selectedFriendChat"
                      :is-friend-chat="tab === 1"
                      :chats="chats"
                      :friend-list-from-index="friendList"
                      @selectedChatEvent="selectedChatEvent"
                      @selectedFriendChatEvent="selectedFriendChatEvent"
                      @mobileClickedEvent="mobileClickedEvent"
                      @click="closeHeatList"
                    />
                  </div>
                </div>
              </div>
            </v-col>
            <!-- chat container -->
            <v-col
              v-show="$vuetify.breakpoint.smAndUp || showChatMobile"
              xl="8"
              lg="8"
              md="8"
              sm="8"
              cols="12"
              class="chat-container fill-height"
            >
              <div class="d-flex flex-column fill-height">
                <!-- title & action bar -->
                <div class="dialog-fill-2">
                  <!-- title -->
                  <msgTitle
                    ref="msgTitle"
                    :current-chat="currentChat"
                    :current-friend-chat="currentFriendChat"
                    :is-friend-chat="isFriendChat"
                    :channels="channels"
                    :selected-channel.sync="selectedChannel"
                    :going-to-channel.sync="goingToChannel"
                    :current-channel-detail="currentChannelDetail"
                    :show-change-channel-dialog-status.sync="showChangeChannelDialogStatus"
                    :selected-channel-tmp-val.sync="selectedChannelTmp.val"
                    :selected-channel-tmp-old-val.sync="selectedChannelTmp.oldVal"
                    :selected-channel-tmp-old-val-index.sync="selectedChannelTmp.oldValIndex"
                    :show-chat-mobile.sync="showChatMobile"
                  />
                  <!-- action btn -->
                  <v-row
                    v-if="isStranger"
                    no-gutters
                    justify="center"
                    align="center"
                    class="action-bar"
                  >
                    <v-btn
                      color="primary-variant-1"
                      class="button-content--text mr-4"
                      :disabled="addFriendBtnDisableStatus"
                      @click="checkAddFriend(currentChat.title)"
                    >
                      <v-icon class="mr-2">mdi-account-plus </v-icon>
                      {{ $t('add_friend_action') }}
                    </v-btn>
                    <v-btn
                      color="primary-variant-1"
                      class="button-content--text"
                      :disabled="addBlockBtnDisableStatus"
                      @click="checkAddBlockEvent(currentChat.title)"
                    >
                      <v-icon class="mr-2">mdi-account-cancel</v-icon>
                      {{ $t('add_to_blacklist') }}
                    </v-btn>
                  </v-row>
                  <!-- remind text -->
                  <v-row
                    v-if="isOfficial(currentChat.title)"
                    no-gutters
                    justify="center"
                    align="center"
                    class="remind-bar px-4 py-2"
                  >
                    <i18n
                      path="star_treasure_reminder"
                      tag="span"
                      class="default-content--text text-body-1 custom-text-noto"
                    >
                      <template v-slot:other>
                        <span
                          class="primary--text text-decoration-underline cursor-pointer"
                          @click="goDownload"
                        >
                          {{ $t('other_star_city_online_platforms') }}
                        </span>
                      </template>
                    </i18n>
                  </v-row>
                </div>
                <!-- msg-box -->
                <!-- 如果該對話在msg中不存在，則需要顯示下面這個div去撐開高度 -->
                <div
                  v-if="isFriendChat && !msg.hasOwnProperty(currentFriendChat?.username)"
                  class="msg-box"
                  :style="{ 'background-color': bgColor }"
                >
                  <div
                    v-if="friendList.length === 0"
                    class="d-flex justify-center align-center fill-height"
                  >
                    <span class="text-body-2 grey-3--text coustom-text-noto">
                      {{ $t('no_friends_yet') }}
                    </span>
                  </div>
                </div>
                <div
                  v-else-if="!isFriendChat && !msg.hasOwnProperty(currentChat.key)"
                  class="msg-box"
                  :style="{ 'background-color': bgColor }"
                />
                <div
                  v-for="(value, key) in msg"
                  :key="key"
                  v-show="
                    isFriendChat ? key === currentFriendChat?.username : key === currentChat.key
                  "
                  :id="`msg-box-${key}`"
                  class="msg-box"
                  :style="{ 'background-color': bgColor }"
                  @scroll="handleScrollDebounced(`msg-box-${key}`)"
                  @click="updateReadNoty(`msg-box-${key}`)"
                >
                  <msgBox :msg-ary="value" :msg-key="key" @showImgDialog="showImgDialog" />
                </div>
                <!-- msg-bar -->
                <div>
                  <msgBar
                    ref="msgBar"
                    only-text
                    :no-target="isNoTarget"
                    :target-offline="isTargetOffline"
                    :is-customer="!isFriendChat && isOfficial(currentChat.title)"
                    @goCustomer="goCustomer"
                    @sendMessageEvent="sendMessageEvent"
                    @click="updateReadNoty(`msg-box-${key}`)"
                  />
                </div>
                <!-- scroll to bottom -->
                <v-slide-y-reverse-transition>
                  <v-btn
                    v-show="scrollTopStatus"
                    class="scroll-top-btn mx-2"
                    small
                    fab
                    dark
                    color="gradient-button"
                    @click="scrollToBottom"
                  >
                    <span class="material-symbols-outlined button-icon--text font-weight-medium">
                      arrow_downward
                    </span>
                  </v-btn>
                </v-slide-y-reverse-transition>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <changeChannelDialog
      :show-change-channel-dialog-status.sync="showChangeChannelDialogStatus"
      :channel-name="goingToChannel"
      @joinChannel="joinChannel"
    />
    <settingDialog
      v-if="showSettingDialogStatus"
      :show-setting-dialog-status.sync="showSettingDialogStatus"
    />
    <imgDialog :img-dialog-status.sync="imgDialog.show" :img="imgDialog.img" />
  </div>
</template>

<script>
  import orientation from '~/mixins/orientation'
  import relationship from '@/mixins/relationship.js'
  import hiddenScrollHtml from '~/mixins/hiddenScrollHtml'
  import chat from '~/mixins/chatroom/chat'
  import audioBuffer from '~/mixins/chatroom/audioBuffer'
  import scssLoader from '@/mixins/scssLoader.js'
  import debounce from 'lodash/debounce'
  import cloneDeep from 'lodash/cloneDeep'
  export default {
    name: 'chatRoomDialog',
    mixins: [orientation, chat, relationship, hiddenScrollHtml, audioBuffer, scssLoader],
    props: {
      showChatRoomDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      msgBar: () => import('~/components/msgBar'),
      chatList: () => import('~/components/chatList'),
      msgBox: () => import('~/components/msgBox'),
      changeChannelDialog: () => import('~/layouts/chatroom/changeChannelDialog'),
      msgTitle: () => import('~/layouts/chatroom/msgTitle'),
      settingDialog: () => import('~/layouts/chatroom/settingDialog'),
      imgDialog: () => import('~/components/imgDialog')
    },
    data() {
      return {
        tab: 0,
        selectedChat: 0,
        selectedFriendChat: '',
        selectedChannel: null,
        selectedChannelTmp: {
          oldVal: null,
          val: null,
          oldValIndex: null
        },
        goingToChannel: '',
        chatListKeyword: '',
        scrollTopStatus: false,
        showChatMobile: false,
        showChangeChannelDialogStatus: false,
        showSettingDialogStatus: false,
        cardTitle: {
          second: {
            icon: 'settings',
            action: this.showSettingsDialog
          }
        },
        updateUserOnlineTimer: null, // 定義 updateUserOnlineTimer 變數
        updateUserOnlineTimerStatus: false,
        initFraudNoty: false,
        bgColor: '',
        officialName: '官方-',
        handleScrollDebounced: null, // 定義防抖函數
        addBlockBtnDisableStatus: false,
        addFriendBtnDisableStatus: false,
        showChatRoomDialogStatusTemp: false,
        imgDialog: {
          show: false,
          img: ''
        }
      }
    },
    computed: {
      chatTabItems({ $store }) {
        let chatItem = $store.getters['chat/chatTabItems']
        return chatItem.filter((item) => this.isLogin || item.id !== 2)
      },
      friendList({ $store }) {
        function compareFriends(a, b) {
          // 依照 pin 值排序，true 在前
          if (a.chat.pin && !b.chat.pin) {
            return -1
          }
          if (!a.chat.pin && b.chat.pin) {
            return 1
          }

          // 依照 online 值排序，true 在前
          if (a.online && !b.online) {
            return -1
          }
          if (!a.online && b.online) {
            return 1
          }

          // 依照 date 值排序，最新的在前
          if (a.lastUpdate === null && b.lastUpdate !== null) {
            return 1
          }
          if (a.lastUpdate !== null && b.lastUpdate === null) {
            return -1
          }
          if (a.lastUpdate !== null && b.lastUpdate !== null) {
            if (a.lastUpdate > b.lastUpdate) {
              return -1
            }
            if (a.lastUpdate < b.lastUpdate) {
              return 1
            }
          }

          // 依照 username 值排序
          else {
            if (!isNaN(a.username) && !isNaN(b.username)) {
              return a.username - b.username
            } else if (!isNaN(a.username)) {
              return -1 // a 是数字，排在前面
            } else if (!isNaN(b.username)) {
              return 1 // b 是数字，排在前面
            } else if (/^\d/.test(a.username)) {
              // 使用正则表达式检查字符串是否以数字开头
              return -1
            } else {
              // 接下来比较英文
              const isStarWithEnglishA = /^[a-zA-Z]/.test(a.username)
              const isStarWithEnglishB = /^[a-zA-Z]/.test(b.username)

              if (isStarWithEnglishA && isStarWithEnglishB) {
                return a.username.localeCompare(b.username, 'en')
              } else if (isStarWithEnglishA && !isStarWithEnglishB) {
                return -1
              } else if (!isStarWithEnglishA && isStarWithEnglishB) {
                return 1
              } else {
                // 最后比较中文，按照中文第一个字的笔画排序
                return a.username.localeCompare(b.username, 'zh-Hant')
              }
            }
          }
        }

        let friendList = $store.getters['social/friendList']
        return friendList.map((item) => item).sort(compareFriends)
      },
      chats({ $store }) {
        let chats = $store.getters['chat/chats']

        return chats
          .map((item) => item)
          .sort((a, b) => {
            // 先按照 id 值排序
            if (a.id === 0) return -1 // a.id 为 0 的排在前面
            if (b.id === 0) return 1 // b.id 为 0 的排在前面
            if (a.id === 1) return -1 // a.id 为 1 的排在前面
            if (b.id === 1) return 1 // b.id 为 1 的排在前面

            // 依照 date 值排序，最新的在前
            if (a.lastUpdate > b.lastUpdate) {
              return -1
            }
            if (a.lastUpdate < b.lastUpdate) {
              return 1
            }
          })
      },
      channels({ $store }) {
        return $store.getters['chat/channels']
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      currentChat() {
        let index = this.chats.findIndex((item) => item.id === this.selectedChat)
        if (index !== -1) return this.chats[index]
        else return this.chats[0]
      },
      currentFriendChat() {
        if (this.selectedFriendChat === undefined) return null
        const index = this.friendList.findIndex((item) => item.username === this.selectedFriendChat)
        if (index !== -1) return this.friendList[index]
        else return null
      },
      currentMsgAry() {
        let key = ''
        if (!this.isFriendChat) {
          key = this.currentChat?.key
        } else if (this.isFriendChat) {
          if (this.currentFriendChat !== null) key = this.currentFriendChat?.username
          else return []
        }
        if (key !== undefined && key in this.msg) return this.msg[key]
        else return []
      },
      isStranger() {
        return !this.isFriendChat &&
          !this.currentChat?.isOfficial &&
          this.friendList.findIndex((item) => item.username === this.currentChat?.title) === -1
          ? true
          : false
      },
      isFriendChat() {
        return this.tab === 1
      },
      isTargetOffline() {
        let isOnline = false

        if (
          (this.isFriendChat && this.currentFriendChat === null) ||
          (!this.isFriendChat && this.currentChat === undefined)
        ) {
          return isOnline
        }

        if (this.isFriendChat) isOnline = this.currentFriendChat?.online
        else
          isOnline =
            this.currentChat?.id > 1 && !this.isOfficial(this.currentChat.title)
              ? this.currentChat?.online
              : true
        return !isOnline
      },
      isNoTarget() {
        return this.isFriendChat && this.$refs.chatList?.friendListFilter.length == 0
      },
      msg({ $store }) {
        return $store.getters['chat/msg']
      },
      //在watch物件使用deep的屬性 會導致所取得的newVal與oldVal都是同一個物件 故使用cloneDeep
      msgClone() {
        return cloneDeep(this.msg)
      },
      secondChatTitle() {
        return this.chats[1].title
      },
      currentChannelDetail({ $store }) {
        return $store.getters['chat/currentChannelDetail']
      },
      allChatNoty({ $store }) {
        return $store.getters['chat/allChatNoty']
      },
      ownName({ $store }) {
        return $store.getters['role/userName']
      },
      setting({ $store }) {
        return $store.getters['chat/setting']
      },
      level({ $store }) {
        return $store.getters['role/level']
      }
    },
    watch: {
      showChatRoomDialogStatus: {
        handler(val) {
          this.showChatRoomDialogStatusTemp = val
          if (val) {
            this.$nextTick(() => {
              //確保已渲染完畢
              setTimeout(() => {
                if (
                  this.$vuetify.breakpoint.smAndUp ||
                  (this.$vuetify.breakpoint.xsOnly && this.showChatMobile === true)
                ) {
                  if (!this.isFriendChat) {
                    if (this.currentChat)
                      this.checkShowScrollStatusAndUpdateReadNoty(`msg-box-${this.currentChat.key}`)
                  } else {
                    if (this.currentFriendChat)
                      this.checkShowScrollStatusAndUpdateReadNoty(
                        `msg-box-${this.currentFriendChat.username}`
                      )
                  }
                }
              }, 1000)
            })
          } else {
            // 取消定時器
            clearInterval(this.updateUserOnlineTimer)
            this.updateUserOnlineTimerStatus = false
            this.$store.commit('chat/SET_IS_KEYBOARD_OPEN', false)
            this.$nextTick(() => {
              this.$refs.msgBar.clearMsg()
              if (document) {
                document.body.removeAttribute('class', 'noscroll')
                document.documentElement.style.overflowY = 'scroll'
                document.body.scroll = 'yes'
              }
            })
          }
        }
      },
      secondChatTitle: {
        immediate: true,
        handler(val) {
          if (val && val.length > 0) {
            const obj = this.channels.find((item) => item.name === val)
            if (obj) {
              this.selectedChannel = obj
            }
          }
        }
      },
      channels: {
        immediate: true,
        handler(val) {
          if (val && val.length > 0 && this.selectedChannel === null) {
            const obj = val.find((item) => item.name === this.secondChatTitle)
            if (obj) {
              this.selectedChannel = obj
            }
          }
        }
      },
      'setting.opacity': {
        immediate: true,
        handler(val) {
          let hexColor = this.$vuetify.theme.defaults.dark['dialog-fill']
          hexColor = hexColor.replace('#', '')
          // 解析 R、G、B 分量
          let r = parseInt(hexColor.substring(0, 2), 16)
          let g = parseInt(hexColor.substring(2, 4), 16)
          let b = parseInt(hexColor.substring(4, 6), 16)

          this.bgColor = `rgba(${r}, ${g}, ${b}, ${val})`
        }
      },
      'setting.whisper.onlyFriend': {
        immediate: true,
        handler() {
          this.$store.dispatch('chat/countAllChatNoty')
          this.$store.dispatch('chat/countWhisperNoty')
        }
      },
      blockList: {
        handler(val) {
          this.chats.forEach((chat, index) =>
            val.findIndex((blocker) => blocker.username === chat.key) !== -1
              ? this.$store.commit('chat/SET_CHAT_IS_BLOCK', {
                  key: this.chats[index].key,
                  payload: true
                })
              : this.$store.commit('chat/SET_CHAT_IS_BLOCK', {
                  key: this.chats[index].key,
                  payload: false
                })
          )
          this.$store.dispatch('chat/countAllChatNoty')
          this.$store.dispatch('chat/countWhisperNoty')
        }
      },
      friendList: {
        handler(val) {
          this.chats.forEach((chat, index) =>
            val.findIndex((friend) => friend.username === chat.key) !== -1
              ? this.$store.commit('chat/SET_CHAT_IS_FRIEND', {
                  key: this.chats[index].key,
                  payload: true
                })
              : this.$store.commit('chat/SET_CHAT_IS_FRIEND', {
                  key: this.chats[index].key,
                  payload: false
                })
          )
          this.$nextTick(() => {
            this.countAllIsNotReadMsg()
          })
        }
      },
      msgClone: {
        async handler(newVal, oldVal) {
          //在xs的情況下且不是顯示聊天室的情況下不自動滾動
          if (this.$vuetify.breakpoint.xsOnly && !this.showChatMobile) {
            return
          }

          const self = this
          let key = null
          const autoScroll = async function () {
            //確保元素已經被渲染
            const scrollToBottomDelay = (id) =>
              new Promise((resolve) => {
                setTimeout(async () => {
                  await self.scrollToBottom(id)
                  resolve()
                }, 50)
              })

            const id = self.getCurrentMsgBoxID()
            const isNotScrollToBottom = self.isNotScrollToBottom(id)
            if (!isNotScrollToBottom) {
              if (self.isFriendChat) {
                if (self.currentFriendChat !== null) {
                  const id = `msg-box-${self.currentFriendChat.username}`
                  await scrollToBottomDelay(id)
                }
              } else {
                const id = `msg-box-${self.currentChat.key}`
                await scrollToBottomDelay(id)
              }
            }
            self.scrollTopStatus = isNotScrollToBottom
          }
          if (this.isFriendChat) {
            if (this.currentFriendChat === null) return
            else key = this.currentFriendChat.username
          } else {
            key = this.currentChat.key
          }

          const isFirstMsg = !(key in oldVal) && key in newVal
          const isInMsg = key in oldVal && key in newVal
          const isAddMsg = oldVal[key]?.length < newVal[key]?.length
          const isLastMsgDiff =
            oldVal[key]?.[oldVal[key]?.length - 1]?.id !==
            newVal[key]?.[newVal[key]?.length - 1]?.id
          if (
            this.showChatRoomDialogStatusTemp &&
            (isFirstMsg || (isInMsg && (isAddMsg || isLastMsgDiff)))
          )
            autoScroll()
        },
        deep: true
      },
      'currentChannelDetail.name': {
        handler(val) {
          this.joinChannelSuccess({ name: val })
        }
      }
    },
    created() {
      this.selectedChat = 0
      this.selectedFriendChat = this.friendList[0]?.username
      this.$nuxt.$on('chat:createMessage', this.createMessage)
      this.handleScrollDebounced = debounce(this.checkShowScrollStatusAndUpdateReadNoty, 500) // 使用 debounce 创建防抖函数
    },
    mounted() {
      this.setSettingFromLocalStorage()
    },
    beforeDestroy() {
      clearInterval(this.updateUserOnlineTimer) // 取消定時器
      this.$nuxt.$off('chat:createMessage', this.createMessage)
      this.$store.commit('chat/SET_IS_KEYBOARD_OPEN', false)
    },
    methods: {
      closeDialog() {
        this.$nuxt.$emit('root:showChatRoomDialogStatus', false)
      },
      goCustomer() {
        this.$nuxt.$emit('root:showCustomerServiceDialogStatusEvent', true)
      },
      showSettingsDialog() {
        this.showSettingDialogStatus = true
      },
      selectedChatEvent(id) {
        const self = this
        this.closeHeatList()
        clearInterval(this.updateUserOnlineTimer) // 取消定時器
        this.$refs.msgBar.clearMsg()

        this.$nextTick(() => {
          if (this.updateUserOnlineTimerStatus === false) {
            const isFriend = this.checkIsFriend(this.currentChat.title)
            //非官方頻道與非官方帳號
            if (id > 1 && !isFriend && !this.currentChat.key.startsWith(this.officialName)) {
              this.updateUserOnlineTimer = setInterval(async () => {
                this.updateUserOnlineTimerStatus = true
                if (!this.isFriendChat && !this.checkIsFriend(this.currentChat.title)) {
                  const role = await this.$store.dispatch(
                    'social/getUserDetail',
                    self.currentChat.key
                  )
                  self.$store.commit('chat/SET_CHAT_ONLINE_STATUS_BY_ID', {
                    id: id,
                    online: role.online
                  })
                }
              }, 1000)
            }
          }
          //手機板透過 mobileClickedEvent 去進行檢查，手機板只有在 mobileClickedEvent 才會真正進到聊天室頁面
          if (this.$vuetify.breakpoint.smAndUp) {
            //確保渲染完畢
            setTimeout(() => {
              this.checkShowScrollStatusAndUpdateReadNoty(`msg-box-${this.currentChat.key}`)
            }, 100)
          }
        })
      },
      selectedFriendChatEvent() {
        this.closeHeatList()
        this.$refs.msgBar.clearMsg()

        if (this.currentFriendChat !== null)
          this.$nextTick(() => {
            //手機板透過 mobileClickedEvent 去進行檢查，手機板只有在 mobileClickedEvent 才會真正進到聊天室頁面
            if (this.$vuetify.breakpoint.smAndUp) {
              //確保渲染完畢
              setTimeout(() => {
                this.checkShowScrollStatusAndUpdateReadNoty(
                  `msg-box-${this.currentFriendChat.username}`
                )
              }, 100)
            }
          })
      },
      mobileClickedEvent() {
        this.showChatMobile = true

        this.$nextTick(() => {
          //確保渲染完畢
          setTimeout(() => {
            if (!this.isFriendChat) {
              if (this.currentChat !== null)
                this.checkShowScrollStatusAndUpdateReadNoty(`msg-box-${this.currentChat.key}`)
            } else {
              if (this.currentFriendChat !== null)
                this.checkShowScrollStatusAndUpdateReadNoty(
                  `msg-box-${this.currentFriendChat.username}`
                )
            }
          }, 100)
        })
      },
      clearNoty() {
        if (!this.isFriendChat && this.currentChat !== undefined)
          this.clearChatNoty(this.currentChat.id)
        else if (this.isFriendChat && this.currentFriendChat !== null)
          this.clearFriendNoty(this.currentFriendChat.username)
      },
      clearChatNoty(id) {
        const self = this
        const chatsByStore = this.$store.getters['chat/chats']
        const index = chatsByStore.findIndex((item) => item.id === id)
        if (index !== -1) {
          const key = chatsByStore[index].key
          if (key in this.msg) {
            this.msg[key].forEach((item) => self.setIsRead({ id: item.id, status: true }))
          }
          this.$store.commit('chat/SET_NOTY', { index: index, payload: 0 })
          this.$store.dispatch('chat/countAllChatNoty')
        }
      },
      clearFriendNoty(name) {
        const self = this
        const index = this.friendList.findIndex((item) => item.username === name)
        if (index !== -1) {
          const key = this.friendList[index].username
          if (key in this.msg) {
            this.msg[key].forEach((item) => self.setIsRead({ id: item.id, status: true }))
          }
          this.$store.commit('social/SET_FRIEND_NOTY', { username: name, noty: 0 })
          this.$store.dispatch('chat/countAllChatNoty')
        }
      },
      async sendMessageEvent(message) {
        await this.sendMessage(message)
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToBottom()
          }, 100)
        })
      },
      async sendMessage(message) {
        try {
          const systemCond = (data) =>
            data.type === 0 &&
            data.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.SYSTEM_MESSAGE.ID
          const banSpeaking = (data) =>
            data.type === 103 && data.isFeature(this.$xinConfig.FEATURE.BAN_SPEAKING.ID)
          const channelBanSpeaking = (data) =>
            data.type === 17 && data.messageType === 1 && data.content.message.includes('禁止發言')

          const sendAndNotify = async (sendPacket) => {
            this.$wsClient.send(sendPacket)
            const res = await this.$xinUtility.waitEvent(
              this.$wsClient.receivedListeners,
              (data) => {
                return (
                  (data.type === 17 &&
                    (data.user.name === this.ownName ||
                      data.content.allMessage.includes('密給'))) ||
                  channelBanSpeaking(data) ||
                  data.isFeature(this.$xinConfig.LOGIN_SERVICE.TYPE.SYSTEM_MESSAGE.ID) ||
                  data.isFeature(this.$xinConfig.FEATURE.BAN_SPEAKING.ID)
                )
              }
            )
            if (systemCond(res)) this.$notify.warning(this.removeDash(res.message))
            else if (banSpeaking(res))
              this.$notify.warning(`${this.$t('you_are_muted_until')}${res.expiryDate}(GMT+8)`)
            else if (channelBanSpeaking(res))
              this.$notify.warning(`${this.$t('you_are_muted_in_this_channel')}`)
          }

          if (!this.isFriendChat) {
            if (this.currentChat.id === 0) {
              await sendAndNotify(this.$wsPacketFactory.sendMessageToAll({ message: message }))
            } else if (this.currentChat.id === 1) {
              await sendAndNotify(this.$wsPacketFactory.sendMessage({ message: message }))
            } else {
              await sendAndNotify(
                this.$wsPacketFactory.sendSecretlyMessage({
                  username: this.currentChat.title,
                  message: message
                })
              )
            }
          } else if (this.currentFriendChat !== null) {
            await sendAndNotify(
              this.$wsPacketFactory.sendSecretlyMessage({
                username: this.currentFriendChat.username,
                message: message
              })
            )
          }
        } catch (err) {
          console.log(err)
        }
      },
      async createMessage(username) {
        const self = this
        const role = await this.$store.dispatch('social/getUserDetail', username)

        if (!role.online) {
          this.$notify.warning(this.$t('player_offline', { player: role.username }))
        } else {
          processRole(username, role)
        }

        function processRole(username, role) {
          const index = self.getFriendIndex(username)

          if (index !== -1) {
            handleFriendRole(username, role)
          } else if (!self.setting.whisper.onlyFriend) {
            handleNonFriendRole(username, role)
          } else {
            self.showNotyDialog(self.$t('reminder'), self.$t('player_not_friend'))
          }
        }

        function handleFriendRole(username, role) {
          checkAndOpenChat(username, role, true)
        }

        function handleNonFriendRole(username, role) {
          checkAndOpenChat(username, role, false)
        }

        async function checkAndOpenChat(username, role, isFriend) {
          self.checkFirstWispher(username)
          const inChatsIndex = self.chats.findIndex((item) => item.title === username)

          if (inChatsIndex === -1) {
            const img = isFriend
              ? self.friendList[self.getFriendIndex(username)].thumbUrl
              : await self.$store.dispatch('role/getThumbUrl', username)

            const chat = {
              title: username,
              id: self.chats.length,
              img: img,
              noty: 0,
              isOfficial: false,
              key: username,
              online: true,
              isBlock: false,
              isFriend: isFriend,
              lastUpdate: new Date()
            }

            self.$store.commit('chat/ADD_CHATS', chat)
            self.selectedChat = chat.id
          } else {
            self.selectedChat = self.chats[inChatsIndex].id
          }

          self.tab = 0
          self.selectedChatEvent(self.selectedChat)
          if (self.$vuetify.breakpoint.xsOnly) self.showChatMobile = true
        }
      },
      async checkAddFriendEvent(name) {
        this.addFriendBtnDisableStatus = true
        await this.checkAddFriend(name)
        this.addFriendBtnDisableStatus = false
      },
      async checkAddBlockEvent(name) {
        this.addBlockBtnDisableStatus = true
        await this.checkAddBlock(name)
        this.addBlockBtnDisableStatus = false
        if (this.$device.isMobile) this.showChatMobile = false
      },
      async joinChannel() {
        const self = this
        let oldVal = this.selectedChannelTmp.oldVal
        let val = this.selectedChannelTmp.val
        this.$nuxt.$loading.start()

        //requset
        const res = await this.joinChannelRequset(val.name)

        //success
        if (res.commandId === 12) {
          this.selectedChannel = val
          this.scrollToBottom()
          this.$nuxt.$loading.finish()
        }
        //fail
        else if (res.type === 0 && res.commandId === 102) {
          this.joinChannelFail(res)
          //避免channel在等待資料回傳當中變動，所以再一次搜尋index
          this.selectedChannelTmp.oldValIndex = this.channels.findIndex(
            (item) => item.name === oldVal
          )
          if (this.selectedChannelTmp.oldValIndex !== -1) {
            this.$nextTick(() => {
              self.selectedChannel = JSON.parse(
                JSON.stringify(self.channels[this.selectedChannelTmp.oldValIndex])
              )
            })
          }
          this.$nuxt.$loading.finish()
        }
      },
      async joinChannelRequset(channel) {
        this.$wsClient.send(this.$wsPacketFactory.joinChannel(channel))
        //成功 傳setCurrentChannelDetail
        const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          return (
            data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.INFO) ||
            data.isFeature(this.$xinConfig.LOGIN_SERVICE.TYPE.SYSTEM_MESSAGE.ID)
          )
        })
        return res
      },
      joinChannelSuccess(res) {
        this.$refs.msgBar.clearMsg()
        const msg = this.$t('joined') + res.name
        const data = {
          messageType: 1,
          user: {
            name: 'system',
            color: {
              red: 255,
              green: 255,
              blue: 255
            }
          },
          content: {
            message: msg,
            type: 3,
            color: {
              red: 255,
              green: 255,
              blue: 255
            }
          }
        }
        this.$store.dispatch('chat/addChannelChat', { payload: data }, { root: true })
      },
      joinChannelFail(res) {
        this.$notify.warning(this.removeDash(res.message))
      },
      async scrollToBottom() {
        this.clearNoty()
        let elementID = ''
        if (this.isFriendChat) elementID = `msg-box-${this.currentFriendChat.username}`
        else elementID = `msg-box-${this.currentChat.key}`
        const element = document.getElementById(elementID)
        if (element)
          await this.$nextTick(() => {
            element.scrollTo({ top: element.scrollHeight, behavior: 'auto' })
          })
      },
      getCurrentMsgBoxID() {
        let elementID = ''
        if (this.isFriendChat && this.currentFriendChat !== null)
          elementID = `msg-box-${this.currentFriendChat.username}`
        else elementID = `msg-box-${this.currentChat.key}`
        return elementID
      },
      checkShowScrollStatusAndUpdateReadNoty(id) {
        this.checkShowScrollStatus(id)
        this.updateReadNoty(id)
      },
      checkShowScrollStatus(id) {
        const self = this

        this.$nextTick(() => {
          const element = document.getElementById(id)
          if (element) self.scrollTopStatus = this.isNotScrollToBottom(id)
          else self.scrollTopStatus = false
        })
      },
      checkShowScrollStatusWithId() {
        const id = this.getCurrentMsgBoxID()
        if (id.length !== 0) this.checkShowScrollStatus(id)
      },
      isNotScrollToBottom(id) {
        const element = document.getElementById(id)
        // 找不到該元素，視為不在底部
        if (!element) {
          this.scrollTopStatus = false
          return
        }
        const scrollTop = element.scrollTop
        const scrollHeight = element.scrollHeight
        const clientHeight = element.clientHeight

        //手機dpi存在誤差，故使用容許誤差的方式
        // 计算允许的最大误差和最小误差
        const maxTolerance = 1
        const minTolerance = -1

        //卷軸位置是否在底部
        return (
          scrollHeight > clientHeight &&
          !(
            scrollHeight - (scrollTop + clientHeight) >= minTolerance &&
            scrollHeight - (scrollTop + clientHeight) <= maxTolerance
          )
        )
      },
      async searchPlayer() {
        if (this.chatListKeyword.length === 0) return

        const isWhitespace = (str) => /^\s*$/.test(str)

        if (isWhitespace(this.chatListKeyword)) {
          this.$notify.warning(this.$t('nickname_cannot_contain_whitespace'))
        }
        //是不是自己
        else if (this.chatListKeyword === this.ownName)
          this.$notify.warning(this.$t('cannot_operate_on_self'))
        else if (this.checkIsBlock(this.chatListKeyword))
          this.$notify.warning(this.$t('cannot_whisper_to_blacklist'))
        else {
          this.createMessage(this.chatListKeyword)
        }
        this.chatListKeyword = ''
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      getFriendIndex(userName) {
        let index = this.friendList.findIndex((item) => item.username === userName)
        return index
      },
      checkFirstWispher(username) {
        if (!this.initFraudNoty) {
          this.showNotyDialog(
            this.$t('reminder'),
            this.$t('official_reminder', { player: username })
          )
          this.initFraudNoty = true
        }
      },
      setIsRead({ id, status }) {
        if (this.isFriendChat && this.currentFriendChat !== null) {
          this.$store.commit('chat/SET_IS_REAED', {
            key: this.currentFriendChat.username,
            status: status,
            id: id
          })
        } else if (!this.isFriendChat && this.currentChat !== undefined) {
          this.$store.commit('chat/SET_IS_REAED', {
            key: this.currentChat.key,
            status: status,
            id: id
          })
        }
      },
      removeDash: (str) => (str.startsWith('-') ? str.slice(1) : str),
      updateReadNoty(id) {
        const container = document.getElementById(id)
        if (!container) {
          return
        }

        const elements = container.children[0]?.children
        if (elements) {
          let lastVisibleElement = null

          for (const element of elements) {
            const elementTop = element.getBoundingClientRect().top
            const elementBottom = elementTop + element.clientHeight
            const containerTop = container.getBoundingClientRect().top
            const containerBottom = containerTop + container.clientHeight

            if (elementTop >= containerTop && elementBottom <= containerBottom) {
              lastVisibleElement = element
            }
          }
          if (lastVisibleElement && lastVisibleElement?.id) {
            const numericPart = lastVisibleElement.id.match(/\d+/)[0]

            const messageList = this.isFriendChat
              ? this.msg[this.currentFriendChat.username] || []
              : this.msg[this.currentChat.key] || []
            messageList.forEach((item) => {
              if (!item.isRead && item.id <= parseInt(numericPart)) {
                this.setIsRead({ id: item.id, status: true })
              }
            })
            this.countAllIsNotReadMsg()
          }
        }
      },
      countAllIsNotReadMsg() {
        for (const key in this.msg) {
          const element = this.msg[key]
          const noty = element.filter((item) => !item.isRead).length

          const chatsByStore = this.$store.getters['chat/chats']
          const chatIndex = chatsByStore.findIndex((item) => item.key === key)
          const friendIndex = this.friendList.findIndex((item) => item.username === key)

          if (chatIndex !== -1) {
            this.$store.commit('chat/SET_NOTY', { index: chatIndex, payload: noty })
          }

          if (friendIndex !== -1) {
            this.$store.commit('social/SET_FRIEND_NOTY', { username: key, noty: noty })
          }
        }
        this.$store.dispatch('chat/countAllChatNoty')
        this.$store.dispatch('chat/countWhisperNoty')
      },
      goDownload() {
        this.closeDialog()
        if (this.$device.isAndroid || this.$device.isIos || this.$device.isMacOS) {
          const url = 'https://www.xin-stars.com/goStore'
          window.open(url)
        } else {
          this.$router.push({ path: '/downloads', hash: '#pc' })
        }
      },
      selectTabEvent() {
        this.closeHeatList()
        this.$refs.msgBar.clearMsg()

        if (
          this.$vuetify.breakpoint.smAndUp ||
          (this.$vuetify.breakpoint.xsOnly && this.showChatMobile === true)
        ) {
          this.$nextTick(() => {
            setTimeout(() => {
              const id = this.getCurrentMsgBoxID()
              this.checkShowScrollStatusAndUpdateReadNoty(id)
            }, 1000)
          })
        }
      },
      closeHeatList() {
        const msgTitle = this.$refs?.msgTitle
        if (msgTitle) msgTitle.closeHeatList()
      },
      showImgDialog(img) {
        if (img.includes('chat_picture_default.webp')) return
        this.imgDialog.show = true
        this.imgDialog.img = img
      },
      selectFirstChat() {
        if (!this.isFriendChat) {
          this.selectedChat = 0
          this.selectedChatEvent(0)
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  //color
  $dialog-fill: map-get($colors, 'dialog-fill');
  $grey-5: map-get($colors, 'grey-5');
  $dialog-fill-with-opacity: rgba($dialog-fill, 0.6);
  $primary: map-get($colors, 'primary');

  //height
  $chatroom-dialog-height: 560px;
  $topbar-height: 48px;
  $text-field-height: 56px;
  $text-left-height: $chatroom-dialog-height - 54px;

  @media screen and (min-width: 960px), screen and (min-height: 600px) and (orientation: portrait) {
    .chatroom-dialog {
      height: $chatroom-dialog-height;
      .chatroom {
        height: $text-left-height;
        .left-side {
          .left-side-tab {
            height: $topbar-height;
          }
          .left-side-content {
            height: calc(100% - #{$topbar-height});
            .left-side-content-textfield {
              height: $text-field-height;
            }
            .left-side-content-list {
              flex: 1;
              overflow: auto;
            }
          }
        }

        .chat-container {
          position: relative;

          .action-bar {
            background-color: $dialog-fill-with-opacity;
            height: 52px;
            border-bottom: 1px solid $primary;
          }
          .remind-bar {
            background-color: $dialog-fill-with-opacity;
            height: 64px;
            border-bottom: 1px solid $primary;
          }
          .msg-box {
            flex: 1;
            overflow: auto;
          }
          .scroll-top-btn {
            position: absolute;
            right: 8px;
            bottom: 62px;
          }
        }

        .theme--dark.v-badge .v-badge__badge::after {
          border-color: $dialog-fill;
        }
      }
    }
  }
  //手機直向
  @media screen and (max-width: 600px) {
    .chatroom-dialog {
      height: 100%;
      .chatroom {
        height: calc(100% - 54px);
        .left-side {
          .left-side-tab {
            height: $topbar-height;
          }
          .left-side-content {
            height: calc(100% - #{$topbar-height});
            .left-side-content-textfield {
              height: $text-field-height;
            }
            .left-side-content-list {
              flex: 1;
              overflow: auto;
            }
          }
        }

        .chat-container {
          position: relative;

          .action-bar {
            background-color: $dialog-fill-with-opacity;
            height: 52px;
            border-bottom: 1px solid $primary;
          }
          .remind-bar {
            background-color: $dialog-fill-with-opacity;
            height: 64px;
            border-bottom: 1px solid $primary;
          }
          .msg-box {
            flex: 1;
            overflow: auto;
          }
          .scroll-top-btn {
            position: absolute;
            right: 8px;
            bottom: 62px;
          }
        }

        .theme--dark.v-badge .v-badge__badge::after {
          border-color: $dialog-fill;
        }
      }
    }
  }
  //手機橫向
  @media screen and (max-width: 960px) and (orientation: landscape) {
    .chatroom-dialog {
      height: 100%;
      .chatroom {
        height: calc(100% - 54px);
        .left-side {
          .left-side-tab {
            height: $topbar-height;
          }
          .left-side-content {
            height: calc(100% - #{$topbar-height});
            .left-side-content-textfield {
              height: $text-field-height;
            }
            .left-side-content-list {
              flex: 1;
              overflow: auto;
            }
          }
        }

        .chat-container {
          position: relative;

          .action-bar {
            background-color: $dialog-fill-with-opacity;
            height: 52px;
            border-bottom: 1px solid $primary;
          }
          .remind-bar {
            background-color: $dialog-fill-with-opacity;
            height: 64px;
            border-bottom: 1px solid $primary;
          }
          .msg-box {
            flex: 1;
            overflow: auto;
          }
          .scroll-top-btn {
            position: absolute;
            right: 8px;
            bottom: 62px;
          }
        }

        .theme--dark.v-badge .v-badge__badge::after {
          border-color: $dialog-fill;
        }
      }
    }
  }
  .topbar-height {
    height: $topbar-height;
  }
</style>
