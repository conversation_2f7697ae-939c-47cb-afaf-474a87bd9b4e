<!-- 有頻道的版本 -->
<template>
  <v-row
    no-gutters
    align="center"
    :class="['topbar-height', 'pl-4', 'w-100', { 'flex-nowrap': $vuetify.breakpoint.xsOnly }]"
  >
    <!-- back icon -->
    <template v-if="$vuetify.breakpoint.xsOnly">
      <span
        class="material-symbols-outlined primary--text mr-2"
        @click="updateShowChatMobile(false)"
      >
        navigate_before
      </span>
    </template>
    <!-- chat -->
    <template v-if="!isFriendChat">
      <!-- global chat -->
      <template
        v-if="currentChat?.title === 'global_chat' || currentChat?.title === 'official_activity'"
      >
        <v-row no-gutters align="center">
          <div>
            <v-img
              v-if="currentChat?.title === 'global_chat'"
              width="40"
              height="40"
              :src="require('~/assets/image/chatroom/' + currentChat.img)"
            />
            <v-img
              v-else
              width="40"
              height="40"
              :src="require('~/assets/image/xin/photo_stickers/' + currentChat.img)"
            />
          </div>
          <div class="pl-4">
            <span class="text-subtitle-2 custom-text-noto default-content--text">
              {{ $t(currentChat.title) }}
            </span>
          </div>
        </v-row>
      </template>
      <!-- channel -->
      <template v-else-if="currentChat?.id === 1">
        <v-row
          v-if="selectedChannelComputed !== null"
          no-gutters
          align="center"
          :class="[
            'pr-4',
            'py-1',
            {
              'w-100': $vuetify.breakpoint.smAndUp,
              'left-side-width-mobile': $vuetify.breakpoint.xsOnly
            }
          ]"
        >
          <v-col xl="9" lg="9" md="9" sm="9" cols="8">
            <v-select
              dense
              outlined
              rounded
              hide-details
              :items="channels"
              v-model="selectedChannelComputed"
              item-text="name"
              return-object
              menu-props="offset-y"
            >
              <template v-slot:item="data">
                <v-row
                  no-gutters
                  align="center"
                  justify="space-between"
                  class="py-2"
                  :class="{ 'channel-list-mobile': $vuetify.breakpoint.xsOnly }"
                >
                  <v-row no-gutters align="center">
                    <div>
                      <v-img
                        width="40"
                        height="40"
                        :src="data.item.thumbUrl"
                        @error="errorCustomImgHandler(data.item)"
                      >
                        <template v-slot:placeholder> <placeHolder /></template>
                      </v-img>
                    </div>
                    <span class="text-subtitle-1 custom-text-noto ml-4">
                      {{ data.item.name }}
                    </span>
                  </v-row>
                  <v-row no-gutters align="center" justify="end">
                    <span class="custom-text-noto">
                      {{ data.item.population }}
                    </span>
                    <v-icon
                      v-if="data.item.population !== 0 && data.item.popIsUp"
                      color="warning"
                      class="ml-4"
                    >
                      mdi-account-arrow-up
                    </v-icon>
                    <v-icon v-else color="success" class="ml-4"> mdi-account-arrow-down </v-icon>
                  </v-row>
                </v-row>
              </template>
              <template v-slot:selection="data">
                <v-row no-gutters align="center" class="flex-nowrap w-100">
                  <div>
                    <v-img
                      width="24"
                      height="24"
                      :src="data.item.thumbUrl"
                      @error="errorCustomImgHandler(data.item)"
                    >
                      <template v-slot:placeholder> <placeHolder /></template>
                    </v-img>
                  </div>
                  <span class="text-subtitle-1 custom-text-noto text-overflow ml-4">
                    {{ data.item.name }}
                  </span>
                </v-row>
              </template>
            </v-select>
          </v-col>
          <v-col xl="3" lg="3" md="3" sm="3" cols="4" class="d-flex justify-end">
            <v-menu
              left
              offset-y
              :open-on-click="false"
              :close-on-content-click="false"
              v-model="heatListStatus"
              :min-width="$vuetify.breakpoint.xsOnly ? 400 : 369"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-chip
                  class="ml-2"
                  color="primary-variant-1"
                  :outlined="!heatListStatus"
                  v-bind="attrs"
                  v-on="on"
                  @click="showHeatList"
                >
                  <v-icon left> mdi-fire </v-icon>
                  {{ $t('heat') + selectedChannel.population }}
                </v-chip>
              </template>
              <div
                v-if="heatListStatus && currentChannelDetail?.users"
                class="heat-list dialog-fill"
              >
                <heatList :heat-list="currentChannelDetail.users" />
              </div>
            </v-menu>
          </v-col>
        </v-row>
      </template>
      <template v-else>
        <v-row no-gutters align="center">
          <div>
            <v-img width="40" height="40" :src="currentChat.img" />
          </div>
          <div class="pl-4">
            <span class="text-subtitle-2 custom-text-noto default-content--text">
              {{ currentChat.title }}
            </span>
          </div>
        </v-row>
      </template>
    </template>
    <!-- friend chat -->
    <template v-else-if="isFriendChat">
      <v-row no-gutters align="center">
        <!-- icon -->
        <div>
          <v-img width="32" height="32" :src="currentFriendChat?.thumbUrl">
            <template v-slot:placeholder>
              <placeHolder v-if="currentFriendChat !== null"
            /></template>
          </v-img>
        </div>
        <!-- title -->
        <div class="pl-4">
          <span class="text-subtitle-1 default-content--text custom-text-noto">
            {{ currentFriendChat?.username }}
          </span>
        </div>
      </v-row>
    </template>
  </v-row>
</template>
<script>
  import cloneDeep from 'lodash/cloneDeep'

  export default {
    name: 'msgTitle',
    props: {
      currentChat: {
        type: Object,
        default: () => {}
      },
      currentFriendChat: {
        type: Object,
        default: () => {}
      },
      isFriendChat: {
        type: Boolean,
        default: false
      },
      channels: {
        type: Array,
        default: () => []
      },
      selectedChannel: {
        type: Object,
        default: () => {}
      },
      currentChannelDetail: {
        type: Object,
        default: {
          name: '',
          users: []
        }
      },
      selectedChannelTmpVal: null,
      selectedChannelTmpOldVal: null,
      selectedChannelTmpOldValIndex: null,
      showChatMobile: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        heatListStatus: false
      }
    },
    components: {
      placeHolder: () => import('~/components/imgPlaceholder'),
      heatList: () => import('~/components/heatList')
    },
    computed: {
      selectedChannelComputed: {
        get() {
          return this.selectedChannel
        },
        set(val) {
          if (val) {
            const selectedChannelTmpVal = val
            const selectedChannelTmpOldVal = this.selectedChannel.name
            let selectedChannelTmpOldValIndex = -1
            this.$emit('update:selectedChannelTmpVal', selectedChannelTmpVal)
            this.$emit('update:selectedChannelTmpOldVal', selectedChannelTmpOldVal)
            this.$emit('update:goingToChannel', selectedChannelTmpVal.name)

            //html select在點選的時候就會自動選擇點選的值，所以這邊要先把值改回來，後續的值由成功或失敗來決定是否切換
            selectedChannelTmpOldValIndex = this.channels.findIndex(
              (item) => item.name === selectedChannelTmpOldVal
            )
            this.$emit('update:selectedChannelTmpOldValIndex', selectedChannelTmpOldValIndex)

            this.$emit(
              'update:selectedChannel',
              cloneDeep(this.channels[selectedChannelTmpOldValIndex])
            )
            this.$emit('update:showChangeChannelDialogStatus', true)
          }
        }
      }
    },
    methods: {
      updateShowChatMobile(val) {
        this.$emit('update:showChatMobile', val)
      },
      showHeatList() {
        const newbieText = '新人專區'
        const infoText = this.$t('cannot_view_heat_in_this_channel')
        if (this.selectedChannelComputed.name.includes(newbieText)) {
          this.$notify.info(infoText)
        } else {
          this.heatListStatus = !this.heatListStatus
        }
      },
      closeHeatList() {
        this.heatListStatus = false
      },
      errorCustomImgHandler(item) {
        this.$store.commit('chat/SET_CHANNEL_IMG', {
          name: item.name,
          thumbUrl: this.defaultImg
        })
      }
    }
  }
</script>
<style scoped lang="scss">
  .left-side-width-mobile {
    width: calc(100% - 32px);
  }
  .heat-list {
    height: 448px;
    width: 369px;
  }

  .channel-list-mobile {
    width: 311px;
  }
</style>
