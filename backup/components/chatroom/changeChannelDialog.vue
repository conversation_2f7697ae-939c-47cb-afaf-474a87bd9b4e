<template>
  <!-- eslint-disable vue/no-v-html -->
  <v-dialog v-model="showChangeChannelDialogStatus" persistent max-width="380">
    <v-card class="dialog-fill">
      <v-card-title class="custom-text-noto text-h6 grey-1--text justify-center py-6">
        {{ $t('reminder') }}
      </v-card-title>
      <v-card-text class="custom-text-noto text-body-2 default-content--text pb-6">
        <i18n path="enter_channel" tag="span">
          <template v-slot:channel>
            <span class="primary--text">{{ channelName }}</span>
          </template>
        </i18n>
      </v-card-text>
      <v-card-actions class="pt-0 pb-6 px-6">
        <v-spacer />
        <v-btn
          text
          elevation="0"
          color="primary-variant-1"
          class="button-text default-content--text"
          @click="closeDialog"
        >
          {{ $t('cancel').toUpperCase() }}
        </v-btn>
        <v-btn
          elevation="0"
          color="primary-variant-1"
          class="button-content--text"
          @click="emitJoinChannelEvent"
        >
          {{ $t('sure').toUpperCase() }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
  export default {
    name: 'changeChannelDialog',
    props: {
      showChangeChannelDialogStatus: { type: Boolean, default: false },
      channelName: { type: String, default: '' }
    },
    data() {
      return {}
    },
    methods: {
      closeDialog() {
        this.$emit('update:showChangeChannelDialogStatus', false)
      },
      emitJoinChannelEvent() {
        this.$emit('joinChannel')
        this.closeDialog()
      }
    }
  }
</script>
<style></style>
