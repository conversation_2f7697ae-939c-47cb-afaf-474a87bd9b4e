<template>
  <v-list>
    <v-list-item v-for="player in heatListTmp" :key="player.userName">
      <v-list-item-content>
        <!-- other player -->
        <v-menu right v-if="!isMe(player.username)">
          <template v-slot:activator="{ on, attrs }">
            <v-row
              no-gutters
              align="center"
              v-bind="attrs"
              v-on="on"
              @click="setPlayerInfo(player.username)"
            >
              <!-- icon -->
              <div v-if="checkIsFriend(player.username)">
                <v-badge bordered bottom overlap color="success">
                  <template v-slot:badge>
                    <v-icon size="12"> mdi-account-multiple </v-icon>
                  </template>
                  <v-img
                    width="40"
                    height="40"
                    :src="player.thumbUrl"
                    @error="errorImgHandler(player)"
                  >
                    <template v-slot:placeholder> <placeHolder /></template>
                  </v-img>
                </v-badge>
              </div>
              <div v-else-if="checkIsBlock(player.username)">
                <v-badge bordered bottom overlap color="black">
                  <template v-slot:badge>
                    <v-icon size="12"> mdi-cancel </v-icon>
                  </template>
                  <v-img width="40" height="40" :src="player.thumbUrl">
                    <template v-slot:placeholder> <placeHolder /></template>
                  </v-img>
                </v-badge>
              </div>
              <div v-else>
                <v-img
                  width="40"
                  height="40"
                  :src="player.thumbUrl"
                  @error="errorImgHandler(player)"
                >
                  <template v-slot:placeholder> <placeHolder /></template>
                </v-img>
              </div>
              <!-- vip & title -->
              <div class="pl-4">
                <v-row no-gutters>
                  <vipLevelIcon :vip-level="player.vipLevel" width="28" height="28" class="mr-2" />
                  <span class="text-subtitle-1 default-content--text custom-text-noto">
                    {{ player.username }}
                  </span>
                </v-row>
              </div>
            </v-row>
          </template>
          <easyPlayerInfo
            tile
            report
            is-card
            action-bar
            only-coin
            :player-info="playerInfo"
            badge-type="relation"
            style="min-width: 300px"
          />
        </v-menu>
        <!-- self -->
        <template v-else>
          <v-row
            no-gutters
            align="center"
            class="cursor-pointer"
            @click="showPlayerInfoCardDialogStatus(player.username)"
          >
            <!-- icon -->
            <div>
              <v-img width="40" height="40" :src="player.thumbUrl" @error="errorImgHandler(player)">
                <template v-slot:placeholder> <placeHolder /></template>
              </v-img>
            </div>
            <!-- vip & title -->
            <div class="pl-4">
              <v-row no-gutters>
                <vipLevelIcon :vip-level="player.vipLevel" width="28" height="28" class="mr-2" />
                <span class="text-subtitle-1 default-content--text custom-text-noto">
                  {{ player.username }}
                </span>
              </v-row>
            </div>
          </v-row>
        </template>
      </v-list-item-content>
      <v-list-item-action>
        <v-row no-gutters justify="end">
          <!-- self -->
          <template v-if="isMe(player.username)">
            <v-btn icon disabled> <v-icon> mdi-account-plus</v-icon></v-btn>
            <v-btn icon disabled>
              <v-icon color="error"> mdi-account-cancel </v-icon>
            </v-btn>
            <v-btn icon disabled>
              <v-icon> mdi-message-processing-outline </v-icon>
            </v-btn>
          </template>
          <!-- other player -->
          <template v-else>
            <v-btn
              v-if="checkIsFriend(player.username)"
              icon
              @click="showConfirmDeleteFriendDialog(player.username)"
            >
              <v-icon color="warning"> mdi-account-remove </v-icon>
            </v-btn>
            <v-btn v-else icon @click="checkAddFriend(player.username)">
              <v-icon color="success"> mdi-account-plus</v-icon>
            </v-btn>

            <v-btn
              v-if="checkIsBlock(player.username)"
              icon
              @click="showConfirmDeleteBlockDialog(player.username)"
            >
              <v-icon color="info"> mdi-account-check </v-icon>
            </v-btn>
            <v-btn v-else icon @click="checkAddBlock(player.username)">
              <v-icon color="error"> mdi-account-cancel </v-icon>
            </v-btn>

            <v-btn
              icon
              :disabled="checkIsBlock(player.username)"
              @click="createMessage(player.username)"
            >
              <v-icon> mdi-message-processing-outline </v-icon>
            </v-btn>
          </template>
        </v-row>
      </v-list-item-action>
    </v-list-item>
  </v-list>
</template>
<script>
  import cloneDeep from 'lodash/cloneDeep'
  import relationship from '~/mixins/relationship'
  import chat from '@/mixins/chatroom/chat.js'

  export default {
    name: 'heatList',
    mixins: [relationship, chat],
    props: {
      heatList: {
        type: Array,
        default: []
      }
    },
    components: {
      vipLevelIcon: () => import('~/components/player_info/vipLevelIcon'),
      placeHolder: () => import('~/components/imgPlaceholder'),
      easyPlayerInfo: () => import('~/components/player_info/easyPlayerInfo')
    },
    data: () => ({
      heatListTmp: [],
      defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
      playerInfo: {
        username: '',
        level: 0,
        levelVip: 0,
        money: 0,
        thumbUrl: '',
        online: false,
        guildName: '-'
      }
    }),
    computed: {
      friendList({ $store }) {
        return $store.getters['social/friendList']
      },
      blockList({ $store }) {
        return $store.getters['social/blockList']
      },
      ownName({ $store }) {
        return $store.getters['role/userName']
      },
      isOfficialMember() {
        return this.vipLevel !== 0
      }
    },
    watch: {
      heatList: {
        handler(val) {
          if (val?.length > 0) {
            const self = this
            const originalHeatList = cloneDeep(val) // 保存原始的 heatList

            // 深拷貝 val 到 heatListTmp
            this.heatListTmp = val.map((item) => {
              return {
                ...item,
                thumbUrl: this.defaultImg
              }
            })
            // 平行地獲取縮略圖URL
            Promise.all(
              self.heatListTmp.map((item) => {
                const userData = { userName: item.username }
                return self.$store.dispatch('role/getThumbUrl', userData)
              })
            ).then((thumbUrls) => {
              // 檢查原始 heatList 是否與當前的 heatList 一致
              if (JSON.stringify(val) === JSON.stringify(originalHeatList)) {
                // 將獲取到的縮略圖URL填充回 heatListTmp 陣列
                for (let i = 0; i < thumbUrls.length; i++) {
                  self.heatListTmp[i].thumbUrl = thumbUrls[i]
                }
              }
            })
          }
        },
        immediate: true
      }
    },
    methods: {
      isMe(username) {
        return this.ownName === username
      },
      errorImgHandler(item) {
        item.thumbUrl = this.defaultImg
      },
      async setPlayerInfo(userName) {
        this.$nuxt.$loading.start()
        const role = await this.getPlayerData(userName)
        this.playerInfo.username = role.username
        this.playerInfo.level = role.level
        this.playerInfo.levelVip = role.levelVip
        this.playerInfo.money = role.money
        this.playerInfo.thumbUrl = role.thumbUrl
        this.playerInfo.online = role.online
        this.playerInfo.guildName = role.guildName
        this.$nuxt.$loading.finish()
      },
      async showPlayerInfoCardDialogStatus(userName) {
        this.setSelectPlayerInfo(await this.getPlayerData(userName))
        this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', true)
      }
    }
  }
</script>
