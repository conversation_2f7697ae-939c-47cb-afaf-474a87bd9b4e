<template>
  <div>
    <v-list flat color="transparent" v-show="!isFriendChat">
      <v-list-item-group
        v-model="selectedChatTmp"
        :mandatory="chatsFilter.length > 0"
        active-class="selected-list-item"
      >
        <v-list-item
          v-for="chat in chatsFilter"
          :key="chat.id"
          :value="chat.id"
          @click="mobileClickedEvent"
        >
          <v-list-item-content
            :class="isWhisper(chat.id, chat.title) && !chat.online ? 'friend-offline' : ''"
          >
            <!-- min-width: 0 是為了讓text-overflow在flex box當中可以生效 -->
            <v-row no-gutters align="center" class="flex-nowrap" style="min-width: 0">
              <!-- icon -->
              <div>
                <template v-if="chat.id === 0 || chat.id === 1">
                  <v-img
                    width="40"
                    height="40"
                    :src="require('~/assets/image/chatroom/' + chat.img)"
                  />
                </template>
                <template v-else-if="chat.isOfficial">
                  <v-img width="40" height="40" :src="defaultImg" />
                </template>
                <template v-else>
                  <div v-show="chat.isFriend">
                    <stopListGroup>
                      <v-menu right :close-on-content-click="false">
                        <template v-slot:activator="{ on, attrs }">
                          <v-badge
                            bordered
                            bottom
                            overlap
                            :color="chat.online ? 'success' : 'offline'"
                          >
                            <template v-slot:badge>
                              <v-icon size="12"> mdi-account-multiple </v-icon>
                            </template>
                            <v-img
                              width="40"
                              height="40"
                              :src="chat.img"
                              v-bind="attrs"
                              v-on="on"
                              @click="setPlayerInfo(chat.title)"
                            >
                              <template v-slot:placeholder> <placeHolder /></template>
                            </v-img>
                          </v-badge>
                        </template>
                        <easyPlayerInfo
                          tile
                          report
                          is-card
                          action-bar
                          only-coin
                          :player-info="playerInfo"
                          style="min-width: 300px"
                        />
                      </v-menu>
                    </stopListGroup>
                  </div>
                  <div v-show="!chat.isFriend">
                    <stopListGroup>
                      <v-menu right :close-on-content-click="false">
                        <template v-slot:activator="{ on, attrs }">
                          <v-img
                            width="40"
                            height="40"
                            :src="chat.img"
                            v-bind="attrs"
                            v-on="on"
                            @click="setPlayerInfo(chat.title)"
                          >
                            <template v-slot:placeholder> <placeHolder /></template>
                          </v-img>
                        </template>
                        <easyPlayerInfo
                          tile
                          report
                          is-card
                          action-bar
                          only-coin
                          :player-info="playerInfo"
                          style="min-width: 300px"
                        />
                      </v-menu>
                    </stopListGroup>
                  </div>
                </template>
              </div>
              <!-- title -->
              <!-- ellipsis color -->
              <div
                class="pl-4 text-overflow"
                :class="{
                  'default-content--text': chat.id < 2,
                  'name-private--text': chat.id > 1
                }"
              >
                <span
                  class="text-subtitle-1 custom-text-noto"
                  :class="{
                    'default-content--text': chat.id < 2,
                    'name-private--text': chat.id > 1
                  }"
                >
                  {{ chat.title }}
                </span>
              </div>
            </v-row>
          </v-list-item-content>
          <v-list-item-action>
            <notyCount v-if="chat.noty > 0" :noty="chat.noty" />
          </v-list-item-action>
        </v-list-item>
      </v-list-item-group>
    </v-list>
    <v-list
      flat
      color="transparent"
      v-show="isFriendChat"
      :class="[{ 'py-0': isFriendChat && friendListFromIndex.length === 0 }]"
    >
      <v-list-item-group
        :mandatory="friendListFilter.length > 0"
        active-class="selected-list-item"
        v-model="selectedFriendChatTmp"
      >
        <v-list-item
          v-for="friend in friendListFilter"
          :key="friend.username"
          :value="friend.username"
          @click="mobileClickedEvent"
        >
          <v-list-item-content :class="friend.online ? '' : 'friend-offline'">
            <!-- min-width: 0 是為了讓text-overflow在flex box當中可以生效 -->
            <v-row no-gutters align="center" class="flex-nowrap" style="min-width: 0">
              <!-- icon -->
              <div>
                <stopListGroup>
                  <v-menu right :close-on-content-click="false">
                    <template v-slot:activator="{ on, attrs }">
                      <v-badge
                        bordered
                        bottom
                        overlap
                        :color="friend.online ? 'success' : 'offline'"
                      >
                        <template v-slot:badge>
                          <v-icon size="12"> mdi-account-multiple </v-icon>
                        </template>
                        <v-img
                          width="40"
                          height="40"
                          :src="friend.thumbUrl"
                          v-bind="attrs"
                          v-on="on"
                          @click="setPlayerInfo(friend.username)"
                        >
                          <template v-slot:placeholder> <placeHolder /></template>
                        </v-img>
                      </v-badge>
                    </template>
                    <easyPlayerInfo
                      tile
                      report
                      is-card
                      action-bar
                      only-coin
                      :player-info="playerInfo"
                      style="min-width: 300px"
                    />
                  </v-menu>
                </stopListGroup>
              </div>
              <!-- title -->
              <!-- ellipsis color -->
              <div class="pl-4 text-overflow name-private--text">
                <span class="text-subtitle-1 name-private--text custom-text-noto">
                  {{ friend.username }}
                </span>
              </div>
            </v-row>
          </v-list-item-content>
          <v-list-item-action>
            <v-row no-gutters justify="end">
              <notyCount
                v-if="friend.noty > 0"
                :noty="friend.noty"
                :class="friend.online ? '' : 'friend-offline'"
              />
              <v-hover v-slot="{ hover }">
                <v-icon
                  class="ml-3"
                  :color="getPinColor(friend.chat.pin, hover)"
                  @click.stop="setFriendPin(friend.username, friend.chat.pin)"
                >
                  {{
                    hover && friend.chat.pin && $vuetify.breakpoint.lgAndUp
                      ? 'mdi-pin-off'
                      : 'mdi-pin'
                  }}
                </v-icon>
              </v-hover>
            </v-row>
          </v-list-item-action>
        </v-list-item>
      </v-list-item-group>
    </v-list>
    <div
      v-if="$vuetify.breakpoint.xsOnly && isFriendChat && friendListFromIndex.length === 0"
      class="d-flex justify-center align-center fill-height"
    >
      <span class="text-body-2 grey-3--text coustom-text-noto">
        {{ $t('no_friends_yet') }}
      </span>
    </div>
  </div>
</template>
<script>
  import chat from '~/mixins/chatroom/chat'
  import relationship from '~/mixins/relationship'

  export default {
    name: 'chatList',
    mixins: [chat, relationship],
    components: {
      placeHolder: () => import('~/components/imgPlaceholder'),
      notyCount: () => import('~/components/notyCount'),
      easyPlayerInfo: () => import('~/components/easyPlayerInfo'),
      stopListGroup: () => import('~/components/stopListGroup')
    },
    props: {
      selectedChat: {
        type: Number,
        default: 0
      },
      selectedFriendChat: {
        type: String,
        default: ''
      },
      isFriendChat: {
        type: Boolean,
        default: false
      },
      chats: {
        type: Array,
        default: () => []
      },
      keyword: {
        type: String,
        default: ''
      },
      friendListFromIndex: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        playerInfo: {
          username: '',
          level: 0,
          levelVip: 0,
          money: 0,
          thumbUrl: '',
          online: false,
          guildName: ''
        }
      }
    },
    computed: {
      chatsFilter() {
        const chats = this.chats.map((chat) => {
          return {
            ...chat,
            title: chat.id === 0 ? this.$t(chat.title) : chat.title
          }
        })
        return chats.filter((chat) => {
          if (chat.id === 0 || chat.id === 1) {
            return true
          }

          if (chat.isBlock || (this.whisperOnlyFriend && !this.checkIsFriend(chat.key))) {
            return false
          }

          return chat.title.toLowerCase().includes(this.keyword.toLowerCase())
        })
      },
      friendListFilter() {
        return this.friendListFromIndex.filter((friend) => {
          return friend.username.toLowerCase().includes(this.keyword.toLowerCase())
        })
      },
      selectedChatTmp: {
        get() {
          return this.selectedChat
        },
        set(val) {
          this.$emit('update:selectedChat', val)
          const selectedItem = this.chatsFilter.find((item) => item.id === val)
          if (selectedItem) this.selectedChatEvent(selectedItem.id)
        }
      },
      selectedFriendChatTmp: {
        get() {
          return this.selectedFriendChat
        },
        set(val) {
          this.$emit('update:selectedFriendChat', val)
          this.selectedFriendChatEvent()
        }
      },
      whisperOnlyFriend({ $store }) {
        return $store.state.chat.setting.whisper.onlyFriend
      }
    },
    methods: {
      getPinColor(pin, hover) {
        if (pin) {
          if (hover && this.$vuetify.breakpoint.lgAndUp) {
            return 'default-content'
          } else {
            return 'primary'
          }
        } else {
          if (hover && this.$vuetify.breakpoint.lgAndUp) {
            return 'default-content'
          } else {
            return 'btn-disable'
          }
        }
      },
      mobileClickedEvent() {
        if (this.$vuetify.breakpoint.xsOnly) this.$emit('mobileClickedEvent')
      },
      selectedChatEvent(id) {
        this.$emit('selectedChatEvent', id)
      },
      selectedFriendChatEvent() {
        this.$emit('selectedFriendChatEvent')
      },
      isWhisper(id, title) {
        return id > 1 && !this.isOfficial(title)
      },
      setFriendPin(username, pin) {
        this.$store.commit('social/SET_FRIEND_PIN', { username: username, pin: !pin })

        const friendsPin = {}

        this.friendList.forEach((item) => {
          friendsPin[item.username] = {}
          friendsPin[item.username].pin = item.chat.pin
        })
        this.saveFriendsPinToLocalStroage(friendsPin)
      },
      saveFriendsPinToLocalStroage(obj) {
        localStorage.setItem('friendsPin', JSON.stringify(obj))
      },
      getFriendsPinFromLocalStroage() {
        const friendsPin = localStorage.getItem('friendsPin')
        return JSON.parse(friendsPin)
      },
      async setPlayerInfo(userName) {
        const role = await this.getPlayerData(userName)
        this.playerInfo.username = role.username
        this.playerInfo.level = role.level
        this.playerInfo.levelVip = role.levelVip
        this.playerInfo.money = role.money
        this.playerInfo.thumbUrl = role.thumbUrl
        this.playerInfo.online = role.online
        this.playerInfo.guildName = role.guildName
      }
    }
  }
</script>
<style scoped>
  .friend-offline {
    opacity: 0.3;
  }
  .selected-list-item {
    background: #fff2923d;
  }
</style>
