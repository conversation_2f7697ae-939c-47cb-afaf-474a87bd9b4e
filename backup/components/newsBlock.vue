<template>
  <div class="border-line rounded-xl">
    <v-card color="transparent" class="pa-2 rounded-xl" elevation="6" style="z-index: 4">
      <!-- tabs -->
      <v-tabs v-model="tab" background-color="transparent" color="primary" class="pl-6 mt-2">
        <v-tab v-for="item in newsTemp.category" :key="item.type">{{ item.title }}</v-tab>
      </v-tabs>
      <v-card-text class="px-0">
        <!-- content -->
        <v-tabs-items v-model="tab" class="transparent">
          <v-tab-item v-for="item in newsTemp.category" :key="item.type">
            <v-row justify="center" no-gutters>
              <newsList
                ref="expandComponent"
                v-if="
                  newsTemp.datas.filter((newsItem) => item.type == newsItem.type).length > 0 &&
                  newsTemp.datas.filter((newsItem) => item.type == newsItem.type)[0].data.length > 0
                "
                :show-news-arr="
                  newsTemp.datas.filter((newsItem) => item.type == newsItem.type)[0].data
                "
                :mode="0"
                style="width: 100%"
              />
              <span v-else class="default-content--text" style="font-size: 20px">
                {{ $t(item.msg.noData) }}
              </span>
            </v-row>
          </v-tab-item>
        </v-tabs-items>
        <!-- see all -->
        <v-row no-gutters justify="center" class="pt-6">
          <span
            v-if="
              newsTemp.category[tab] &&
              newsTemp.datas.filter((newsItem) => newsTemp.category[tab].type == newsItem.type)
                .length > 0 &&
              newsTemp.datas.filter((newsItem) => newsTemp.category[tab].type == newsItem.type)[0]
                .data.length > 0
            "
            class="font-weight-medium cursor-pointer default-content--text text-md-body-2"
            @click.stop="goToNews"
            >{{ $t('view_all') }}</span
          >
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
  export default {
    name: 'newsBlock',
    components: {
      newsList: () => import('~/components/news')
    },
    props: {
      category: { type: Array, default: () => [] },
      datas: { type: Array, default: () => [] }
    },

    data() {
      return {
        tab: 0,
        newsTemp: { category: [], datas: [] }
      }
    },

    mounted() {},
    watch: {
      category: {
        handler(val) {
          this.newsTemp.category = val ? val : []
        },
        immediate: true
      },
      datas: {
        handler(val) {
          this.newsTemp.datas = val
        },
        immediate: true,
        deep: true
      },
      tab: {
        handler() {
          if (this.$refs.expandComponent) {
            if (Array.isArray(this.$refs.expandComponent))
              this.$refs.expandComponent.forEach((item) => item.closeAllPanels())
          }
        }
      }
    },
    methods: {
      goToNews() {
        const self = this
        self.$store.dispatch('news/setTab', self.tab)
        self.$router.push({ path: '/news' })
      }
    }
  }
</script>
