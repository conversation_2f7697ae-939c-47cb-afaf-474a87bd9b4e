<template>
  <div class="yoeGame-swiper">
    <bigProviderCardList :provider-list="yoeShopArray" />
    <yoeProviderCard
      v-if="levelGiftPack"
      class="level-gift-pack"
      :provider-info="yoeShopArray[0]"
    />
    <smallProviderCardList class="pt-4" :provider-list="yoeShopArray" />
    <!--取消-->
    <yoeConfirmDialog v-model="showCancelDialog" :show-cancel="false">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <span class="default-content--text">{{ $t('cancelYoeShopNoty') }}</span>
    </yoeConfirmDialog>
    <!--成功-->
    <yoeConfirmDialog v-model="showSuccessDialog" :show-cancel="false">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <div class="d-flex align-center">
        <span class="default-content--text">{{ $t('yoeShopSuccessNoty') }}</span>
      </div>
    </yoeConfirmDialog>
    <!--成功但沒收到訂單-->
    <yoeConfirmDialog v-model="showPurchaseCancelDialog" :show-cancel="false">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <span class="default-content--text">{{
        $t('successButPurchaseFalseNoty').split('\n')[0]
      }}</span
      ><br />
      <span class="default-content--text">
        {{ $t('successButPurchaseFalseNoty').split('\n')[1] }}
      </span>
    </yoeConfirmDialog>
    <!--error code 18-->
    <yoeConfirmDialog v-model="showErrorDialog" :show-cancel="false" :action="handleConfirmClick">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <span class="default-content--text">{{ errorMessage }}</span>
    </yoeConfirmDialog>
  </div>
</template>

<script>
  import bigProviderCardList from '~/components/payment/bigProviderCardList'
  import smallProviderCardList from '~/components/payment/smallProviderCardList'
  import yoeProviderCard from '~/components/payment/yoeProviderCard'
  import yoeGameMgr from '@/mixins/yoeGameMgr.js'

  export default {
    name: 'giftPackArea',
    mixins: [yoeGameMgr],
    components: {
      bigProviderCardList,
      smallProviderCardList,
      yoeProviderCard,
      yoeConfirmDialog: () => import('~/components/payment/yoeConfirmDialog')
    },
    data() {
      return {
        showCancelDialog: false,
        showSuccessDialog: false,
        showPurchaseCancelDialog: false,
        showErrorDialog: false,
        errorMessage: ''
      }
    },
    computed: {
      yoeShopArray({ $store }) {
        return $store.getters['yoeShop/yoeShopArray']
      },
      levelGiftPack() {
        return this.yoeShopArray.find((item) => item.id && item.id.includes('gpvip'))
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      }
    },
    mounted() {
      this.$nuxt.$on('yoe:dialogStatusChange', this.handleYoeDialogStatusChange)
      this.$nuxt.$on('yoe:showYoeDialogStatus', this.handleYoeDialogStatusChange)
      this.$nuxt.$on('yoe:showErrorDialog', this.handleErrorDialog)
    },
    beforeDestroy() {
      this.$nuxt.$off('yoe:dialogStatusChange', this.handleYoeDialogStatusChange)
      this.$nuxt.$off('yoe:showYoeDialogStatus', this.handleYoeDialogStatusChange)
      this.$nuxt.$off('yoe:showErrorDialog', this.handleErrorDialog)
    },
    methods: {
      handleConfirm() {
        this.$store.commit('yoeShop/SET_PRODUCT_INFO', this.providerInfo)
        this.$nuxt.$emit('root:showYoeDialogStatus', {
          show: true,
          shopItem: this.providerInfo,
          cancel: false,
          purChaseCancel: false,
          onClose: async () => {
            // 對話框關閉時重新獲取數據
            await this.$store.dispatch('yoeShop/fetchYoeGame')
          }
        })
      },
      async handleYoeDialogStatusChange(value) {
        // 維持原有的維護檢查邏輯
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) {
          return
        }
        // 維持原有的狀態處理邏輯
        if (value.cancel) {
          this.showCancelDialog = true
        }
        if (!value.cancel) {
          await this.$store.dispatch('yoeShop/fetchYoeGame')
          this.showSuccessDialog = true
        }
        if (!value.cancel && value.purChaseCancel) {
          this.showPurchaseCancelDialog = true
        }
      },
      handleErrorDialog(error) {
        if (error.errorCode === 18) {
          this.errorMessage = error.message
          this.showErrorDialog = true
        }
      },
      async handleConfirmClick() {
        await this.$store.dispatch('yoeShop/fetchYoeGame')

        const iframe = this.$store.getters['yoeShop/iframe']
        if (iframe) {
          this.sendMessage({
            type: this.sendType.QUERY_PENDING_PURCHASE
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .yoeGame-swiper::v-deep {
    .swiper-item {
      margin-right: 0px !important;

      .swiper-slide:last-child {
        margin-right: 0 !important;
      }
    }
  }
  .level-gift-pack::v-deep {
    margin: 25px 0 9px 0;
    .gift-pack-card {
      .gift-pack-card-layout {
        flex-direction: row;
        justify-content: space-between;
        .gift-pack-main-img-block {
          display: flex;
          flex-direction: row-reverse;
          align-items: center;
          .gift-pack-title-block {
            margin-left: 25px;
          }
          .limit-and-img {
            position: relative;
            .gift-limit-count {
              display: block;
              width: 100%;
              text-shadow: 0px 2px 1px rgba(0, 0, 0, 0.2), 0px 1px 1px rgba(0, 0, 0, 0.14),
                0px 1px 3px rgba(0, 0, 0, 0.12);
            }
            .main-img {
              margin-top: -4px !important;
            }
          }
        }
        .levelPack-right-layout {
          width: 50%;
          padding-left: 16px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          .countDown-and-price {
            display: flex;
            flex-direction: column;
            width: 50%;
            .time-count-down {
              width: 100%;
              margin-bottom: 4px;
              span {
                white-space: nowrap;
              }
            }
          }
        }
        .gift-pack-title-block {
          .levelPack-price {
            color: #e9b950;
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            line-height: 22px;
            letter-spacing: 0.1px;
          }
        }
        .gift-pack-discountIcon {
          width: 8%;
          top: -3%;
          left: -10px;
        }
        .price-area {
          width: 100%;
          margin: 0;
        }
      }
      .gift-pack-content {
        font-weight: 400;
      }
    }
  }
  @media screen and (max-width: 550px) {
    .level-gift-pack::v-deep {
      .gift-pack-card {
        .gift-pack-card-layout {
          justify-content: space-between;
          padding: 20px 16px !important;
          .gift-pack-main-img-block {
            width: 40%;
            flex-direction: column-reverse;
            margin-top: 10px;
            position: relative;
            .gift-limit-count {
              width: 100%;
              top: -10px;
              left: 0;
            }
            .gift-pack-title-block {
              margin-left: unset;
              margin-top: 10px;
              .levelPack-price {
                white-space: nowrap;
              }
            }
          }
          .gift-pack-discountIcon {
            width: 15%;
            top: 0;
          }
          .levelPack-right-layout {
            width: 60%;
            flex-direction: column;
            .gift-pack-content {
              .main-content {
                margin-bottom: 19px;
              }
              .sub-content {
                height: unset;
                margin-bottom: 19px;
              }
              &.has-countdown {
                .main-content {
                  margin-bottom: 4px;
                }
                .sub-content {
                  margin-bottom: 4px;
                }
              }
            }
            .countDown-and-price {
              width: 100%;
              .price-area {
                width: 100%;
                margin: 0;
              }
            }
          }
        }
        .gift-pack-content {
          font-size: 14px;
        }
      }
    }
  }
  @media screen and (max-width: 520px) {
    .level-gift-pack::v-deep {
      .gift-pack-card {
        .gift-pack-card-layout {
          .gift-pack-main-img-block {
            .gift-pack-title-block {
              margin-left: 5px;
              .levelPack-price {
                white-space: nowrap;
              }
            }
          }
          .gift-pack-discountIcon {
            top: 16px;
          }
        }
      }
    }
  }
</style>
