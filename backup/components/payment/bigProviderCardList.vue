<template>
  <swiperCustom
    v-if="filteredProviderList.length > 0"
    :game-list="filteredProviderList"
    :swiper-slide-style="swiperSlideStyle"
    :show-slide-btn="showSlideBtnComputed"
    :btn-position="btnPosition"
    :disable-touch="true"
    :custom-swiper-option="customSwiperOptions"
  >
    <template #card="{ game }">
      <yoeProviderCard :provider-info="game" />
    </template>
  </swiperCustom>
</template>

<script>
  import swiperCustom from '~/components/swiperCustom.vue'
  import yoeProviderCard from '~/components/payment/yoeProviderCard'

  export default {
    name: 'bigProviderCardList',
    components: {
      swiperCustom,
      yoeProviderCard
    },
    props: {
      providerList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        btnPosition: {
          left: '-15px',
          right: '-15px',
          top: 'calc(50% + 10px)'
        },
        expiredProviderIds: [], // 儲存已過期的 provider id
        customSwiperOptions: {
          spaceBetween: 'auto',
          breakpoints: {
            // 當縮小為兩個一排時
            1: {
              spaceBetween: 8
            },
            520: {
              spaceBetween: 16
            }
          }
        }
      }
    },
    computed: {
      filteredProviderList() {
        return (
          this.providerList
            .filter((provider) => {
              // 必須是大卡且不在過期列表中
              return (
                provider.isBigCard &&
                !this.expiredProviderIds.includes(provider.id) &&
                // 二階段優化將階級禮包移至giftPackArea
                !(provider.id && provider.id.includes('gpvip'))
              )
            })
            // 按照sortOrder排序，如果沒有 sortOrder，給予最大值，確保排在最後
            .sort((a, b) => {
              const orderA = typeof a.sortOrder === 'number' ? a.sortOrder : Infinity
              const orderB = typeof b.sortOrder === 'number' ? b.sortOrder : Infinity
              return orderA - orderB
            })
        )
      },
      showSlideBtnComputed() {
        const breakWidth = this.$vuetify.breakpoint.width
        const twoGiftPack = breakWidth < 520 // 使用 520px 作為斷點

        return twoGiftPack
          ? this.filteredProviderList.length > 2 // 520px 以下超過 2 個顯示按鈕
          : this.filteredProviderList.length > 3 // 520px 以上超過 3 個顯示按鈕
      },
      swiperSlideStyle() {
        const breakWidth = this.$vuetify.breakpoint.width
        const width =
          breakWidth >= 1264
            ? this.$UIConfig.swiperBox.giftPackCardWidth.lg
            : breakWidth >= 960
            ? this.$UIConfig.swiperBox.giftPackCardWidth.md
            : breakWidth >= 600
            ? this.$UIConfig.swiperBox.giftPackCardWidth.sm
            : breakWidth >= 520
            ? this.$UIConfig.swiperBox.giftPackCardWidth.xs
            : this.$UIConfig.swiperBox.giftPackCardWidth.twoGiftPack
        return { boxSizing: 'border-box', width }
      }
    },
    methods: {}
  }
</script>

<style lang="scss" scoped>
  .swiper-container::v-deep {
    overflow: visible;
    .swiper-item {
      overflow-y: visible !important;
      overflow-x: hidden;
      padding-top: 8px;
    }
  }
</style>
