<template>
  <v-dialog
    v-model="showTransferPointDialogTmp"
    max-width="400px"
    persistent
    transition="dialog-transition"
  >
    <v-card elevation="0">
      <v-card elevation="0">
        <v-card
          class="d-flex justify-center gradient-primary-left rounded-b-0 transparent"
          tile
          elevation="0"
        >
          <v-card class="pa-2 rounded-0" color="transparent" outlined tile>
            <span class="white--text">
              {{ $t('transfer').toUpperCase() }}
            </span>
          </v-card>
        </v-card>
      </v-card>
      <v-container fluid class="transparent pa-5">
        <v-row>
          <v-col class="d-flex align-center">
            <span>{{ $t('transfer_noty1') }}</span>
          </v-col>
        </v-row>
        <v-row>
          <v-col class="pb-0">
            <v-text-field
              v-model="amount"
              v-validate="{
                required: true,
                transfer_min: 100,
                insufficient_quota: selfBalance
              }"
              type="number"
              name="transfer_amount"
              :error-messages="errors.first('transfer_amount')"
              :label="$t('transfer_amount')"
              :data-vv-as="$t('xin_coin')"
              filled
            />
          </v-col>
        </v-row>
        <v-row
          class="justify-center"
          :class="{
            'justify-left': $vuetify.breakpoint.xsOnly
          }"
        >
          <v-col
            v-for="(point, index) in fastAmountLevel"
            :key="`${index}`"
            cols="4"
            sm="4"
            md="4"
            lg="4"
            class="d-flex justify-center"
          >
            <v-btn color="primary-variant-1" outlined width="90px" @click="amount = point">
              <span>
                {{ formatPrice(point) }}
              </span>
            </v-btn>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-card color="transparent" elevation="0">
              <v-card-actions class="pa-0">
                <v-spacer />
                <v-btn class="mx-2" elevation="0" text @click="closeDialog">
                  <span class="black--text">
                    {{ $t('cancel').toUpperCase() }}
                  </span>
                </v-btn>
                <v-btn
                  :disabled="submitStatus"
                  class="mx-2"
                  color="primary-variant-1"
                  elevation="0"
                  @click="doTransferPlay"
                >
                  <span class="white--text">
                    {{ $t('startsPlay').toUpperCase() }}
                  </span>
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'TransferPointDialog',
    components: {},
    props: {
      showTransferPointDialog: { type: Boolean, required: true, default: false }
    },
    data() {
      const fastAmountLevel = [50000, 500000, 5000000]
      return {
        amount: 0,
        fastAmountLevel,
        showTransferPointDialogTmp: this.showTransferPointDialog,
        submitStatus: true
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      selfBalance({ $store }) {
        return $store.getters['role/balance']
      },
      isVi({ $store }) {
        return $store.$i18n.locale === 'vi-vn'
      },
      getSingleGameHallInfo({ $store }) {
        return $store.getters['gameHall/singleGameHallInfo']
      }
    },
    watch: {
      showTransferPointDialog: {
        handler(status) {
          this.showTransferPointDialogTmp = status
        }
      },
      amount: {
        handler() {
          this.$validator.validate().then((valid) => {
            if (!valid) {
              this.submitStatus = true
            } else {
              this.submitStatus = false
            }
          })
        }
      }
    },
    mounted() {},
    methods: {
      closeDialog() {
        this.amount = 0
        this.$validator.reset('transfer_amount')
        this.$emit('update:showTransferPointDialog', false)
      },
      formatPrice(value) {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      },
      customGameOpen(url) {
        let taget = ''
        let option = ''
        if (
          window.navigator.userAgent.indexOf('Safari') > -1 &&
          window.navigator.userAgent.indexOf('Chrome') === -1 &&
          this.$cookies.get('accept_popup_window') !== 1
        ) {
          alert(this.$t('popup_windows_noty'))
          this.$cookies.set('accept_popup_window', 1, { path: '/', maxAge: 60 * 60 * 24 * 365 })
        }
        if (this.$device.isMobile || this.$device.isTablet) {
          // 非PC裝置
          taget = '_blank'
          option = 'noopener,noreferrer'
        } else {
          // PC裝置
          taget = 'redirect'
        }
        window.open(url, taget, option)
      },
      isJson(str) {
        try {
          JSON.parse(str)
        } catch (e) {
          return false
        }
        return true
      },
      async transferOut(body) {
        // 星城切割功能 轉帳
        const reqData = this.$wsPacketFactory.withdraw(body.platformId, parseInt(this.amount))

        this.$wsClient.send(reqData)

        await this.$xinUtility
          .waitEvent(
            this.$wsClient.receivedListeners,
            (data) => {
              return data.isFeature(this.$xinConfig.FEATURE.GAME.TYPE.WITHDRAW)
            },
            5000
          )
          .then((res) => {
            let message = ''
            if (res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.SYSTEM_MESSAGE.ID) {
              const response = res.message.replace('-', '')
              if (this.isJson(response)) {
                const data = JSON.parse(response)
                this.$notify.backendError(data.error_code)
              } else if (res.message) {
                message = res.message
                this.$notify.info(message.replace('-', ''))
              }
            } else {
              // 將廠商餘額存入Store中
              this.$store.commit('role/SET_PROVIDER_WALLETS', {
                providerId: res.providerId,
                balance: res.balance
              })
              this.$notify.success(this.$t('deposit_success_msg', { amount: parseInt(res.amount) }))
            }
          })
          .catch((err) => this.$notify.info(err + ' ' + this.$t('unknow_error_noty')))
      },
      async doTransferPlay() {
        const body = {
          gameCategoryId: this.getSingleGameHallInfo.gameCategoryId,
          platformId: this.getSingleGameHallInfo.platformId,
          gameId: this.getSingleGameHallInfo.gameId,
          lang: this.$i18n.locale
        }
        this.submitStatus = true

        // 取得遊戲連結
        this.$wsClient.send(this.$wsPacketFactory.getGameLink(body.platformId, body.gameId))
        await this.$xinUtility
          .waitEvent(
            this.$wsClient.receivedListeners,
            (data) => {
              return data.isFeature(this.$xinConfig.FEATURE.GAME.TYPE.GET_LINK)
            },
            5000
          )
          .then((res) => {
            let message = ''

            if (Object.prototype.hasOwnProperty.call(res, 'url')) {
              this.$emit('update:showTransferPointDialog', false)
              this.transferOut(body)
              this.customGameOpen(res.url)
            } else if (Object.prototype.hasOwnProperty.call(res, 'code')) {
              const response = res.message.replace('-', '')
              if (this.isJson(response)) {
                const data = JSON.parse(response)
                this.$notify.backendError(data.error_code)
                if (data.error_code === 60001) {
                  this.$store.dispatch('gameHall/updateListWhenDisabled', {
                    gameCategoryId: body.gameCategoryId,
                    gameId: body.gameId
                  })
                } else {
                  this.$store.commit('gameHall/SET_GAMELIST_MAINTAIN_STATUS', {
                    gameId: body.gameId,
                    status: true
                  })
                }
                this.$emit('update:showTransferPointDialog', false)
              } else if (res.message) {
                message = res.message
                this.$notify.info(message.replace('-', ''))
              }
            }
          })
          .catch(() => this.$notify.info(this.$t('unknow_error_noty')))
        this.amount = 0
        this.$validator.reset('transfer_amount')
        this.submitStatus = false
      }
    }
  }
</script>
