<template>
  <v-card class="gradient-white-card rounded-xl pa-2" elevation="6">
    <v-container>
      <!-- game type tab -->
      <v-row no-gutters>
        <v-spacer></v-spacer>
        <v-col xl="8" lg="8" md="8" cols="12" class="pl-7">
          <v-tabs background-color="transparent" color="primary" class="removeArrow">
            <v-tab
              v-for="(category, index) in gameCategory"
              :key="index"
              class="v-tab-custom"
              :show-arrows="false"
              @click="selectedGameCategory = category.code"
            >
              {{ $t(category.dict + '_game') }}
            </v-tab>
          </v-tabs>
        </v-col>
      </v-row>
      <!-- game title & slide group -->
      <v-row no-gutters align="center">
        <!-- game title -->
        <v-col
          cols="12"
          sm="12"
          md="4"
          lg="4"
          v-if="$vuetify.breakpoint.mdAndUp || ($vuetify.breakpoint.smAndDown && !isLogin)"
        >
          <v-card color="transparent" elevation="0">
            <!-- mdAndTop -->
            <v-card-title
              v-if="!($vuetify.breakpoint.smAndDown && !isLogin)"
              class="text-left gradient-title--text custom-text-noto text-h2 font-weight-bold"
              :style="{
                'font-size':
                  $vuetify.breakpoint.width <= 1000 ? '5.5vw !important' : '60px !important'
              }"
            >
              {{
                selectedGameCategory == 100
                  ? 'CASINO'
                  : selectedGameCategory == 300
                  ? 'BOARD'
                  : selectedGameCategory == 600
                  ? 'FISHING'
                  : 'PARTY'
              }}
            </v-card-title>
            <v-card-subtitle
              v-if="!($vuetify.breakpoint.smAndDown && !isLogin)"
              class="text-left gradient-title--text custom-text-noto text-h2 font-weight-bold"
              :style="{
                'font-size':
                  $vuetify.breakpoint.width <= 1000 ? '5.5vw !important' : '60px !important'
              }"
            >
              {{ selectedGameCategory == 300 ? 'CARD' : 'GAMES' }}
            </v-card-subtitle>
            <v-card-text class="justify-left text-left pb-0 pl-7">
              <!-- smAndDown -->
              <p
                v-if="$vuetify.breakpoint.smAndDown && !isLogin"
                class="text-left gradient-title--text font-weight-bold"
                style="font-size: 21px"
              >
                {{
                  selectedGameCategory == 100
                    ? 'CASINO GAMES'
                    : selectedGameCategory == 300
                    ? 'BOARD CARD'
                    : selectedGameCategory == 600
                    ? 'FISHING GAMES'
                    : 'PARTY GAMES'
                }}
              </p>
              <p class="font-weight-regular grey-2--text custom-text-noto text-body-1">
                {{
                  selectedGameCategory == 100
                    ? $t('hot_provider_description')
                    : selectedGameCategory == 300
                    ? $t('hot_provider_description1')
                    : selectedGameCategory == 600
                    ? $t('hot_provider_description2')
                    : $t('hot_provider_description3')
                }}
              </p>
              <p class="mb-0">
                <v-btn
                  elevation="0"
                  class="gradient-primary justify-left"
                  rounded
                  :to="
                    localePath(
                      '/series_guide?category=' + chooseGameCategoryName(selectedGameCategory)
                    )
                  "
                >
                  <span class="white--text">
                    {{ $t('explore_all') }}
                  </span>
                </v-btn>
              </p>
            </v-card-text>
          </v-card>
        </v-col>
        <!-- slide group & arrow btn -->
        <v-col cols="12" md="8" lg="8">
          <v-slide-group
            id="v-slide-custom"
            v-model="slideGroup"
            class="pa-4 pb-0"
            active-class="success"
            mandatory
            center-active
          >
            <v-slide-item
              v-for="(provider, index) in showGameProviderList"
              :key="index"
              v-slot="{ active }"
            >
              <v-card color="transparent" elevation="0">
                <v-hover v-slot="{ hover }">
                  <v-card
                    :width="$vuetify.breakpoint.xsOnly ? '150px' : '200px'"
                    :height="$vuetify.breakpoint.xsOnly ? '225px' : '300px'"
                    color="transparent"
                    class="ma-3 rounded"
                    elevation="2"
                    :to="
                      !provider.maintained
                        ? generateGameHallLink(
                            chooseGameCategoryName(selectedGameCategory),
                            provider.providerId
                          )
                        : ''
                    "
                  >
                    <v-img
                      :lazy-src="gameProviderDefault"
                      :aspect-ratio="200 / 300"
                      height="100%"
                      :src="provider.url"
                      class="rounded"
                      @error="setAltImg(index)"
                    >
                      <template v-slot:placeholder>
                        <v-row class="fill-height ma-0" align="center" justify="center">
                          <v-progress-circular indeterminate color="grey lighten-5" />
                        </v-row>
                      </template>
                      <div
                        class="d-flex v-card--reveal"
                        :class="provider.maintained ? 'gradient-game-maintenance' : ''"
                        style="height: 100%"
                      >
                        <span
                          v-if="provider.maintained"
                          class="material-symbols-outlined white--text"
                          style="font-size: 48px"
                        >
                          construction
                        </span>
                      </div>
                    </v-img>
                    <v-expand-transition>
                      <v-card
                        v-if="hover && !provider.maintained"
                        class="transition-fast-in-fast-out v-card--reveal"
                        color="transparent"
                        style="height: 100%"
                      >
                        <v-sheet
                          height="100%"
                          tile
                          style="
                            opacity: 0.9 !important;
                            background: linear-gradient(#8dacfc, #345ec8);
                          "
                          class="bottom-gradient"
                        >
                          <v-row class="fill-height" align="center" justify="center">
                            <div class="custom-text-noto text-h2 white--text">GO</div>
                          </v-row>
                        </v-sheet>
                      </v-card>
                    </v-expand-transition>
                  </v-card>
                </v-hover>

                <v-card color="transparent" elevation="0">
                  <v-card-text
                    class="pt-0 text-left grey-2--text font-weight-medium text-md-body-1"
                  >
                    {{ provider.name }}
                    <span class="text-sm-body-2 font-weight-bold warning--text">
                      {{ provider.maintained ? $t('in_maintenance') : '' }}
                    </span>
                  </v-card-text>
                </v-card>
              </v-card>
            </v-slide-item>
          </v-slide-group>
          <!-- arrow btn -->
          <v-row class="pl-8 justify-space-between">
            <div>
              <v-btn color="white" fab dark x-small class="mx-2" @click="previousSlide">
                <span class="material-symbols-rounded grey-3--text"> arrow_left </span>
              </v-btn>
              <v-btn color="white" fab dark x-small class="mx-2" @click="nextSlide">
                <span class="material-symbols-rounded grey-3--text"> arrow_right </span>
              </v-btn>
            </div>
            <div v-if="isLogin && $vuetify.breakpoint.smAndDown" class="pr-7">
              <v-btn
                elevation="0"
                class="gradient-primary justify-left"
                rounded
                :to="
                  localePath(
                    '/series_guide?category=' + chooseGameCategoryName(selectedGameCategory)
                  )
                "
              >
                <span class="white--text" style="font-size: 14px">
                  {{ $t('explore_all') }}
                </span>
              </v-btn>
            </div>
          </v-row>
        </v-col>
      </v-row>
    </v-container>
  </v-card>
</template>

<script>
  export default {
    name: 'HotProvider',
    props: {
      gameProviderList: {
        type: Array,
        default() {
          return []
        }
      }
    },
    data() {
      return {
        selectedGameCategory: 100,
        slideGroup: 0,
        showGameProviderList: [],
        gameProviderDefault: require('~/assets/image/game/provider_default.jpg')
      }
    },
    computed: {
      gameCategory({ $store }) {
        return $store.getters['gameProvider/gameCategory']
      },
      isLogin() {
        return this.$store.getters['role/isLogin']
      }
    },
    watch: {
      selectedGameCategory: {
        handler() {
          const providerList = this.gameProviderList
          this.showGameProviderList = this.doClassificationForGameProvider(providerList)
          this.slideGroup = 0
        }
      }
    },
    mounted() {
      const providerList = this.gameProviderList
      this.showGameProviderList = this.doClassificationForGameProvider(providerList)
    },
    methods: {
      checkMaintain() {
        return this.$store.getters['newGame/isMaintain'](this.game, this.$store.getters.now)
      },
      doClassificationForGameProvider(providerList) {
        let newData = []

        newData = providerList.filter((val) => {
          let selectedProvider = 0
          selectedProvider = val.gameCategoryId.indexOf(this.selectedGameCategory)
          return providerList[selectedProvider]
        })
        return newData
      },
      nextSlide() {
        if (this.slideGroup < 14) {
          this.slideGroup += 3
        } else {
          this.slideGroup = 14 + 1
        }
      },
      previousSlide() {
        if (this.slideGroup > 0) {
          this.slideGroup -= 3
        } else {
          this.slideGroup = -1
        }
      },
      generateGameHallLink(gameCategory, providerId) {
        let url = ''
        url = '/games?category=' + gameCategory.toLowerCase() + '&gameProvider=' + providerId

        return this.localePath(url)
      },
      chooseGameCategoryName(categoryId) {
        const selectedCategory = this.gameCategory.filter((val) => val.code === categoryId)
        const categoryName = selectedCategory[0].name
        return categoryName
      },
      setAltImg(index) {
        this.showGameProviderList[index].url = this.gameProviderDefault
      }
    }
  }
</script>

<style lang="scss" scoped>
  #v-slide-custom ::v-deep {
    .v-slide-group__prev {
      display: none !important;
    }

    .v-slide-group__next {
      display: none !important;
    }
  }
  .removeArrow ::v-deep {
    .v-slide-group__prev {
      display: none !important;
    }
    .v-slide-group__next {
      display: none !important;
    }
  }
</style>
