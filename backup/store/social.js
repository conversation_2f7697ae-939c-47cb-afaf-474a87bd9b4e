import Vue from 'vue'

export const state = () => ({
  friendList: [],
  blockList: [],
  guildMemberList: [],
  promote: '',
  selectPlayer: {},
  newlyOnlineFriend: ''
})

export const getters = {
  friendList(state) {
    return state.friendList
  },
  guildMemberList(state) {
    return state.guildMemberList
  },
  promote(state) {
    return state.promote
  },
  selectPlayer(state) {
    return state.selectPlayer
  },
  blockList(state) {
    return state.blockList
  },
  newlyOnlineFriend(state) {
    return state.newlyOnlineFriend
  }
}

export const mutations = {
  SET_ALL_FRIENDLIST(state, data) {
    state.friendList = data
  },
  SET_SINGLE_FRIEND(state, data) {
    state.friendList.push(data)
  },
  SET_ALL_BLOCKLIST(state, data) {
    state.blockList = data
  },
  SET_SINGLE_BLOCK(state, data) {
    state.blockList.push(data)
  },
  SET_SINGLE_FRIEND_ONLINE_STATUS(state, data) {
    let friend = state.friendList.find((item) => item.username === data.username)
    if (friend) {
      friend.online = data.online
      friend.gender = data.gender
      friend.rank = data.rank
      friend.level = data.level
      friend.levelVip = data.levelVip
      friend.money = data.money
      if (data.thumbUrl) {
        friend.thumbUrl = data.thumbUrl
      }
    }
  },
  SET_SINGLE_BLOCK_ONLINE_STATUS(state, data) {
    let block = state.blockList.find((item) => item.username === data.username)
    if (block) {
      block.online = data.online
      block.gender = data.gender
      block.rank = data.rank
      block.level = data.level
      block.levelVip = data.levelVip
      block.money = data.money
      if (data.thumbUrl) {
        block.thumbUrl = data.thumbUrl
      }
    }
  },
  SET_GUILD_MEMBER_LIST(state, data) {
    state.guildMemberList = data
  },
  SET_PROMOTE(state, data) {
    state.promote = data
  },
  SET_SELECT_FRIEND(state, data) {
    state.selectPlayer = data
  },
  DELETE_FRIEND(state, data) {
    let friendList = state.friendList
    for (let i = 0; i < friendList.length; i++) {
      if (friendList[i].username === data) {
        friendList.splice(i, 1)
        break
      }
    }
  },
  DELETE_BLOCK(state, data) {
    let blockList = state.blockList
    for (let i = 0; i < blockList.length; i++) {
      if (blockList[i].username === data) {
        blockList.splice(i, 1)
        break
      }
    }
  },
  SET_FRIEND_NOTY(state, data) {
    let friend = state.friendList.find((item) => item.username === data.username)
    if (friend) {
      friend.noty = data.noty
    }
  },
  CLEAR_FRIEND_ALL_NOTY(state) {
    state.friendList.forEach((item) => {
      item.noty = 0
    })
  },
  SET_FRIEND_PIN(state, data) {
    let friend = state.friendList.find((item) => item.username === data.username)
    if (friend) {
      friend.chat.pin = data.pin
    }
  },
  SET_FRIEND_LAST_UPDATE(state, data) {
    let friend = state.friendList.find((item) => item.username === data.username)
    if (friend) {
      friend.lastUpdate = data.lastUpdate
    }
  },
  RESET(state) {
    Vue.set(state, 'friendList', [])
    Vue.set(state, 'blockList', [])
    Vue.set(state, 'promote', '')
    Vue.set(state, 'selectPlayer', '')
  },
  SET_NOW_ONLINE_FRIEND(state, data) {
    state.newlyOnlineFriend = data
  },
  SET_SCROLL_TOP(state, payload) {
    let index = state.friendList.findIndex((item) => item.username === payload.username)
    if (index !== -1) state.friendList[index].scrollTop = payload.scrollTop
  }
}

export const actions = {
  async init({ dispatch, commit }, data) {
    const friendsPin = await localStorage.getItem('friendsPin')
    let friendsPinObj = null
    if (friendsPin) friendsPinObj = JSON.parse(friendsPin)
    for (let index = 0; index < data.friendList.length; index++) {
      data.friendList[index].levelVip = data.friendList[index].vip
      const tmp = data.friendList[index]
      const userPin = friendsPinObj ? friendsPinObj[tmp.username]?.pin : false
      const thumbUrl = await dispatch(
        'role/getThumbUrl',
        { userName: tmp.username },
        { root: true }
      )
      tmp.thumbUrl = thumbUrl
      tmp.chat = {}
      tmp.chat.pin = userPin ? userPin : false
      tmp.chat.scrollTop = 0
      tmp.noty = 0
      tmp.lastUpdate = null
    }
    for (let index = 0; index < data.blockList.length; index++) {
      data.blockList[index].levelVip = data.blockList[index].vip
      const tmp = data.blockList[index]
      const thumbUrl = await dispatch(
        'role/getThumbUrl',
        { userName: tmp.username },
        { root: true }
      )
      data.blockList[index].thumbUrl = thumbUrl
    }
    commit('SET_ALL_FRIENDLIST', data.friendList)
    commit('SET_ALL_BLOCKLIST', data.blockList)
  },
  // eslint-disable-next-line no-unused-vars
  sendGMT({ commit }, message) {
    if (process.env.NUXT_ENV !== 'production') {
      const channelId = this.$xinConfig.CHANNEL.LOCALE
      const req = this.$wsPacketFactory.sendGMT(channelId, message)
      this.$wsClient.send(req)
    }
  },
  showCMD() {
    const cmdList = [
      {
        cmd: '請給我刮刮卡',
        description: '請給我刮刮卡'
      },
      {
        cmd: '請給我翻轉卡',
        description: '請給我翻轉卡'
      },
      {
        cmd: 'level=x',
        description: '調整等級，x:等級'
      },
      {
        cmd: '加榮譽值',
        description: '獲得榮譽值，一次加50000'
      },
      {
        cmd: 'setmoney=x',
        description: '調整星幣，x:星幣金額'
      },
      {
        cmd: 'setsilver=x',
        description: '調整銀幣，x:銀幣金額'
      },
      {
        cmd: '加點數=x',
        description: '增加點數，x:點數'
      },
      {
        cmd: '請給我新信件',
        description:
          '結果: 自己會收到一封帶有連結的信件 指令範例: 請給我新信件=test=星城Online-官方=我是內文=10=2000=1=hhhhh=1 指令格式: 請給我新信件=標題=寄件者=內文=期限(幾分鐘後刪除)=數量=類型=收件者=幾封'
      },
      {
        cmd: '設VIP=x',
        description: 'x 請輸入 0-7，對應VIP 等級'
      },
      {
        cmd: '建立公會=會長暱稱',
        description: '會長暱稱請輸入玩家名稱'
      },
      {
        cmd: '解散公會=公會名稱',
        description: '公會名稱請輸入公會名稱'
      }
    ]
    if (process.env.NUXT_ENV !== 'production') {
      console.log('指令: ', cmdList)
    }
  },
  async getUserDetail(ctx, username) {
    const reqData = this.$wsPacketFactory.getUserDetail(username)
    this.$wsClient.send(reqData)
    const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
      return data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.ID)
    })
    return res
  },
  setSingleFriendStatus({ dispatch, commit, rootGetters }, data) {
    //好友上線後取得詳細資料
    var userData = {}
    commit('SET_NOW_ONLINE_FRIEND', data.username)
    dispatch('getUserDetail', data.username).then(async (detail) => {
      userData.username = detail.username
      userData.facebookId = detail.facebookId
      userData.rank = detail.rank
      userData.gender = detail.gender
      userData.level = detail.level
      userData.vip = detail.levelVip
      userData.levelVip = detail.levelVip
      userData.online = detail.online

      const chats = rootGetters['chat/chats']
      const chatIndex = chats.findIndex((item) => item.title === userData.username)
      if (chatIndex !== -1) {
        commit(
          'chat/SET_CHAT_ONLINE_STATUS_BY_NAME',
          { name: data.username, online: userData.online },
          { root: true }
        )
      }
      // 玩家上線時，更新大頭貼連結
      if (userData.online) {
        await dispatch('role/getThumbUrl', { userName: data.username }, { root: true }).then(
          (thumbUrl) => {
            userData.thumbUrl = thumbUrl
          }
        )
      }
      commit('SET_SINGLE_FRIEND_ONLINE_STATUS', userData)
    })
  },
  setSingleBlockStatus({ dispatch, commit }, data) {
    var userData = {}
    commit('SET_NOW_ONLINE_FRIEND', data.username)
    dispatch('getUserDetail', data.username).then(async (detail) => {
      userData.username = detail.username
      userData.facebookId = detail.facebookId
      userData.rank = detail.rank
      userData.gender = detail.gender
      userData.level = detail.level
      userData.Vip = detail.levelVip
      userData.online = detail.online

      // 玩家上線時，更新大頭貼連結
      if (userData.online) {
        await dispatch('role/getThumbUrl', { userName: data.username }, { root: true }).then(
          (thumbUrl) => {
            userData.thumbUrl = thumbUrl
          }
        )
      }
      commit('SET_SINGLE_BLOCK_ONLINE_STATUS', userData)
    })
  },
  //加好友
  async addFriend({ commit, dispatch }, res) {
    let friendObj = {}
    friendObj.username = res.username
    friendObj.facebookId = res.facebookId
    friendObj.rank = res.rank
    friendObj.online = res.online
    friendObj.gender = res.gender
    const userDetail = await dispatch('getUserDetail', res.username)
    const userThumbUrl = await dispatch(
      'role/getThumbUrl',
      { userName: res.username },
      { root: true }
    )
    friendObj.levelVip = userDetail.levelVip
    friendObj.level = userDetail.level
    friendObj.thumbUrl = userThumbUrl
    friendObj.chat = {}
    friendObj.chat.pin = false
    friendObj.chat.scrollTop = 0
    friendObj.noty = 0
    friendObj.lastUpdate = null
    commit('SET_SINGLE_FRIEND', friendObj)
  },
  //加黑名單
  async addBlock({ commit, dispatch }, res) {
    let friendObj = {}
    friendObj.username = res.username
    friendObj.facebookId = res.facebookId
    friendObj.rank = res.rank
    friendObj.online = res.online
    friendObj.gender = res.gender
    const userDetail = await dispatch('getUserDetail', res.username)
    const userThumbUrl = await dispatch(
      'role/getThumbUrl',
      { userName: res.username },
      { root: true }
    )
    friendObj.levelVip = userDetail.levelVip
    friendObj.level = userDetail.level
    friendObj.thumbUrl = userThumbUrl
    commit('SET_SINGLE_BLOCK', friendObj)
  }
}
