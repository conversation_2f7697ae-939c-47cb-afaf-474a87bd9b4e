// 有好友與聊天 且星城頻道得版本
import Vue from 'vue'
import config from '~/plugins/xin-socket/config.js'

const SEND_MAX_ID = 60000
const BANK_GAME_ID = 2
const STATION = process.env.STATION
export const state = () => ({
  heartbeat: null,
  serverMsg: '',
  heartbeatTimes: 0,
  loginStep: 0,
  services: {
    [config.LOGIN_SERVICE.ID]: {
      id: config.LOGIN_SERVICE.ID,
      enable: false,
      sendId: 0,
      receiveId: 0
    },
    [config.LOTTERY_SERVICE.ID]: {
      id: config.LOTTERY_SERVICE.ID,
      enable: false,
      sendId: 0,
      receiveId: 0
    },
    [config.GAME_SERVICE.ID]: {
      id: config.GAME_SERVICE.ID,
      enable: false,
      sendId: 0,
      receiveId: 0
    },
    [config.SOCIAL_SERVICE.ID]: {
      id: config.SOCIAL_SERVICE.ID,
      enable: false,
      sendId: 0,
      receiveId: 0
    },
    [config.BANK_SERVICE.ID]: {
      id: config.BANK_SERVICE.ID,
      enable: false,
      sendId: 0,
      receiveId: 0
    },
    [config.GUILD_SERVICE.ID]: {
      id: config.GUILD_SERVICE.ID,
      enable: false,
      sendId: 0,
      receiveId: 0
    },
    [config.WEB_GAME_SERVICE.ID]: {
      id: config.WEB_GAME_SERVICE.ID,
      enable: false,
      sendId: 0,
      receiveId: 0
    }
  },
  healthyData: '',
  latency: undefined,
  failedTimes: 0,
  serverTimestamp: 0,
  socketRetryTimes: 0
})

export const getters = {
  loginServiceSendId(state) {
    return state.services[config.LOGIN_SERVICE.ID].sendId
  },
  loginServiceReceiveId(state) {
    return state.services[config.LOGIN_SERVICE.ID].receiveId
  },
  gameServiceSendId(state) {
    return state.services[config.GAME_SERVICE.ID].sendId
  },
  gameServiceReceiveId(state) {
    return state.services[config.GAME_SERVICE.ID].receiveId
  },
  socialServiceSendId(state) {
    return state.services[config.SOCIAL_SERVICE.ID].sendId
  },
  socialServiceReceiveId(state) {
    return state.services[config.SOCIAL_SERVICE.ID].receiveId
  },
  bankServiceSendId(state) {
    return state.services[config.BANK_SERVICE.ID].sendId
  },
  bankServiceReceiveId(state) {
    return state.services[config.BANK_SERVICE.ID].receiveId
  },
  guildServiceSendId(state) {
    return state.services[config.GUILD_SERVICE.ID].sendId
  },
  guildServiceReceiveId(state) {
    return state.services[config.GUILD_SERVICE.ID].receiveId
  },
  serverMsg(state) {
    return state.serverMsg
  },
  heartbeatTimes(state) {
    return state.heartbeatTimes
  },
  services(state) {
    return state.services
  },
  loginStep(state) {
    return state.loginStep
  },
  healthyData(state) {
    return state.healthyData
  },
  latency(state) {
    return state.latency
  },
  failedTimes(state) {
    return state.failedTimes
  },
  serverTimestamp(state) {
    return state.serverTimestamp
  },
  socketRetryTimes(state) {
    return state.socketRetryTimes
  }
}

export const mutations = {
  RESET_BANK_SERVICE_SEND_ID(state) {
    state.services[config.BANK_SERVICE.ID].sendId = 0
  },
  SET_HEARTBEAT(state, data) {
    clearInterval(state.heartbeat)
    state.heartbeat = data
  },
  CLEAR_HEARTBEAT(state) {
    clearInterval(state.heartbeat)
    Vue.set(state, 'heartbeat', null)
  },
  SET_SERVER_MSG(state, data) {
    state.serverMsg = data
  },
  SET_HEARTBEAT_TIMES(state, data) {
    state.heartbeatTimes = data
  },
  SET_SERVICE_IDS(
    state,
    { serviceId, enable = undefined, sendId = undefined, receiveId = undefined }
  ) {
    if (enable != undefined) state.services[serviceId].enable = enable
    if (sendId != undefined) state.services[serviceId].sendId = sendId
    if (receiveId != undefined) state.services[serviceId].receiveId = receiveId
  },
  SET_LOGIN_STEP(state, data) {
    state.loginStep = data
  },

  SET_HEALTHY_DATA(state, data) {
    state.healthyData = data
  },
  SET_LATENCY(state, data) {
    state.latency = data
  },
  SET_FAILED_TIMES(state, data) {
    state.failedTimes = data
  },
  SET_SERVER_TIMESTAMP(state, data) {
    state.serverTimestamp = data
  },
  SET_SOCKET_RETRY_TIMES(state, data) {
    state.socketRetryTimes = data
  },

  RESET(state) {
    Vue.set(state, 'serverMsg', '')
    Vue.set(state, 'heartbeatTimes', 0)
    clearInterval(state.heartbeat)
    Vue.set(state, 'heartbeat', null)
    Vue.set(state, 'loginStep', 0)
    Vue.set(state, 'services', {
      [config.LOGIN_SERVICE.ID]: {
        enable: false,
        sendId: 0,
        receiveId: 0
      },
      [config.LOTTERY_SERVICE.ID]: {
        enable: false,
        sendId: 0,
        receiveId: 0
      },
      [config.GAME_SERVICE.ID]: {
        enable: false,
        sendId: 0,
        receiveId: 0
      },
      [config.SOCIAL_SERVICE.ID]: {
        enable: false,
        sendId: 0,
        receiveId: 0
      },
      [config.GUILD_SERVICE.ID]: {
        enable: false,
        sendId: 0,
        receiveId: 0
      },
      [config.BANK_SERVICE.ID]: {
        enable: false,
        sendId: 0,
        receiveId: 0
      },
      [config.WEB_GAME_SERVICE.ID]: {
        enable: false,
        sendId: 0,
        receiveId: 0
      }
    })
    Vue.set(state, 'healthyData', '')
    Vue.set(state, 'latency', 0)
    Vue.set(state, 'failedTimes', 0)
    Vue.set(state, 'serverTimestamp', 0)
    Vue.set(state, 'socketRetryTimes', 0)
  }
}

export const actions = {
  async receiveNextId({ commit }, { serviceId, id }) {
    commit('SET_SERVICE_IDS', { serviceId: serviceId, receiveId: id })
  },
  async sendNextId({ commit, getters }, serviceId) {
    let sendId = getters['services'][serviceId].sendId
    if (sendId >= SEND_MAX_ID) {
      sendId = 0
    }
    sendId++
    commit('SET_SERVICE_IDS', { serviceId: serviceId, sendId })
  },
  async initReceived({ commit, dispatch, getters, rootGetters }) {
    // 接收伺服器主動推播的資訊
    this.$wsClient.receivedListeners.add((data) => {
      if (data.commandId === 1) {
        if (data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.GET)) {
          // 初始化MAIL 是一筆一筆送增加該判斷避免洗版
          if (!rootGetters['mail/getFirstRecive']) {
            commit('SET_SERVER_MSG', 'new_mail_revice_noty')
          }

          dispatch('mail/init', data, { root: true })
        }
      } else if (data.commandId === 4) {
        if (data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.LEVEL)) {
          dispatch('role/updateLevel', data.level, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.MONEY)) {
          commit('role/SET_BALANCE', data.money, { root: true })
        }
      } else if (data.commandId === 20) {
        if (data.isFeature(this.$xinConfig.FEATURE.FRIEND.TYPE.OFFLINE)) {
          data.online = false
          if (rootGetters['social/friendList'].find((item) => item.username === data.username))
            dispatch('social/setSingleFriendStatus', data, { root: true })
          else dispatch('social/setSingleBlockStatus', data, { root: true })
        }
      } else if (data.commandId === 21) {
        if (data.isFeature(this.$xinConfig.FEATURE.FRIEND.TYPE.ONLINE)) {
          data.online = true
          commit('SET_SERVER_MSG', 'friend_online')
          if (rootGetters['social/friendList'].find((item) => item.username === data.username))
            dispatch('social/setSingleFriendStatus', data, { root: true })
          else dispatch('social/setSingleBlockStatus', data, { root: true })
        }
      } else if (data.commandId === 29) {
        if (data.isFeature(this.$xinConfig.FEATURE.CUSTOMER_SERVICE.ID)) {
          dispatch('chat/addCustomerServiceMsg', data, { root: true })
        }
      } else if (data.commandId === 31) {
        if (data.isFeature(this.$xinConfig.FEATURE.MAIL.ID)) {
          dispatch('social/init', data, { root: true })
        }
      } else if (data.protocolId === 33) {
        if (data.isFeature(this.$xinConfig.FEATURE.LOGOUT.ID)) {
          if (data.serviceId !== this.$xinConfig.LOGIN_SERVICE.ID) {
            let times = getters['failedTimes'] + 1
            commit('SET_FAILED_TIMES', times)
            if (times <= 10) {
              let receiveId = getters['services'][data.serviceId].receiveId
              // console.log('receiveId', receiveId)
              this.$wsClient.send(
                this.$wsPacketFactory.getServiceJoin(data.serviceId, receiveId + 1)
              )
              this.$xinUtility
                .waitEvent(
                  this.$wsClient.receivedListeners,
                  (data) =>
                    data.protocolId == this.$xinConfig.PROTOCOL_ID.REJOINED_SERVICE &&
                    data.serviceId
                )
                .then(async (data) => {
                  commit('SET_SERVICE_IDS', { serviceId: data.serviceId, sendId: data.sendId })
                })
                .catch(() => {
                  if (times >= 10) {
                    commit('SET_SERVER_MSG', 'server_kick_noty2')
                  }
                })
            }
          } else if (getters['loginStep'] === 1) {
            commit('SET_SERVER_MSG', 'server_kick_noty2')
          }

          try {
            commit('SET_SERVICE_IDS', {
              serviceId: data.serviceId,
              enable: false
            })
          } catch (err) {
            console.log(err)
          }
        }
      } else if (data.commandId === 98) {
        if (data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.VIP)) {
          commit('role/SET_VIPLEVEL', data.vip, { root: true })
        }
      } else if (data.protocolId === 112) {
        dispatch('role/saveToken', data, { root: true })
        commit(
          'SET_HEARTBEAT',
          setInterval(() => {
            dispatch('sendAlive')
            dispatch('updateLatency')
          }, 6000)
        )
      } else if (data.commandId === 135) {
        //多個登入出現，伺服器主動踢出前面登入的裝置
        if (data.action === 1) {
          commit('SET_SERVER_MSG', data.message)
        }
      } else if (data.type === 17) {
        //目前不接收系統訊息 1:系統 2:頻道 3:密語 4:全服
        // if (data.messageType === 1) {
        //   dispatch('chat/addGlobalChat', { payload: data }, { root: true })
        //   dispatch('chat/addChannelChat', { payload: data }, { root: true })
        // }
        if (data.messageType === 2)
          dispatch('chat/addChannelChat', { payload: data, avatar: true }, { root: true })
        else if (data.messageType === 3)
          dispatch('chat/addWhisperChat', { payload: data, avatar: true }, { root: true })
        else if (data.messageType === 4)
          dispatch('chat/addGlobalChat', { payload: data, avatar: true }, { root: true })
      } else if (data.type === 18) {
        if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.INFO)) {
          dispatch('chat/setCurrentChannelDetail', data, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.LIST)) {
          dispatch('chat/setChannelDetail', data, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.REMOVE)) {
          console.log('REMOVE:', data)
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.UPDATE)) {
          dispatch('chat/updateChannels', data.channels, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.USER_EXIT)) {
          commit('chat/REMOVE_USER_FROM_CHANNEL', data.users, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.USER_JOIN)) {
          commit('chat/ADD_USER_TO_CHANNEL', data.users, { root: true })
        }
      } else if (data.type === 25) {
        //公告訊息與星城遊戲訊息混在一起 目前不接收
        const addAnnouncementStatus = false
        if (addAnnouncementStatus) dispatch('chat/addAnnouncement', data, { root: true })
      } else if (data.type === 72) {
        if (data.hasGuild && data.members && data.members.length > 0) {
          commit('social/SET_GUILD_MEMBER_LIST', data.members, { root: true })
          commit('role/SET_GUILD_NAME', data.guildName, { root: true })
        }
      } else if (data.type === 100) {
        let isEnable = data.gameIds.find((element) => element == BANK_GAME_ID) != undefined
        commit(`${STATION}/payment/SET_BANK_ENABLE`, isEnable, { root: true })
      } else if (data.type == 250) {
        if (data.commandId === 150) {
          if (data.isFeature(this.$xinConfig.FEATURE.LOGIN.ALLOW_LIST)) {
            commit('role/SET_DEVICE_LIST', data.deviceList, { root: true })
          }
        } else if (data.commandId === 151) {
          if (data.isFeature(this.$xinConfig.FEATURE.LOGIN.LOGIN_RECORD)) {
            commit('role/SET_LOGIN_RECORD', data.records, { root: true })
          }
        }
      } else if (data.protocolId === 255) {
        // 因為是每三秒收到一次包，所以要-0.5
        commit('SET_HEARTBEAT_TIMES', getters['heartbeatTimes'] - 0.5)
      }
      // if (data.protocolId !== 255 && data.protocolId !== 38) {
      //   // 不顯示 健康檢查log
      //   console.log('debug data', data)
      // }
    })

    this.$wsClient.closedListeners.add(async (event) => {
      console.log('WEB SOCKET CLOSE!!!', event)
      if (event.code == 1006) {
        this.$wsClient.endPoint.next()
      }
      let retryTimes = getters['socketRetryTimes']
      do {
        let endPoint = this.$wsClient.endPoint.current
        console.log('重新連線到:' + endPoint)

        if (retryTimes > 5) {
          commit('SET_SERVER_MSG', 'server_kick_noty2')
          return
        } else {
          retryTimes++
          commit('SET_SOCKET_RETRY_TIMES', retryTimes)
        }
        try {
          await this.$wsClient.disconnect(1000)
          await this.$wsClient.connect(endPoint)
        } catch (err) {
          console.log('CATCH ERR', err)
          this.$wsClient.endPoint.next()
        }
      } while (!this.$wsClient.isConnected)

      commit('SET_SOCKET_RETRY_TIMES', retryTimes)
      let serviceIds = await dispatch('getEnabledServices')
      if (serviceIds) {
        let connectionInfo = rootGetters['role/connInfo']
        // console.log('CONNECTION_INFO: ', connectionInfo)
        this.$wsClient.send(
          this.$wsPacketFactory.reconnectionPacket(
            serviceIds[0],
            connectionInfo.clientId,
            connectionInfo.token
          )
        )

        await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          if (data.protocolId == this.$xinConfig.PROTOCOL_ID.RECONNECT) {
            return true
          }
        })

        // 重連當前服務
        let services = getters['services']
        for (let i = 0; i < serviceIds.length; i++) {
          try {
            let serviceId = serviceIds[i]
            let receiveId = services[serviceId].receiveId
            this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(serviceId, receiveId + 1))
            await this.$xinUtility
              .waitEvent(
                this.$wsClient.receivedListeners,
                (data) => data.protocolId == this.$xinConfig.PROTOCOL_ID.REJOINED_SERVICE
              )
              .then(async (data) => {
                commit('SET_SERVICE_IDS', {
                  serviceId: data.serviceId,
                  sendId: data.sendId - 1
                })
              })
              .catch(() => {
                throw 'err in reconnection'
              })
          } catch (err) {
            commit('SET_SERVER_MSG', 'server_kick_noty2')
            return
          }
        }

        commit('SET_HEARTBEAT_TIMES', 0)
      }
    })
  },
  async updateLatency(ctx) {
    // 更新 latency (ms)
    let date = new Date()
    this.$wsClient.send(this.$wsPacketFactory.serverEcho(date.getTime().toString()))
    this.$xinUtility
      .waitEvent(
        this.$wsClient.receivedListeners,
        (data) => data.protocolId == this.$xinConfig.PROTOCOL_ID.ECHO
      )
      .then((data) => {
        ctx.commit('SET_LATENCY', data.latency)
      })
      .catch(() => {
        ctx.commit('SET_LATENCY', undefined)
      })
  },
  async sendAlive(ctx) {
    // console.log('heartbeatTimes', ctx.getters['heartbeatTimes'])
    ctx.commit('SET_HEARTBEAT_TIMES', ctx.getters['heartbeatTimes'] + 1)
    this.$wsClient.send(this.$wsPacketFactory.getAlive())

    // 服務器 Echo
    let serviceIds = await ctx.dispatch('getEnabledServices')
    for (let i = 0; i < serviceIds.length; i++) {
      this.$wsClient.send(this.$wsPacketFactory.healthyCheck(serviceIds[i]))
    }
  },
  getEnabledServices({ getters }) {
    let services = getters['services']
    let result = []
    for (let serviceId in services) {
      if (services[serviceId].enable) {
        result.push(Number(serviceId))
      }
    }
    return result
  },
  async getServerLocalTimeDiff({ commit }) {
    try {
      this.$wsClient.send(this.$wsPacketFactory.healthyCheck(this.$xinConfig.SOCIAL_SERVICE.ID))
      const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
        return (
          data.protocolId === 30 &&
          data.type === 99 &&
          data.serviceId === this.$xinConfig.SOCIAL_SERVICE.ID
        )
      })
      const localTime = new Date().getTime()
      const serverTime = res.serverTime.getTime()
      const timeDiff = localTime - serverTime
      commit('SET_SERVER_TIMESTAMP', timeDiff)
    } catch (error) {
      console.error('Failed to get server time:', error)
      commit('SET_SERVER_TIMESTAMP', 0)
    }
  }
}
