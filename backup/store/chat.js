// 此為有好友與聊天標籤且星城頻道
import Vue from 'vue'
import cloneDeep from 'lodash/cloneDeep'
import SpeexDecoder from '~/plugins/NSpeex/NSpeex.Decoder.js'
import BandMode from '~/plugins/NSpeex/BandMode.js'

const globalTitle = 'global_chat'
const channelTitle = 'chat_session'
//用於判別星城所回傳密語之暱稱
const officialName = '官方-'
const checkMsgType = (msg) =>
  msg.content.type === 3 || msg.content.type === 4 || msg.content.type === 8
const itemLimit = 500

function decompressAndTranscodeAudio(Data) {
  if (!Data) return

  // 先转成 Uint8Array
  let U8Arr = new Uint8Array(Data)
  // 建立 Int16Array
  let I16Arr = new Int16Array(U8Arr.length * 4)

  let SpDecoder = new SpeexDecoder(BandMode.Narrow, false)
  try {
    SpDecoder.Decode(U8Arr, 0, U8Arr.length, I16Arr, 0, false) // 解压缩
  } catch (e) {
    U8Arr = I16Arr = SpDecoder = null
    return
  }
  let F32Arr = new Float32Array(I16Arr) // 建立信号格式所需的数据数组
  for (let i = 0; i < F32Arr.length; i++) {
    if (F32Arr[i] !== 0) F32Arr[i] = F32Arr[i] / 32767.0 // 转换信号值 -1~1
  }

  return F32Arr
}

export const state = () => ({
  chatTabItems: [
    { title: 'chat_session', id: 0 },
    { title: 'friend', id: 1 }
  ],
  chats: [
    {
      title: globalTitle,
      id: 0,
      img: 'allChannel.png',
      noty: 0,
      isOfficial: true,
      key: globalTitle,
      isBlock: false,
      isFriend: false,
      lastUpdate: new Date()
    },
    {
      title: '',
      id: 1,
      img: 'channel.png',
      noty: 0,
      isOfficial: true,
      key: channelTitle,
      isBlock: false,
      isFriend: false,
      lastUpdate: new Date()
    }
  ],
  channels: [],
  currentChannelDetail: {
    name: '',
    users: []
  },
  msg: {},
  customerServiceMsg: [],
  allChatNoty: 0,
  whisperNoty: 0,
  customerServiceNoty: 0,
  setting: {
    opacity: 0.6,
    global: {
      stiker: true,
      customStiker: true,
      voice: true,
      autoVoice: true
    },
    channel: {
      stiker: true,
      customStiker: true,
      voice: true,
      autoVoice: true
    },
    whisper: {
      stiker: true,
      customStiker: true,
      voice: true,
      autoVoice: true,
      notySound: true,
      onlyFriend: true
    }
  },
  isKeyboardOpen: false,
  cacheAvatar: {}
})

export const getters = {
  chatTabItems(state) {
    return state.chatTabItems
  },
  chats(state) {
    return state.chats
  },
  channels(state) {
    return state.channels
  },
  msg(state) {
    return state.msg
  },
  customerServiceMsg(state) {
    return state.customerServiceMsg
  },
  currentChannelDetail(state) {
    return state.currentChannelDetail
  },
  allChatNoty(state) {
    return state.allChatNoty
  },
  whisperNoty(state) {
    return state.whisperNoty
  },
  customerServiceNoty(state) {
    return state.customerServiceNoty
  },
  setting(state) {
    return state.setting
  },
  opacity(state) {
    return state.setting.opacity
  },
  globalSetting(state) {
    return state.setting.global
  },
  channelSetting(state) {
    return state.setting.channel
  },
  whisperSetting(state) {
    return state.setting.whisper
  },
  isKeyboardOpen(state) {
    return state.isKeyboardOpen
  },
  cacheAvatar(state) {
    return state.cacheAvatar
  }
}

export const mutations = {
  SET_CHAT_TAB_ITEMS(state, payload) {
    state.chatTabItems = payload
  },
  SET_CHATS(state, payload) {
    state.chats = payload
  },
  ADD_CHATS(state, payload) {
    state.chats.push(payload)
  },
  SET_CHAT_IS_BLOCK(state, { key, payload }) {
    const index = state.chats.findIndex((item) => item.key === key)

    if (index !== -1) {
      state.chats[index].isBlock = payload
    }
  },
  SET_CHAT_IS_FRIEND(state, { key, payload }) {
    const index = state.chats.findIndex((item) => item.key === key)

    if (index !== -1) {
      state.chats[index].isFriend = payload
    }
  },
  ADD_CHANNELS(state, payload) {
    state.channels.push(payload)
  },
  SET_MSG(state, { title, payload }) {
    //因為msg是object，所以要用Vue.set才能觸發更新
    Vue.set(state.msg, title, payload)
  },
  ADD_MSG(state, { title, payload }) {
    state.msg[title].push(payload)
    if (state.msg[title].length > itemLimit) {
      state.msg[title].shift()
    }
  },
  ADD_CUSTOMER_SERVICE_MSG(state, payload) {
    state.customerServiceMsg.push(payload)
  },
  SET_CHAT_ONLINE_STATUS_BY_ID(state, payload) {
    let index = state.chats.findIndex((item) => item.id === payload.id)
    if (index !== -1) {
      state.chats[index].online = payload.online
    }
  },
  SET_CHAT_ONLINE_STATUS_BY_NAME(state, payload) {
    let index = state.chats.findIndex((item) => item.title === payload.name)
    if (index !== -1) {
      state.chats[index].online = payload.online
    }
  },
  SET_NOTY(state, { index, payload }) {
    state.chats[index].noty = payload
  },
  SET_CURRENT_CHANNELS_DETAIL(state, payload) {
    state.currentChannelDetail.name = payload.name
    state.currentChannelDetail.users = payload.users
  },
  SET_CURRENT_CHANNELS_NAME(state, payload) {
    state.chats[1].title = payload
  },
  UPDATE_CHANNEL_POPULATION(state, payload) {
    let channel = state.channels[payload.index]
    if (channel.population > payload.population) {
      channel.popIsUp = false
    } else if (channel.population < payload.population) {
      channel.popIsUp = true
    }
    channel.population = payload.population
  },
  SORT_CHANNEL(state, payload) {
    const selfName = payload.selfName
    function sortChannel(a, b) {
      // 第一優先條件：如果頻道名稱與自己的角色名稱一樣，排在前面
      if (a.name === selfName && b.name !== selfName) {
        return -1
      }
      if (!a.name !== selfName && b.name === selfName) {
        return 1
      }

      // 第二條件：如果頻道名稱開頭為 *，排在前面
      if (a.name.startsWith('*') && !b.name.startsWith('*')) {
        return -1
      }
      if (!a.name.startsWith('*') && b.name.startsWith('*')) {
        return 1
      }

      // 第三條件：按照人數排序（不包含"星城頻道"開頭的）
      if (!a.name.startsWith('星城頻道') && !b.name.startsWith('星城頻道')) {
        return b.population - a.population
      }

      return 0 // 其他情況不變
    }

    state.channels = payload.channels.sort(sortChannel)
  },
  ADD_USER_TO_CHANNEL(state, payload) {
    if (payload.length !== 0)
      payload.forEach((user) => {
        const userExist = state.currentChannelDetail.users.find((channelUser) => {
          return user.username === channelUser.username
        })
        if (!userExist) state.currentChannelDetail.users.push(user)
      })
  },
  REMOVE_USER_FROM_CHANNEL(state, payload) {
    if (payload.length !== 0)
      payload.forEach((user) => {
        state.currentChannelDetail.users.forEach((channelUser, index) => {
          if (user.username === channelUser.username) {
            state.currentChannelDetail.users.splice(index, 1)
          }
        })
      })
  },
  SET_ALL_CHAT_NOTY(state, payload) {
    state.allChatNoty = payload
  },
  ADD_ALL_CHAT_NOTY(state, payload) {
    state.allChatNoty += payload
  },
  SET_WHISPER_NOTY(state, payload) {
    state.whisperNoty = payload
  },
  ADD_WHISPER_NOTY(state, payload) {
    state.whisperNoty += payload
  },
  ADD_CUSTOMER_SERVICE_NOTY(state, payload) {
    state.customerServiceNoty += payload
  },
  SET_CUSTOMER_SERVICE_NOTY(state, payload) {
    state.customerServiceNoty = payload
  },
  CLEAR_ALL_CHAT_NOTY(state) {
    state.allChatNoty = 0
  },
  CLEAR_CUSTOMER_SERVICE_NOTY(state) {
    state.customerServiceNoty = 0
  },
  SET_SETTING(state, payload) {
    state.setting = payload
  },
  SET_OPACITY(state, payload) {
    state.setting.opacity = payload
  },
  SET_GLOBAL_STIKER(state, payload) {
    state.setting.global.stiker = payload
  },
  SET_GLOBAL_CUSTOM_STIKER(state, payload) {
    state.setting.global.customStiker = payload
  },
  SET_GLOBAL_VOICE(state, payload) {
    state.setting.global.voice = payload
  },
  SET_GLOBAL_AUTO_VOICE(state, payload) {
    state.setting.global.autoVoice = payload
  },
  SET_CHANNEL_STIKER(state, payload) {
    state.setting.channel.stiker = payload
  },
  SET_CHANNEL_CUSTOM_STIKER(state, payload) {
    state.setting.channel.customStiker = payload
  },
  SET_CHANNEL_VOICE(state, payload) {
    state.setting.channel.voice = payload
  },
  SET_CHANNEL_AUTO_VOICE(state, payload) {
    state.setting.channel.autoVoice = payload
  },
  SET_WHISPER_STIKER(state, payload) {
    state.setting.whisper.stiker = payload
  },
  SET_WHISPER_CUSTOM_STIKER(state, payload) {
    state.setting.whisper.customStiker = payload
  },
  SET_WHISPER_VOICE(state, payload) {
    state.setting.whisper.voice = payload
  },
  SET_WHISPER_AUTO_VOICE(state, payload) {
    state.setting.whisper.autoVoice = payload
  },
  SET_WHISPER_NOTY_SOUND(state, payload) {
    state.setting.whisper.notySound = payload
  },
  SET_WHISPER_ONLY_FRIEND(state, payload) {
    state.setting.whisper.onlyFriend = payload
  },
  SET_IS_REAED(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].isRead = payload.status
      }
    }
  },
  SET_CUSTOMER_SERVICE_IS_REAED(state, payload) {
    let index = state.customerServiceMsg.findIndex((item) => item.id === payload.id)
    if (index !== -1) {
      state.customerServiceMsg[index].isRead = payload.status
    }
  },
  CLEAR_ALL_MSG(state) {
    for (const key in state.msg) {
      Vue.set(state.msg, key, [])
    }
    state.chats.forEach((chat) => {
      chat.noty = 0
    })
    this.commit('social/CLEAR_FRIEND_ALL_NOTY', null, { root: true })
    mutations.CLEAR_ALL_CHAT_NOTY(state)
  },
  SET_CACHE_AVATAR(state, payload) {
    Vue.set(state.cacheAvatar, payload.name, payload.data)
  },
  SET_IS_PLAYING(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.isPlaying = payload.isPlaying
      }
    }
  },
  SET_STICKER(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.stickerId = payload.stickerId
      }
    }
  },
  SET_STICKER_ERROR(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.errorStatus = payload.errorStatus
      }
    }
  },
  SET_CUSTOM_IMG(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.imageUrl = payload.imageUrl
        state.msg[payload.key][index].msg.thumbUrl = payload.thumbUrl
      }
    }
  },
  SET_CHANNEL_IMG(state, payload) {
    const index = state.channels.findIndex((item) => item.name === payload.name)
    if (index !== -1) {
      state.channels[index].thumbUrl = payload.thumbUrl
    }
  },
  UPDATE_AUDIO_BUFFER(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.audioBuffer = payload.audioBuffer
      }
    }
  },
  UPDATE_DURATION(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.duration = payload.duration
      }
    }
  },
  UPDATE_GET_AUDIO_BUFFER_STATUS(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.getAudioBufferStatus = payload.getAudioBufferStatus
      }
    }
  },
  UPDATE_CHAT_LAST_UPDATE(state, payload) {
    const index = state.chats.findIndex((item) => item.title === payload.key)
    if (index !== -1) {
      state.chats[index].lastUpdate = payload.lastUpdate
    }
  },
  SET_IS_KEYBOARD_OPEN(state, payload) {
    state.isKeyboardOpen = payload
  },
  RESET(state) {
    Vue.set(state, 'channels', [])
    Vue.set(state, 'msg', {})
    Vue.set(state, 'chats', [
      {
        title: globalTitle,
        id: 0,
        img: 'allChannel.png',
        noty: 0,
        isOfficial: true,
        key: globalTitle,
        isBlock: false,
        isFriend: false,
        lastUpdate: new Date()
      },
      {
        title: '',
        id: 1,
        img: 'channel.png',
        noty: 0,
        isOfficial: true,
        key: channelTitle,
        isBlock: false,
        isFriend: false,
        lastUpdate: new Date()
      }
    ])
    Vue.set(state, 'currentChannelDetail', { name: '', users: [] })
    Vue.set(state, 'allChatNoty', 0)
    Vue.set(state, 'whisperNoty', 0)
    Vue.set(state, 'customerServiceNoty', 0)
    Vue.set(state, 'customerServiceMsg', [])
    Vue.set(state, 'isKeyboardOpen', false)
    Vue.set(state, 'cacheAvatar', {})
  }
}
export const actions = {
  async addGlobalChat({ state, dispatch, commit, rootGetters }, { payload, avatar }) {
    if (checkMsgType(payload)) {
      const isBlock = await dispatch('isBlock', payload.user.name)
      if (!isBlock) {
        const selfName = rootGetters['role/userName']
        const msg = await dispatch('createMsg', { payload, avatar, type: 'global' })
        const isSelf = payload.user.name === selfName

        if (globalTitle in state.msg) {
          commit('ADD_MSG', { title: globalTitle, payload: msg })
        } else {
          commit('SET_MSG', { title: globalTitle, payload: [msg] })
        }

        commit('UPDATE_CHAT_LAST_UPDATE', { key: globalTitle, lastUpdate: new Date() })

        if (!isSelf) {
          commit('SET_NOTY', { index: 0, payload: state.chats[0].noty + 1 })
          dispatch('countAllChatNoty')
        }
      }
    }
  },
  async addChannelChat({ state, dispatch, commit, rootGetters }, { payload, avatar }) {
    if (checkMsgType(payload)) {
      const isBlock = await dispatch('isBlock', payload.user.name)
      if (!isBlock) {
        const selfName = rootGetters['role/userName']
        const msg = await dispatch('createMsg', { payload, avatar, type: 'channel' })
        const isSelf = payload.user.name === selfName
        const isSystemMsg = payload.messageType === 1

        if (channelTitle in state.msg) {
          commit('ADD_MSG', { title: channelTitle, payload: msg })
        } else {
          commit('SET_MSG', { title: channelTitle, payload: [msg] })
        }

        commit('UPDATE_CHAT_LAST_UPDATE', { key: channelTitle, lastUpdate: new Date() })

        if (!isSelf && !isSystemMsg) {
          commit('SET_NOTY', { index: 1, payload: state.chats[1].noty + 1 })
          dispatch('countAllChatNoty')
        }
      }
    }
  },
  async addWhisperChat({ state, rootGetters, dispatch, commit }, { payload, avatar }) {
    if (checkMsgType(payload)) {
      const isBlock = await dispatch('isBlock', payload.user.name)
      if (!isBlock) {
        const dmText = '密給'
        const selfName = rootGetters['role/userName']
        const msg = await dispatch('createMsg', { payload, avatar, type: 'whisper' })
        let chatIndex = -1
        let friendIndex = -1
        let isFriend = false
        const friendList = rootGetters['social/friendList']
        const isIncludeDMText = payload.content.allMessage.includes(dmText)
        const isSelf = isIncludeDMText ? msg.user.name === selfName : payload.user.name === selfName

        //check if friend
        friendList.forEach((friend, index) => {
          if (friend.username === payload.user.name) {
            friendIndex = index
          }
        })
        isFriend = friendIndex !== -1

        let isInChat = false

        state.chats.forEach((chat, index) => {
          if (payload.user.name === chat.title) {
            isInChat = true
            chatIndex = index
          }
        })

        if (!isInChat) {
          const chat = {
            title: isIncludeDMText ? payload.user.name : msg.user.name,
            id: state.chats.length,
            img: isFriend ? friendList[friendIndex].thumbUrl : msg.img,
            noty: isIncludeDMText ? 0 : 1,
            isOfficial: msg.user.name.includes(officialName) ? true : false,
            key: isIncludeDMText ? payload.user.name : msg.user.name,
            online: true,
            isBlock: isBlock,
            isFriend: isFriend,
            lastUpdate: new Date()
          }
          commit('ADD_CHATS', chat)
        }
        //add noty & update lastupdate
        else {
          if (chatIndex !== -1 && !isSelf) {
            commit('SET_NOTY', { index: chatIndex, payload: state.chats[chatIndex].noty + 1 })
          }
          commit('UPDATE_CHAT_LAST_UPDATE', {
            key: isIncludeDMText ? payload.user.name : msg.user.name,
            lastUpdate: new Date()
          })
        }

        //add friend noty
        if (isFriend) {
          if (!isSelf)
            commit(
              'social/SET_FRIEND_NOTY',
              { username: payload.user.name, noty: friendList[friendIndex].noty + 1 },
              { root: true }
            )
          commit(
            'social/SET_FRIEND_LAST_UPDATE',
            { username: payload.user.name, lastUpdate: new Date() },
            { root: true }
          )
        }
        dispatch('countAllChatNoty')
        dispatch('countWhisperNoty')
        if (payload.user.name in state.msg) {
          commit('ADD_MSG', { title: payload.user.name, payload: msg })
        } else {
          commit('SET_MSG', { title: payload.user.name, payload: [msg] })
        }
      }
    }
  },
  async addAnnouncement({ state, dispatch, commit }, payload) {
    payload.content = {}
    payload.user = {}
    payload.user.color = {}
    payload.content.color = {}
    payload.user.name = 'system'
    payload.content.message = payload.message
    payload.content.type = 3
    payload.user.color.red = 255
    payload.user.color.blue = 255
    payload.user.color.green = 255
    payload.content.color.red = 255
    payload.content.color.blue = 255
    payload.content.color.green = 255
    payload.messageType = 1
    const msg = await dispatch('createMsg', { payload, type: 'announcementMsg' })

    if (globalTitle in state.msg) {
      commit('ADD_MSG', { title: globalTitle, payload: cloneDeep(msg) })
    } else {
      commit('SET_MSG', { title: globalTitle, payload: [cloneDeep(msg)] })
    }

    if (channelTitle in state.msg) {
      commit('ADD_MSG', { title: channelTitle, payload: cloneDeep(msg) })
    } else {
      commit('SET_MSG', { title: channelTitle, payload: [cloneDeep(msg)] })
    }
    commit('SET_NOTY', { index: 0, payload: state.chats[0].noty + 1 })
    commit('SET_NOTY', { index: 1, payload: state.chats[1].noty + 1 })
    dispatch('countAllChatNoty')
  },
  async addCustomerServiceMsg({ state, dispatch, commit, rootGetters }, payload) {
    const selfName = rootGetters['role/userName']
    //用於判別星城所回傳密語之暱稱
    const customerText = '【線上客服】'
    const isSelf = payload.message.startsWith(selfName)
    const isCustomerServiceOrSelf = payload.message.startsWith(customerText) || isSelf
    if (isCustomerServiceOrSelf) {
      const colonIndex = payload.message.indexOf('：')

      if (colonIndex !== -1) {
        const firstPart = payload.message.substring(0, colonIndex)
        const secondPart = payload.message.substring(colonIndex + 1)

        payload.content = {}
        payload.user = {}
        payload.user.color = {}
        payload.content.color = {}
        payload.user.name = firstPart
        payload.content.message = secondPart
        payload.content.type = 3
        payload.user.color.red = payload.color.r
        payload.user.color.blue = payload.color.b
        payload.user.color.green = payload.color.g
        payload.content.color.red = payload.color.r
        payload.content.color.blue = payload.color.b
        payload.content.color.green = payload.color.g
        payload.messageType = null
      }
    } else {
      payload.content = {}
      payload.user = {}
      payload.user.color = {}
      payload.content.color = {}
      payload.user.name = 'system'
      payload.content.message = payload.message
      payload.content.type = 3
      payload.user.color.red = payload.color.r
      payload.user.color.blue = payload.color.b
      payload.user.color.green = payload.color.g
      payload.content.color.red = payload.color.r
      payload.content.color.blue = payload.color.b
      payload.content.color.green = payload.color.g
      payload.messageType = 1
    }
    const msg = await dispatch('createMsg', { payload, type: 'customerServiceMsg' })
    commit('ADD_CUSTOMER_SERVICE_MSG', msg)

    if (payload.message.startsWith(customerText))
      commit('ADD_CUSTOMER_SERVICE_NOTY', state.customerServiceNoty + 1)
  },
  async createMsg({ dispatch, commit, rootGetters }, { payload, avatar, type }) {
    let thumbUrl = ''
    let userName = payload.user.name
    let msg = {}

    const selfName = rootGetters['role/userName']
    const selfAvatar = rootGetters['role/thumbUrl']

    if (
      (type === 'whisper' && payload.content.allMessage.includes('密給')) ||
      userName === selfName
    ) {
      thumbUrl = selfAvatar
      userName = selfName
    } else if (type === 'customerServiceMsg' || type === 'announcementMsg') {
      thumbUrl = null
    } else if (avatar) {
      thumbUrl = await dispatch('role/getThumbUrl', userName, { root: true })
    }
    // 須注意，目前觀察server回傳#FF0000(紅色)的文字實際上應顯示為白色
    const isRed =
      payload.content.color.red === 255 &&
      payload.content.color.blue === 0 &&
      payload.content.color.green === 0

    const whiteColor = {
      red: 255,
      blue: 255,
      green: 255
    }

    //check if block
    const isBlock = await dispatch('isBlock', payload.user.name)
    if (payload.content.type === 3) {
      msg = {
        id: Date.now(),
        messageType: payload.messageType,
        user: {
          name: userName,
          color: payload.user.color
        },
        date: new Date(),
        msg: {
          color: isRed ? whiteColor : payload.content.color,
          message: payload.content.message,
          type: payload.content.type
        },
        isRead: isBlock ? true : false,
        isBlock: isBlock
      }
    } else if (payload.content.type === 4) {
      msg = {
        id: Date.now(),
        messageType: payload.messageType,
        user: {
          name: userName,
          color: payload.user.color
        },
        date: new Date(),
        msg: {
          color: isRed ? whiteColor : payload.content.color,
          stickerId: payload.content.stickerId,
          type: payload.content.type,
          errorStatus: false
        },
        isRead: isBlock ? true : false,
        isBlock: isBlock
      }
    } else if (payload.content.type === 8) {
      if (payload.content.dataType === 1) {
        msg = {
          id: Date.now(),
          messageType: payload.messageType,
          user: {
            name: userName,
            color: payload.user.color
          },
          date: new Date(),
          msg: {
            color: isRed ? whiteColor : payload.content.color,
            imageUrl: payload.content.imageUrl,
            thumbUrl: payload.content.thumbUrl,
            type: payload.content.type,
            dataType: payload.content.dataType
          },
          isRead: isBlock ? true : false,
          isBlock: isBlock
        }
      } else if (payload.content.dataType === 2) {
        const dateNow = Date.now()
        msg = {
          id: dateNow,
          messageType: payload.messageType,
          user: {
            name: userName,
            color: payload.user.color
          },
          date: new Date(),
          msg: {
            color: isRed ? whiteColor : payload.content.color,
            getAudioBufferStatus: false,
            audioBuffer: null,
            type: payload.content.type,
            dataType: payload.content.dataType,
            isPlaying: false,
            duration: 0
          },
          isRead: isBlock ? true : false,
          isBlock: isBlock
        }
        fetch(payload.content.voiceUrl).then((res) => {
          res.arrayBuffer().then((buffer) => {
            const audio = decompressAndTranscodeAudio(buffer)
            const audioContext = new (window.AudioContext || window.webkitAudioContext)()
            const audioBuffer = audioContext.createBuffer(1, audio.length, 7600)
            audioBuffer.copyToChannel(audio, 0)
            const key =
              type === 'global'
                ? globalTitle
                : type === 'channel'
                ? channelTitle
                : payload.user.name
            commit('UPDATE_AUDIO_BUFFER', {
              key: key,
              id: dateNow,
              audioBuffer: audioBuffer
            })
            commit('UPDATE_DURATION', {
              key: key,
              id: dateNow,
              duration: Math.ceil(audioBuffer.duration)
            })
            commit('UPDATE_GET_AUDIO_BUFFER_STATUS', {
              key: key,
              id: dateNow,
              getAudioBufferStatus: true
            })
          })
        })
      }
    }
    if (thumbUrl?.length !== 0) msg.img = thumbUrl

    return msg
  },
  async setCurrentChannelDetail({ commit }, payload) {
    commit('SET_CURRENT_CHANNELS_NAME', payload.name)
    commit('SET_CURRENT_CHANNELS_DETAIL', payload)
  },
  async countAllChatNoty({ commit, state, dispatch }) {
    let noty = 0
    for (let chatItem of state.chats) {
      if (
        chatItem.key === globalTitle ||
        chatItem.key === channelTitle ||
        (!chatItem.isBlock &&
          (!state.setting.whisper.onlyFriend || (await dispatch('isFriend', chatItem.key))))
      )
        noty += chatItem.noty
    }

    commit('SET_ALL_CHAT_NOTY', noty)
  },
  async countWhisperNoty({ commit, state, dispatch }) {
    let noty = 0
    for (let chatItem of state.chats) {
      if (
        chatItem.key !== globalTitle &&
        chatItem.key !== channelTitle &&
        !chatItem.isBlock &&
        (!state.setting.whisper.onlyFriend || (await dispatch('isFriend', chatItem.key)))
      )
        noty += chatItem.noty
    }

    commit('SET_WHISPER_NOTY', noty)
  },

  isBlock({ rootGetters }, payload) {
    const blockList = rootGetters['social/blockList']
    let isBlock = false
    blockList.forEach((block) => {
      if (block.username === payload) {
        isBlock = true
      }
    })
    return isBlock
  },
  isFriend({ rootGetters }, payload) {
    const friendList = rootGetters['social/friendList']
    let isFriend = false
    friendList.forEach((friend) => {
      if (friend.username === payload) {
        isFriend = true
      }
    })
    return isFriend
  },
  async setChannelDetail({ commit, dispatch, rootGetters }, payload) {
    const selfName = rootGetters['role/userName']
    await Promise.all(
      payload.channels.map((channel) => dispatch('role/getThumbUrl', channel.name, { root: true }))
    ).then((thumbUrls) => {
      for (let i = 0; i < thumbUrls.length; i++) {
        payload.channels[i].thumbUrl = thumbUrls[i]
      }
    })
    payload.channels.forEach((element) => {
      element.popIsUp = true
    })
    commit('SORT_CHANNEL', { selfName: selfName, channels: payload.channels })
  },
  async updateChannels({ state, commit, dispatch, rootGetters }, payload) {
    const selfName = rootGetters['role/userName']

    for (let channel of payload) {
      const cannelIndex = state.channels.findIndex((item) => item.name === channel.name)
      if (cannelIndex === -1) {
        const thumbUrl = await dispatch('role/getThumbUrl', channel.name, { root: true })
        commit('ADD_CHANNELS', { ...channel, thumbUrl, popIsUp: true })
      } else {
        commit('UPDATE_CHANNEL_POPULATION', { index: cannelIndex, population: channel.population })
      }
    }
    commit('SORT_CHANNEL', { selfName: selfName, channels: state.channels })
  },

  async addChannelUser({ commit, dispatch }, payload) {
    const thumbUrl = await dispatch('role/getThumbUrl', payload.username, { root: true })
    payload.thumbUrl = thumbUrl
    commit('ADD_USER_TO_CHANNEL', [payload])
  }
}
