<template>
  <div>
    SupportPwa: {{ supportPwa }} <br />
    device: {{ $device }} <br />
    userAgent: {{ userAgent }} <br />
    standalone: {{ !!standalone }} <br />
    installedApps: {{ installedApps }} <br />
    condition: {{ condition }}
  </div>
</template>
<script>
  export default {
    data() {
      return {
        supportPwa: false,
        userAgent: {},
        standalone: false,
        installedApps: [],
        condition: 0
      }
    },
    async mounted() {
      this.userAgent = navigator.userAgent
      this.standalone =
        navigator.standalone || window.matchMedia('(display-mode: standalone)').matches
      this.installedApps = await navigator.getInstalledRelatedApps()
      if (navigator.standalone || window.matchMedia('(display-mode: standalone)').matches) {
        this.supportPwa = false
        this.condition = 1
      } else if (this.$device.isIos || this.$device.isMacOS) {
        this.supportPwa = true
        this.iosSafari = /Safari/i.test(navigator.userAgent)
        this.iosChrome = /CriOS/i.test(navigator.userAgent)
        this.condition = 2
      } else {
        this.condition = 3
        // 監聽 pwa 符合安裝條件後, cache event 便於之後調用安裝
        window.addEventListener('beforeinstallprompt', (event) => {
          event.preventDefault()
          this.pwaDeferredPrompt = event
          this.supportPwa = true
          this.condition = 4
        })

        // 監聽 pwa 安裝事件, 確保用戶正確安裝
        window.addEventListener('appinstalled', () => {
          this.supportPwa = false
          this.condition = 5
        })
      }
    },
    beforeDestroy() {
      window.removeEventListener('beforeinstallprompt')
      window.removeEventListener('appinstalled')
    }
  }
</script>
