<template>
  <v-container
    :fluid="$vuetify.breakpoint.mdAndDown"
    class="pt-16"
    :class="$vuetify.breakpoint.xsOnly ? 'pb-0' : 'pb-15'"
  >
    <v-row class="justify-center pt-4 pb-4">
      <linearGradientTitle :title="$t(chooseGameCategoryName(gameCategory) + '_game')" />
    </v-row>
    <v-row no-gutters class="align-center justify-left">
      <v-col
        v-for="(provider, index) in showGameProviderList"
        :key="index"
        cols="4"
        sm="4"
        md="2"
        lg="2"
        xl="2"
        class="px-2"
        :class="{ 'mt-5': provider.maintained }"
      >
        <v-card
          color="transparent"
          class="rounded"
          :width="$vuetify.breakpoint.xlOnly || $vuetify.breakpoint.smOnly ? '200px' : '100%'"
          elevation="2"
          :to="
            !provider.maintained
              ? generateGameHallLink(chooseGameCategoryName(gameCategory), provider.providerId)
              : ''
          "
        >
          <v-img
            :lazy-src="gameProviderDefault"
            width="100%"
            height="100%"
            :aspect-ratio="200 / 300"
            :src="provider.url"
            class="rounded"
            contain
            @error="setAltImg(index)"
          >
            <template v-slot:placeholder>
              <v-row class="fill-height ma-0" align="center" justify="center">
                <v-progress-circular indeterminate color="grey lighten-5" />
              </v-row>
            </template>
            <div
              class="d-flex v-card--reveal"
              :class="provider.maintained ? 'gradient-game-maintenance' : ''"
              style="height: 100%"
            >
              <span
                v-if="provider.maintained"
                class="material-symbols-outlined white--text"
                style="font-size: 48px"
              >
                construction
              </span>
            </div>
          </v-img>
        </v-card>
        <v-card color="transparent" elevation="0">
          <v-card-text class="px-0">
            <span class="text-left grey-2--text font-weight-bold text-md-body-1">
              {{ provider.name }}
            </span>
            <p class="mb-0 text-sm-body-2 font-weight-bold red--text">
              {{ provider.maintained ? $t('in_maintenance') : '' }}
            </p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  const STATION = process.env.STATION
  export default {
    name: 'SeriesGuide',
    components: {
      linearGradientTitle: require(`~/components_station/${STATION}/linearGradientTitle`).default
    },
    data() {
      return {
        showGameProviderList: [],
        gameCategory: '',
        gameProviderDefault: require('~/assets/image/game/provider_default.jpg')
      }
    },
    computed: {
      gameCategoryOriginData({ $store }) {
        return $store.getters['gameProvider/gameCategoryOriginData']
      }
    },
    watch: {
      '$route.query.category': {
        immediate: true,
        handler(val) {
          const categoryListList = this.gameCategoryList()
          this.gameCategory = this.chooseGameCategoryId(val)
          this.showgameCategoryList = this.doClassificationForGameProvider(categoryListList)
        }
      }
    },
    mounted() {
      const categoryListList = this.gameCategoryList()
      this.showGameProviderList = this.doClassificationForGameProvider(categoryListList)
    },
    methods: {
      gameCategoryList() {
        const providerList = this.$api.game.gameCategoryList()

        return providerList
      },
      doClassificationForGameProvider(providerList) {
        let newData = []

        newData = providerList.filter((val) => {
          let selectedProvider = 0
          selectedProvider = val.gameCategoryId.indexOf(this.gameCategory)
          return providerList[selectedProvider]
        })
        return newData
      },
      generateGameHallLink(gameCategoryId, providerId) {
        let url = ''
        url = '/games?category=' + gameCategoryId + '&gameProvider=' + providerId

        return this.localePath(url)
      },
      chooseGameCategoryId(categoryName) {
        let gameCategoryId = 0
        switch (categoryName) {
          case 'slot':
            gameCategoryId = 100
            break
          case 'table':
            gameCategoryId = 300
            break
          case 'fishing':
            gameCategoryId = 600
            break
          case 'party_games':
            gameCategoryId = 700
            break
          default:
            gameCategoryId = 100
            break
        }
        return gameCategoryId
      },
      chooseGameCategoryName(categoryId) {
        let gameCategoryName = 0
        switch (categoryId) {
          case 100:
            gameCategoryName = 'slot'
            break
          case 300:
            gameCategoryName = 'table'
            break
          case 600:
            gameCategoryName = 'fishing'
            break
          case 700:
            gameCategoryName = 'party_games'
            break

          default:
            gameCategoryName = 'slot'
            break
        }

        return gameCategoryName
      },
      setAltImg(index) {
        this.showGameProviderList[index].url = this.gameProviderDefault
      }
    }
  }
</script>
