<template>
  <v-container fluid class="pb-15 pt-16" v-show="!maintainSystem[0].maintaining && !status404">
    <!-- title -->
    <v-row align="center" justify="center" no-gutters>
      <div class="pt-8 pb-2">
        <linearGradientTitle :title="$t(selectedGameCategory + '_game')" />
      </div>
    </v-row>
    <!-- game list card -->
    <v-row justify="center" no-gutters class="pt-6 align-top">
      <v-col cols="12" xl="10" lg="10">
        <v-card>
          <v-container fluid class="card-fill">
            <!-- filter -->
            <v-form>
              <v-row no-gutters justify="end" align="end">
                <v-col
                  cols="12"
                  sm="4"
                  md="3"
                  lg="3"
                  xl="2"
                  class="pr-xl-2 pr-lg-2 pr-md-2 pr-md-2 px-2"
                >
                  <v-select
                    v-model="selectedGameHallSort"
                    rounded
                    outlined
                    dense
                    height="40px"
                    :items="gameHallSortList"
                    item-value="sortType"
                    item-text="name"
                    @change="selectedGameHallSortEvent"
                  />
                </v-col>
                <v-col
                  cols="12"
                  sm="4"
                  md="3"
                  lg="3"
                  xl="2"
                  class="pr-xl-3 pr-lg-3 pr-md-3 pr-md-3 px-2"
                >
                  <v-text-field
                    v-model="searchWord"
                    v-validate="'special_character'"
                    append-icon="mdi-magnify"
                    clearable
                    outlined
                    rounded
                    dense
                    :label="$t('search_games')"
                    :error-messages="errors.first('filter.special_character')"
                    :data-vv-as="$t('xin_coin')"
                    data-vv-name="special_character"
                    data-vv-scope="filter"
                    @click:clear="clearSearchEvent"
                    @click:append="searchGameEvent(false)"
                    @keydown.enter="searchGameEvent(false)"
                  />
                </v-col>
              </v-row>
            </v-form>
            <!-- game list -->
            <v-row
              v-if="gameHallList.length > 0"
              no-gutters
              class="text-left align-top justify-start"
            >
              <v-col
                v-for="(game, index) in gameHallList"
                v-show="showEnableStatus(game.enable)"
                :key="`${index}`"
                cols="6"
                sm="4"
                md="3"
                lg="2"
              >
                <gameCard
                  :game-category-id="gameCategoryCode[selectedGameCategory].code"
                  :index="index"
                  :game="game"
                  :show-main-daily-rtp="showMainDailyRtp"
                  :show-rtp-dashboad-status="true"
                />
              </v-col>
            </v-row>
            <!-- no_results_found notice -->
            <v-row v-else no-gutters align="center" justify="center" style="min-height: 120px">
              <span
                class="custom-text-noto text-caption grey-3--text py-4"
                style="font-size: 12px !important"
                >{{ $t('no_results_found') }}</span
              >
            </v-row>
            <!-- pagination -->
            <v-row no-gutters v-if="pageObj.pageTotal">
              <v-col>
                <div class="text-center">
                  <v-pagination
                    v-model="pageNumber"
                    :length="pageObj.pageTotal"
                    total-visible="7"
                    circle
                    color="primary-variant-1"
                    @input="changePageEvent"
                  />
                </div>
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  import gameList from '@/mixins/gameList'
  const STATION = process.env.STATION
  export default {
    mixins: [gameList],
    name: 'GameListIndex',
    components: {
      linearGradientTitle: () => import(`~/components_station/${STATION}/linearGradientTitle`),
      gameCard: () => import('~/components/game/gameCard.vue')
    },
    data() {
      const getGameHallSortList = [
        {
          sortType: 1,
          name: this.$t('all_game')
        },
        {
          sortType: 2,
          name: this.$t('hot_games')
        },
        {
          sortType: 3,
          name: this.$t('by_launch_time')
        }
      ]

      const pageObj = {
        showItemLimit: 24,
        pageTotal: 1,
        startIndex: 0,
        endIndex: 0
      }

      return {
        gameHallSortList: [],
        selectedGameHallSort: '',
        selectedGameCategory: 'slot',
        gameHallList: [],
        getGameHallSortList,
        getGameHallfilterList: [],
        gameDefault: require(`~/assets/image/${STATION}game/game_default.webp`),
        searchWord: null,
        getGameHallList: [],
        pageObj,
        pageNumber: 1,
        showGameIframeStatus: false,
        showMainDailyRtp: false
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      showGameList({ $store }) {
        return $store.getters['gameHall/gameList']
      },
      gameCategoryCode({ $store }) {
        let tmpAry = $store.getters['gameProvider/gameCategory']
        let obj = {}
        tmpAry.forEach((element) => {
          obj[element.dict] = element
        })

        return obj
      },
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      status404() {
        return this.$store.getters['maintain/status404']
      }
    },
    watch: {
      '$route.query.category': {
        immediate: true,
        async handler(categoryCode) {
          if (this.maintainSystem[0].maintaining) {
            return
          }
          const hasCategory = this.gameCategoryCode[categoryCode]
          if (!hasCategory) {
            this.$store.commit('maintain/SET_STATUS404', true)
            return
          }
          this.$store.dispatch('gameProvider/fetch')
          if (categoryCode !== undefined) {
            this.pageNumber = 1
            this.selectedGameHallSort = 2
            this.selectedGameCategory = categoryCode
            const sortType = this.selectedGameHallSort
            this.searchWord = null
            this.getGameHallfilterList = []
            try {
              await this.fetchGameList(categoryCode, sortType)
            } catch (error) {
              console.log(error)
              this.$store.commit('maintain/SET_STATUS404', true)
            }
          }
        }
      },
      showGameList: {
        async handler() {
          this.doPaginationHandler()
        }
      },
      selectedGameHallSort: {
        async handler() {
          this.pageNumber = 1
          this.$store.dispatch('gameProvider/fetch')
        }
      }
    },
    created() {
      const getSelectItemList = this.getGameHallSortList
      this.gameHallSortList = getSelectItemList
      this.selectedGameHallSort = getSelectItemList[1].sortType
    },
    methods: {
      clearSearchEvent() {
        this.searchWord = null
        this.searchGameEvent(true)
      },
      async searchGameEvent(clear) {
        const validate = await this.$validator.validate('filter.*')
        if (validate) {
          let searchGameList = []
          let keyword = this.searchWord
          let getGameHallListTmp = []
          this.pageNumber = 1

          if (clear || keyword === null || keyword === undefined || keyword === '') {
            getGameHallListTmp = this.getGameHallList
          } else {
            searchGameList = this.searchGame(keyword)

            if (Array.isArray(searchGameList) && searchGameList.length) {
              this.getGameHallfilterList = searchGameList
            } else {
              this.getGameHallfilterList = []
              this.pageObj.pageTotal = 0
            }

            //reset
            getGameHallListTmp = this.getGameHallfilterList
          }
          this.pageObj.pageTotal = Math.ceil(getGameHallListTmp.length / this.pageObj.showItemLimit)
          this.doPagination(false, getGameHallListTmp)
        }
      },
      changePageEvent() {
        this.doPagination(
          false,
          this.searchWord !== null && this.searchWord !== ''
            ? this.getGameHallfilterList
            : this.getGameHallList
        )
        //在 Safari 瀏覽器中，window.scrollTo()方法的behavior參數不支援'smooth'值，只支援'auto'和'instant'兩個值。
        window.scrollTo({ top: 0, behavior: 'auto' })
      },
      async selectedGameHallSortEvent() {
        this.searchWord = null
        this.getGameHallfilterList = []

        const gameCategoryId = this.gameCategoryCode[this.selectedGameCategory].code
        const sortType = this.selectedGameHallSort
        const lang = this.$i18n.locale
        const limit = 999

        await this.$store.dispatch('gameHall/fetchGameList', {
          gameCategoryId,
          sortType,
          lang,
          limit
        })
      },
      searchGame(keyword) {
        let searchResultArr = []
        searchResultArr = this.getGameHallList.filter((item) => {
          const gameName = item.name
          let index = gameName.indexOf(keyword)
          return index == -1 ? false : true
        })
        return searchResultArr
      },
      doPagination(reset, gameHallList) {
        const list = []
        if (reset || this.pageNumber === 1) {
          this.pageObj.startIndex = 0
          this.pageObj.endIndex = this.pageObj.showItemLimit - 1
        } else {
          this.pageObj.startIndex =
            this.pageNumber * this.pageObj.showItemLimit - this.pageObj.showItemLimit
          this.pageObj.endIndex = this.pageNumber * this.pageObj.showItemLimit - 1
        }
        for (let index = this.pageObj.startIndex; index <= this.pageObj.endIndex; index++) {
          if (gameHallList[index] !== undefined) {
            list.push(gameHallList[index])
          }
        }
        this.gameHallList = list
        this.showMainDailyRtp = this.selectedGameHallSort === 2 ? true : false
      },
      doPaginationHandler() {
        this.getGameHallList = this.showGameList
        this.pageObj.pageTotal = Math.ceil(this.getGameHallList.length / this.pageObj.showItemLimit)
        this.doPagination(false, this.getGameHallList)
      },
      async fetchGameList(selectedGameCategory, sortType) {
        const gameCategoryId = this.gameCategoryCode[selectedGameCategory].code
        const lang = this.$i18n.locale
        const limit = 999
        await this.$store.dispatch('gameHall/fetchGameList', {
          gameCategoryId,
          sortType,
          lang,
          limit
        })
      },
      showMaintainNotify() {
        this.$store.dispatch('easySnackbar/setContent', {
          time: -1,
          type: 'warning',
          msg1: this.$t('maintenanceWithdrawalError'),
          msg2: this.$t('plz_wait_or_app'),
          show: true
        })
      }
    }
  }
</script>
