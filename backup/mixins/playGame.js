const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

export default {
  data() {
    return {
      //配合星城伺服器的遊戲ID 為了避免跟其他伺服器的遊戲ID重複
      serverConstant: loadConfig.game.serverConstant,
      //若遊戲無經驗，於提示窗確認前，暫存遊戲資訊
      clickedNoExpGameInfo: {
        platformId: '',
        gameId: '',
        isDemo: false
      },
      openGameHandler: {}
    }
  },
  computed: {
    maintainSystem({ $store }) {
      return $store.getters['maintain/system']
    }
  },
  methods: {
    loginGameService() {
      this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(this.$xinConfig.WEB_GAME_SERVICE.ID))
      this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
        serviceId: this.$xinConfig.WEB_GAME_SERVICE.ID,
        enable: true,
        connected: true
      })
    },
    getGameBody(xinkey, platformId, gameId) {
      const userAgent = this.$device.userAgent
      const isSafari = userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Version') !== -1
      const isIphone = userAgent.indexOf('iPhone') !== -1 && userAgent.indexOf('Version') !== -1
      const isIpadPro = isSafari && !isIphone && 'ontouchend' in document // 判斷是否為ipadPro
      const gameCategoryId = this.game.categoryType
      const userLevel = this.$store.getters['role/level']
      let gameBody = {
        xinkey,
        gameCategoryId,
        gameName: this.game.name,
        canRedirect: this.game.canRedirect,
        platformId,
        gameId,
        mobile: isIpadPro ? true : this.$device.isMobile,
        lang: this.$i18n.locale,
        ip: this.$store.getters['deviceManagement/getIP'],
        backUrl: '',
        userAgent: this.$ua._ua,
        thumbUrl: this.$store.getters['role/thumbUrl'],
        vipLevel: this.$store.getters['role/vipLevel']
      }

      gameBody = { ...gameBody, userLevel: userLevel }
      return gameBody
    },
    setupGameLink(body, gameLink) {
      if (Number.isInteger(gameLink)) {
        this.$store.commit('gameHall/SET_OPENGAMELOCK', false)
        this.$notify.backendError(gameLink)
        const gameDisableList = [50003, 60001, 60032, 60037]
        if (gameDisableList.includes(gameLink)) {
          this.$store.dispatch('gameHall/updateListWhenDisabled', {
            gameCategoryId: body.gameCategoryId,
            gameId: body.gameId
          })
        } else {
          this.$store.commit('gameHall/SET_GAMELIST_MAINTAIN_STATUS', {
            gameId: body.gameId,
            status: true
          })
          this.$store.commit('gameHall/SET_ALL_GAME_LIST_MAINTAIN_STATUS', {
            gameId: body.gameId,
            status: true
          })
        }
      }
      return !Number.isInteger(gameLink)
    },
    async initGameLink(xinkey, isDemo, body, headerData) {
      this.$store.commit('gameHall/SET_SINGLEGAMEHALLINFO', body)
      const gameSetting = isDemo ? 'fetchGameDemo' : 'fetchGameLink'
      const gameHall = `gameHall/${gameSetting}`
      await this.$store.dispatch(gameHall, { headerData, body })
      this.xinkey = isDemo ? this.xinkey : xinkey
      return this.$store.getters['gameHall/gameLink']
    },
    async getGameXinKey(gameId) {
      try {
        // 取得 user key
        this.$wsClient.send(this.$wsPacketFactory.getUserKey(this.serverConstant + gameId))
        const userKey = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) =>
          data.isFeature(this.$xinConfig.FEATURE.GAME.TYPE.USER_KEY)
        )
        return userKey
      } catch (error) {
        this.$notify.error(this.$t('get_xinkey_failed'))
        setTimeout(() => {
          this.$store.commit('gameHall/SET_OPENGAMELOCK', false)
        }, 3000)
        return ''
      }
    },
    async prepareHandler(platformId, gameId, isDemo) {
      if (!isDemo) {
        //避免連點
        // 避免特殊情況所加入的鎖，畫面已經有對應的鎖，但是考慮到可能會有特殊情況，所以加上這個鎖
        let openGameLock = this.$store.getters['gameHall/openGameLock']
        this.disabledStatus = openGameLock
        if (openGameLock) return
        this.$store.commit('gameHall/SET_OPENGAMELOCK', true)
      }
      await this.$store.dispatch('maintain/fetch')
      if (this.maintainSystem[0].maintaining) return
      let xinkey = { key: '', userAlias: '' }
      if (this.isLogin && !isDemo) {
        const serviceList = this.$store.getters['xinProtocol/services']
        const gameServiceEnable = serviceList[this.$xinConfig.WEB_GAME_SERVICE.ID].enable
        if (!gameServiceEnable) this.loginGameService()
        xinkey = await this.getGameXinKey(gameId)
        if (xinkey.key === '' || xinkey.userAlias === '') return { isSuccess: false }
      }
      const body = this.getGameBody(xinkey.key, platformId, gameId, xinkey.userAlias)
      const headerData = {
        username: this.$store.getters['role/userName']
          ? this.$store.getters['role/userName']
          : null,
        alias: xinkey.userAlias ? xinkey.userAlias : null
      }
      const gameLink = await this.initGameLink(xinkey.key, isDemo, body, headerData)
      const isSuccess = this.setupGameLink(body, gameLink)
      return {
        isSuccess,
        openGame: { gameId, isDemo, body, xinkey: xinkey.key, headerData }
      }
    },

    async openGame({ headerData, xinkey, isDemo, body, gameId }) {
      this.$nuxt.$emit('root:showGameIframeStatus', true)
      if (this.isLogin) this.$store.commit('gameHall/ADD_PLAYED_GAME_LIST', gameId) //更新近期遊玩列表
      this.$store.commit('gameHall/SET_XINKEY', xinkey)

      this.gameClickAnalytics({ headerData, gameId, isDemo, body, xinkey })
      if (isDemo) this.freePlayAnalytics()
      else this.playAnalytics()
    },
    showRTPStyle() {
      const condition = this.defaultRtp
      const dailyRtp = typeof this.game.dailyRtp === 'number' ? this.game.dailyRtp : undefined
      const weeklyRtp = typeof this.game.weeklyRtp === 'number' ? this.game.weeklyRtp : undefined
      const monthlyRtp = typeof this.game.monthlyRtp === 'number' ? this.game.monthlyRtp : undefined

      return {
        showRTPTooltipStatus:
          dailyRtp !== undefined && weeklyRtp !== undefined && monthlyRtp !== undefined,
        dailyShowStatus: dailyRtp !== undefined,
        dailyBackgroundColor: dailyRtp > condition ? 'error' : 'success',
        dailyIcon: dailyRtp > condition ? 'trending_up' : 'trending_down',
        dailyIconColor: dailyRtp > condition ? 'error--text' : 'success--text',
        weeklyShowStatus: dailyRtp !== undefined,
        weeklyIcon: weeklyRtp > condition ? 'trending_up' : 'trending_down',
        weeklyIconColor: weeklyRtp > condition ? 'error--text' : 'success--text',
        monthlyShowStatus: dailyRtp !== undefined,
        monthlyIcon: monthlyRtp > condition ? 'trending_up' : 'trending_down',
        monthlyIconColor: monthlyRtp > condition ? 'error--text' : 'success--text'
      }
    },
    // 因 props 傳入的參數，若作為函式的參數帶入，則不具備響應性，故改用 this.game.xxx
    async payGameClickHandler() {
      if (this.disabledStatus || this.isVipLevelLimit) return
      await this.$store.dispatch('maintain/fetch')
      if (this.maintainSystem[0].maintaining) return
      const localStorageLiveConfirm = this.$localStorage.get('localStorageLiveConfirm').expired
      if (!this.hasExp) {
        this.clickedNoExpGameInfo.platformId = this.game.platformId
        this.clickedNoExpGameInfo.gameId = this.game.id
        this.clickedNoExpGameInfo.isDemo = false
      }
      const memberLevel = this.$store.getters['role/vipLevel']
      const canOpenGame = memberLevel > 0
      if (this.game.maintaining) return
      if (canOpenGame) {
        this.openGameHandler = await this.prepareHandler(this.game.platformId, this.game.id, false)
        const isShowDialog =
          this.$UIConfig.gameIframe.showNoExpDialog &&
          (localStorageLiveConfirm === undefined ||
            this.$moment(localStorageLiveConfirm).isBefore(this.$moment(), 'day'))
        if (!this.openGameHandler.isSuccess) return
        if (isShowDialog && this.game.categoryType === 200) {
          this.$nuxt.$emit('root:showGameCardConfirmDialogStatus', {
            show: true,
            hasExp: this.hasExp,
            onConfirmNotify: () => {
              this.openGame(this.openGameHandler.openGame)
            }
          })
        } else {
          if (this.hasExp) this.openGame(this.openGameHandler.openGame)
          else this.showNotyNoExpGainNotyDialogStatus = true
        }
      } else {
        this.showNotyNotRealMemberDialogStatus = true
        this.$store.commit('gameHall/SET_OPENGAMELOCK', false)
      }
    },
    async freeGameClickHandler() {
      if (this.game.maintaining) return
      await this.$store.dispatch('maintain/fetch')
      if (this.maintainSystem[0].maintaining) return
      this.openGameHandler = await this.prepareHandler(this.game.platformId, this.game.id, true)
      if (this.openGameHandler.isSuccess) this.openGame(this.openGameHandler.openGame)
    },
    async playNoExpGame() {
      await this.$store.dispatch('maintain/fetch')
      if (this.maintainSystem[0].maintaining) return
      if (this.openGameHandler.isSuccess) this.openGame(this.openGameHandler.openGame)
    }
  }
}
