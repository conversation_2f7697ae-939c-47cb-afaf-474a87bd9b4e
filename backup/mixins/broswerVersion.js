export default {
  methods: {
    getBrowserVersion(browserName) {
      if (navigator && navigator.userAgent) {
        var browserVersion = navigator.userAgent
        switch (browserName) {
          case 'Chrome':
            return parseFloat(browserVersion.split('Chrome/')[1]) || null
          case 'Safari':
            return parseFloat(browserVersion.split('Version/')[1]) || null
          case 'Firefox':
            return parseFloat(browserVersion.split('Firefox/')[1]) || null
          case 'CriOS':
            return parseFloat(browserVersion.split('CriOS/')[1]) || null
          case 'Samsung':
            return parseFloat(browserVersion.split('SamsungBrowser/')[1]) || null
          case 'macOS':
            return parseFloat(browserVersion.split('Mac OS X ')[1]) || null
          default:
            return -1
        }
      }
      return -1
    },
    isLowerThanRequiredVersion() {
      let isLower = false
      const device = this.$device
      const requiredChromeVersion = 88
      const requiredSafariVersion = 14
      const requiredSamsungVersion = 9
      const ignores = ['Google Search App', 'Webview'] // 不檢查的瀏覽器清單
      // 遇到無能為力，所以不做檢查
      if (ignores.includes(this.$ua._parsed.name)) {
        return isLower
      }
      // Chrome
      if (device.isChrome) {
        //ipad && iphone
        if ((device.isTablet && device.isIos) || (device.isMobile && device.isIos))
          isLower = this.getBrowserVersion('CriOS') < requiredChromeVersion
        else isLower = this.getBrowserVersion('Chrome') < requiredChromeVersion
      }
      // 三星瀏覽器
      else if (device.isSamsung)
        isLower = this.getBrowserVersion('Samsung') < requiredSamsungVersion
      // Safari
      else if (device.isSafari) {
        isLower = this.getBrowserVersion('Safari') < requiredSafariVersion
      }
      return isLower
    }
  }
}
