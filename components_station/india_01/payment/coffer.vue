<template>
  <div>
    <v-container fluid class="pa-4 pa-sm-6" id="coffer-container">
      <!-- 存入星幣 文字 -->
      <v-row no-gutters class="pb-4 align-center">
        <v-col class="default-content--text">
          {{ $UIConfig.coffer.saveStarCoin.title.format($t('store'), $t('xin_coin')) }}
        </v-col>
      </v-row>
      <!-- 存入星幣區塊 輸入框 -->
      <v-row no-gutters class="align-center justify-left">
        <!-- 星幣餘額 -->
        <v-col cols="12" sm="6" md="5" lg="5" class="pr-sm-2">
          <v-text-field
            v-model="selfBalance"
            disabled
            :label="$UIConfig.coffer.saveStarCoin.balance.format($t('xin_coin'), $t('balance'))"
            filled
            shaped
          />
        </v-col>
        <!-- 存入星幣 -->
        <v-col cols="12" sm="6" md="7" lg="7" class="pl-sm-2">
          <v-text-field
            v-model="storePoints"
            ref="storePoints"
            :label="$UIConfig.coffer.saveStarCoin.input.format($t('store'), $t('xin_coin'))"
            name="storePoints"
            :error-messages="errors.first('coffer.storePoints')"
            v-validate="{
              coin_min: 1,
              is_number: true,
              insufficient_store_points: selfBalance
            }"
            data-vv-scope="coffer"
            filled
            shaped
            clearable
            :placeholder="$t('store_xin_coin_msg')"
            @input="validateStorePoints"
            :disabled="(withdrawPoints !== null && withdrawPoints !== '') || loading"
            class="error-wrap"
          >
            <template v-slot:append-outer>
              <v-btn
                outlined
                color="primary"
                class="ml-2"
                :disabled="
                  (withdrawPoints !== null && withdrawPoints !== '') || selfBalance === 0 || loading
                "
                @click="storeAll"
              >
                MAX
              </v-btn>
            </template>
          </v-text-field>
        </v-col>
      </v-row>
      <v-divider />
      <!-- 提領星幣 文字 -->
      <v-row no-gutters class="py-4 align-center">
        <v-col class="default-content--text">
          {{ $UIConfig.coffer.withdrawCoin.title.format($t('withdraw'), $t('xin_coin')) }}
        </v-col>
      </v-row>
      <!-- 提領星幣區塊 輸入框 -->
      <v-row no-gutters class="align-center justify-left">
        <!-- 已存星幣 -->
        <v-col cols="12" sm="6" md="5" lg="5" class="pr-sm-2">
          <v-text-field
            v-model="selfSafe"
            disabled
            :label="
              $UIConfig.coffer.withdrawCoin.balance.format($t('already_saved'), $t('xin_coin'))
            "
            filled
            shaped
          />
        </v-col>
        <!-- 提領星幣 -->
        <v-col cols="12" sm="6" md="7" lg="7" class="pl-sm-2">
          <v-text-field
            v-model="withdrawPoints"
            ref="withdrawPoints"
            name="withdrawPoints"
            :error-messages="errors.first('coffer.withdrawPoints')"
            v-validate="{
              coin_min: 1,
              is_number: true,
              insufficient_withdraw_points: selfSafe
            }"
            data-vv-scope="coffer"
            :label="$UIConfig.coffer.withdrawCoin.input.format($t('withdraw'), $t('xin_coin'))"
            filled
            shaped
            clearable
            :placeholder="$t('withdraw_xin_coin_msg')"
            @input="validateWithdrawPoints"
            :disabled="(storePoints !== null && storePoints !== '') || loading"
            class="error-wrap"
          >
            <template v-slot:append-outer>
              <v-btn
                outlined
                color="primary"
                class="ml-2"
                :disabled="
                  (storePoints !== null && storePoints !== '') || selfSafe === 0 || loading
                "
                @click="withdrawAll"
              >
                MAX
              </v-btn>
            </template>
          </v-text-field>
        </v-col>
      </v-row>
      <!-- 確定按鈕 -->
      <v-row no-gutters>
        <v-col>
          <v-card color="transparent" elevation="0">
            <v-card-actions class="px-0 pb-0 pt-2">
              <v-spacer />
              <v-btn
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                :loading="!useCofferStatus"
                :disabled="cofferBtnDisabled || loading || !useCofferStatus"
                @click="doSure"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
  export default {
    name: 'coffer',
    data() {
      return {
        //存入多少星幣
        storePoints: null,
        //提領多少星幣
        withdrawPoints: null,
        cofferBtnDisabled: true,
        loading: false,
        //由於後端回傳5秒後才能使用保險箱，故先讓按鈕轉圈圈，實際上前端要等5秒後才能使用....
        useCofferStatus: false
      }
    },
    async mounted() {
      // 金流服務連線後，5秒後才能使用保險箱
      setTimeout(() => {
        this.useCofferStatus = true
      }, 5000)
      this.$wsClient.send(this.$wsPacketFactory.demandSafeBox())
      try {
        const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          return data.isFeature(this.$xinConfig.FEATURE.SAFEBOX.TYPE.DEMAND_BOX_AMOUNT)
        })
        this.$store.commit('role/SET_SAFE', res.boxAmount)
      } catch (error) {
        this.$notify.error(this.$t('coffer_error_msg'))
        console.log(error)
      }
      this.validateAll()
    },
    computed: {
      selfBalance({ $store }) {
        return Number($store.getters['role/balance'])
      },
      selfSafe({ $store }) {
        return Number($store.getters['role/safe'])
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      storePoints: {
        handler() {
          //畫面渲染完再做驗證
          this.$nextTick(() => {
            this.validateAll()
          })
        }
      },
      withdrawPoints: {
        handler() {
          //畫面渲染完再做驗證
          this.$nextTick(() => {
            this.validateAll()
          })
        }
      }
    },

    methods: {
      //全部存入
      storeAll() {
        this.withdrawPoints = null
        this.storePoints = this.selfBalance
      },
      //全部提領
      withdrawAll() {
        this.storePoints = null
        this.withdrawPoints = this.selfSafe
      },
      validatePoints(pointType) {
        if (this[pointType] === null) return
        this[pointType] = this[pointType].replace(/[^0-9]/g, '')
        this[pointType] = this[pointType].replace(/^0+/, '')
        if (this[pointType].length > 10) this[pointType] = this[pointType].slice(0, 10)
        this.$refs[pointType].lazyValue = this[pointType]
        this.validateAll()
      },
      validateStorePoints() {
        this.validatePoints('storePoints')
      },
      validateWithdrawPoints() {
        this.validatePoints('withdrawPoints')
      },
      validateAll() {
        this.$validator.validate('coffer.*').then((valid) => {
          const hasValidInput =
            (this.storePoints && this.storePoints !== '') ||
            (this.withdrawPoints && this.withdrawPoints !== '')
          this.cofferBtnDisabled = !(valid && hasValidInput)
        })
      },
      //確認
      async doSure() {
        this.loading = true
        this.$emit('setDisableBtn', this.loading)
        if (this.storePoints !== null && this.storePoints !== '') {
          await this.handleSafeBoxOperation(true) // 存入
        } else {
          await this.handleSafeBoxOperation(false) // 取出
        }
        this.loading = false
        this.$emit('setDisableBtn', this.loading)
      },
      async handleSafeBoxOperation(isDeposit) {
        const setupValue = (amount, boxAmount) => {
          this.$store.commit('role/SET_BALANCE', amount)
          this.$store.commit('role/SET_SAFE', boxAmount)
          this.withdrawPoints = null
          this.storePoints = null
          const successNoty = '{0} {1} {2}'.format(
            this.$t('toatleCoffer'),
            this.formatNumber(boxAmount),
            this.$t('xin_coin')
          )
          this.$notify.success(successNoty)
        }
        const points = isDeposit ? this.storePoints : this.withdrawPoints
        if (isDeposit && (points === null || points === '')) return

        const packetMethod = isDeposit ? 'depositSafeBox' : 'withdrawSafeBox'
        const featureType = isDeposit ? 'DEPOSIT_BOX_AMOUNT' : 'RECEIVE_BOX_AMOUNT'

        this.$wsClient.send(this.$wsPacketFactory[packetMethod](points))

        try {
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) =>
            data.isFeature(this.$xinConfig.FEATURE.SAFEBOX.TYPE[featureType])
          )

          if (res.success) {
            setupValue(res.amount, res.boxAmount)
          } else {
            this.$notify.error(this.$t(res.response))
          }
        } catch (error) {
          this.$notify.error(this.$t('coffer_error_msg'))
          console.log(error)
        }
      },
      //千分位
      formatNumber(num) {
        return new Intl.NumberFormat().format(num)
      }
    }
  }
</script>

<style lang="scss">
  .append-outer-text {
    width: 32px;
  }
  .error-wrap ::v-deep .v-messages {
    white-space: normal !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    line-height: 1.2;
    padding-top: 4px;
  }

  /* 如果需要調整錯誤訊息容器的寬度 */
  .error-wrap ::v-deep .v-text-field__details {
    min-height: auto;
    padding-bottom: 4px;
  }
  #coffer-container {
    .v-input__append-outer {
      margin-left: 8px !important;
    }
  }
</style>
