<template>
  <v-container class="pa-0">
    <div class="px-4 pa-sm-6 px-0">
      <div class="d-flex justify-center justify-sm-start flex-nowrap">
        <div>
          <avatar frame :guild-id="guildId" />
          <div
            v-if="!breakpoint.xsOnly"
            class="d-flex align-end justify-center"
            :class="{ 'mt-10 pt-4': guildCaption !== '', 'mt-3': guildCaption === '' }"
          >
            <v-btn
              block
              :disabled="hasGuild"
              class="button-content--text"
              :color="$UIConfig.defaultBtnColor"
              elevation="0"
              @click="sendAddGuildMember(guildId)"
            >
              {{ $t('send_add_member') }}
            </v-btn>
          </div>
        </div>

        <div v-if="!breakpoint.xsOnly" class="ml-6" style="flex-grow: 1">
          <v-row no-gutters>
            <v-col>
              <span class="text-start custom-text-noto text-subtitle-1 text-sm-h6 primary--text">
                {{ guildName }}
              </span>
            </v-col>
          </v-row>

          <v-row v-if="!breakpoint.xsOnly" class="mt-4" no-gutters>
            <v-col class="my-1" cols="12" sm="5" md="5" lg="5" xl="5">
              <div class="d-flex justify-start align-center">
                <span class="text-start custom-text-noto text-caption grey-2--text">
                  {{ '{0}/{1}'.format($t('online_member_count'), $t('guild_member_count')) }}
                </span>
                <v-spacer v-if="breakpoint.xsOnly" />
                <span
                  class="text-start custom-text-noto text-subtitle-1 default-content--text ml-2"
                >
                  {{ '{0}/{1}'.format(guildOnlineMembers, guildMembers) }}
                </span>
                <div class="d-flex justify-start align-center ml-10">
                  <span class="text-start custom-text-noto text-caption grey-2--text text-no-wrap">
                    {{ $t('guild_fund') }}
                  </span>
                  <v-img
                    :src="getImage('<EMAIL>')"
                    :srcset="getSrcset('coin')"
                    min-width="24"
                    max-width="24"
                    height="24"
                    class="ml-2 d-inline-block vertical-middle"
                  />
                  <span
                    class="text-start custom-text-noto text-subtitle-1 default-content--text ml-1"
                  >
                    {{ guildFund }}
                  </span>
                </div>
              </div>
            </v-col>
          </v-row>

          <v-row v-if="!breakpoint.xsOnly" class="mt-4" no-gutters>
            <v-card
              flat
              class="guild-scrollable-content"
              width="100%"
              :height="guildCaption === '' ? '70px' : '114px'"
            >
              <v-card-text class="px-3 py-2">
                <span
                  class="custom-text-noto text-body-2 guild-content-text"
                  :class="{
                    'grey-3--text': guildCaption === '',
                    'default-content--text': !(guildCaption === '')
                  }"
                  >{{
                    guildCaption === '' ? $t('not_have_caption') : replaceKeywords(guildCaption)
                  }}</span
                >
              </v-card-text>
            </v-card>
          </v-row>
        </div>
      </div>
      <div v-if="breakpoint.xsOnly">
        <v-row justify="center" no-gutters>
          <span class="custom-text-noto text-subtitle-1 text-sm-h6 primary--text">
            {{ guildName }}
          </span>
        </v-row>
        <v-row no-gutters>
          <v-col cols="12">
            <div class="d-flex justify-center align-center">
              <span class="text-start custom-text-noto text-caption grey-2--text">
                {{ '{0}/{1}'.format($t('online_member_count'), $t('guild_member_count')) }}
              </span>
              <v-spacer />
              <span class="text-start custom-text-noto text-subtitle-1 default-content--text ml-2">
                {{ '{0}/{1}'.format(guildOnlineMembers, guildMembers) }}
              </span>
            </div>
          </v-col>
        </v-row>
        <v-row no-gutters>
          <v-col cols="12">
            <div class="d-flex justify-start align-center">
              <span class="text-start custom-text-noto text-caption grey-2--text">
                {{ $t('guild_fund') }}
              </span>
              <v-spacer />
              <v-img
                :src="getImage('<EMAIL>')"
                :srcset="getSrcset('coin')"
                min-width="20"
                max-width="20"
                height="20"
                class="ml-2 d-inline-block vertical-middle"
              />
              <span class="text-start custom-text-noto text-subtitle-1 default-content--text">
                {{ guildFund }}
              </span>
            </div>
          </v-col>
        </v-row>
        <v-row no-gutters>
          <v-col cols="12">
            <v-card
              flat
              v-if="breakpoint.xsOnly"
              class="guild-scrollable-content mt-4"
              width="100%"
              height="104px"
            >
              <v-card-text class="px-3 py-2">
                <span
                  class="custom-text-noto text-body-2 guild-content-text"
                  :class="{
                    'grey-3--text': guildCaption === '',
                    'default-content--text': !(guildCaption === '')
                  }"
                  >{{
                    guildCaption === '' ? $t('not_have_caption') : replaceKeywords(guildCaption)
                  }}</span
                >
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </div>
      <v-row v-if="breakpoint.xsOnly" no-gutters>
        <v-col cols="12">
          <div class="d-flex justify-center pt-4 pt-sm-2">
            <v-btn
              block
              :disabled="hasGuild"
              class="button-content--text"
              :color="$UIConfig.defaultBtnColor"
              elevation="0"
              @click="sendAddGuildMember(guildId)"
            >
              {{ $t('send_add_member') }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
      <guildConfirmDialog
        v-if="guildConfirmStatus"
        :confirm-obj="confirmObj"
        :has-guild-letter="hasGuildLetter"
        :show-guild-confirm-dialog-status.sync="guildConfirmStatus"
      />
    </div>
  </v-container>
</template>

<script>
  import images from '~/mixins/images'
  import scssLoader from '~/mixins/scssLoader.js'
  import converter from '~/mixins/converter'
  export default {
    name: 'guildLobbyCardInfo',
    mixins: [images, scssLoader, converter],
    components: {
      guildConfirmDialog: () => import('~/components/guild/guildConfirmDialog'),
      avatar: () => import('~/components/guild/guildAvatar')
    },
    props: {
      isCard: {
        type: Boolean,
        default: false
      },
      isInfoPage: {
        type: Boolean,
        default: false
      },
      guildCaption: {
        type: String,
        default: ''
      },
      guildName: {
        type: String,
        default: ''
      },
      guildPower: {
        type: Number,
        default: 0
      },
      guildOnlineMembers: {
        type: Number,
        default: 0
      },
      guildMembers: {
        type: Number,
        default: 0
      },
      guildRank: {
        type: Number,
        default: 0
      },
      guildId: {
        type: Number,
        default: 0
      },
      hasGuild: {
        type: Boolean,
        default: false
      },
      guildFund: {
        type: String,
        default: 0
      }
    },
    data() {
      return { guildConfirmStatus: false, confirmObj: {} }
    },

    computed: {
      orientation({ $store }) {
        return $store.getters['deviceManagement/getOrientation']
      },
      facebookId() {
        return this.$store.getters['role/facebookId']
      },
      hasGuildLetter() {
        const guildLetterList = this.$store.getters['mail/list'].filter(
          (mail) => mail.guildInvitationLetter === true
        )
        const hasGuildLetter = guildLetterList.some(
          (mail) => mail.itemType === this.guildId && mail.isRead === false
        )
        return hasGuildLetter
      },
      //是否橫屏
      isStanding() {
        return this.orientation === 0
      },
      //是否手機
      isMobile() {
        return this.$device.isMobile
      },
      selfGuildName({ $store }) {
        return $store.getters['guild/guildName']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    async mounted() {
      this.setOrientation() //偵測手機轉向是否改變
      window.addEventListener('orientationchange', this.setOrientation)
    },
    beforeDestroy() {
      window.removeEventListener('orientationchange', this.setOrientation)
    },

    methods: {
      setOrientation() {
        this.$store.dispatch('deviceManagement/setOrientation')
      },
      openUploadMethodDialog() {
        if (!this.facebookId && this.isInfoPage) {
          this.$nuxt.$emit('root:showUploadPhotoMethodDialogStatus', true)
        }
      },
      async onLeaveGuildClick() {
        this.$wsClient.send(this.$wsPacketFactory.leaveGuild())
      },
      async sendAddGuildMember(guildId) {
        const sendAddGuild = () => {
          this.$wsClient.send(this.$wsPacketFactory.addGuild(guildId))
        }
        this.confirmObj = {
          confirm: 'sure',
          confirmInfo: this.$t('guild_add_noty', { guild: this.guildName }),
          cancel: 'latter',
          onConfirmNotify: sendAddGuild
        }
        this.guildConfirmStatus = true
      }
    },
    isGuildPresident() {
      return this.guildRank === 3
    },
    showOperation(rank) {
      return rank > 1
    }
  }
</script>
<!-- 此處加scoped會使部分Class無作用，故在最外層包覆一層class以及將class命名複雜化避免汙染 -->
<style lang="scss">
  $dialog-fill: map-get($colors, 'dialog-fill');
  $black-with-opacity-20-color: map-get($colors, black-with-opacity-20);
  .guild-scrollable-content {
    overflow-y: auto; /* 自动显示滚动条 */
    background-color: $black-with-opacity-20-color !important;
  }
  .guild-content-text {
    word-break: break-all;
    white-space: pre-wrap;
  }
</style>
