<template>
  <v-row no-gutters id="guild-list-data-table">
    <v-col cols="12">
      <template>
        <v-data-table
          class="grey-6"
          fixed-header
          @pagination="scrollToTop"
          @update:page="handlePageChange"
          :hide-default-footer="$vuetify.breakpoint.xsOnly"
          :height="isCard ? height : '100%'"
          :items-per-page="itemsPage"
          :headers="showOperation(guildRank) ? guildMasterListHeaders : guildListHeaders"
          :page="currentPage"
          :items="displayList"
          @current-items="handleCurrentItems"
          :footer-props="{
            'items-per-page-text': $t('items_per_page'),
            'items-per-page-all-text': $t('all'),
            'page-text': `{0}-{1} ${$t('total_page')} {2} ${$t('quantity')}`,
            'items-per-page-options': itemsPerPageOptions
          }"
        >
          <template v-if="displayList.length === 0" v-slot:body>
            <tbody v-show="$vuetify.breakpoint.xsOnly" :height="isCard ? height - 48 : 'auto'">
              <tr class="v-data-table__empty-wrapper d-flex justify-center">
                <td class="d-flex align-center" colspan="5">
                  <span class="text-center">
                    {{ $t('no_data') }}
                  </span>
                </td>
              </tr>
            </tbody>
            <tbody v-show="!$vuetify.breakpoint.xsOnly" :height="isCard ? height - 48 : 'auto'">
              <tr class="v-data-table__empty-wrapper">
                <td colspan="5">
                  <span class="text-center">
                    {{ $t('no_data') }}
                  </span>
                </td>
              </tr>
            </tbody>
          </template>
          <!--mobile body-->
          <template v-else-if="$vuetify.breakpoint.xsOnly" v-slot:body="{ items, headers }">
            <tbody v-show="$vuetify.breakpoint.xsOnly" :height="isCard ? height - 48 : 'auto'">
              <div v-for="(item, index) in items" :key="index">
                <tr class="v-data-table__mobile-table-row">
                  <v-menu
                    :disabled="isSelf(item.name)"
                    offset-y
                    absolute
                    content-class="easyPlayer-custom-border"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <div
                        @click="onUserInfoClick(item.name, guildInfoName)"
                        class="px-4"
                        v-bind="attrs"
                        v-on="on"
                      >
                        <td class="v-data-table__mobile-row">
                          <div class="v-data-table__mobile-row__header">
                            <span class="content--text px-0">
                              {{ headers[0].text }}
                            </span>
                          </div>
                          <div class="v-data-table__mobile-row__cell">
                            <span
                              v-if="item.rank === 1"
                              class="text-center custom-text-noto text-body-2 default-content--text nowrap--text"
                            >
                              {{ $t('member') }}
                            </span>
                            <v-img
                              v-else
                              :src="getPresidentImg3x(item.rank)"
                              :srcset="`${getPresidentImg(item.rank)} 1x, ${getPresidentImg3x(
                                item.rank
                              )} 2x`"
                              max-width="40"
                              max-height="40"
                            />
                          </div>
                        </td>
                        <td class="v-data-table__mobile-row">
                          <div class="v-data-table__mobile-row__header">
                            <span class="content--text px-0">
                              {{ headers[1].text }}
                            </span>
                          </div>
                          <div class="v-data-table__mobile-row__cell">
                            <v-badge
                              bordered
                              bottom
                              :color="item.onlines ? 'success' : 'offline'"
                              dot
                              offset-x="9"
                              offset-y="12"
                            >
                              <v-list-item-avatar class="pa-0 ma-0">
                                <v-img :src="item.avatar" contain @error="errorImgHandler(item)">
                                  <template v-slot:placeholder>
                                    <v-row class="fill-height ma-0" align="center" justify="center">
                                      <v-img :src="defaultImg" contain />
                                    </v-row>
                                  </template>
                                </v-img>
                              </v-list-item-avatar>
                            </v-badge>
                          </div>
                        </td>
                        <td class="v-data-table__mobile-row">
                          <div class="v-data-table__mobile-row__header">
                            <span class="content--text px-0">
                              {{ headers[2].text }}
                            </span>
                          </div>
                          <div class="v-data-table__mobile-row__cell">
                            <span
                              class="text-center custom-text-noto text-subtitle-1 primary--text"
                            >
                              {{ item.name }}
                            </span>
                          </div>
                        </td>
                      </div>
                    </template>
                    <easyPlayerInfo
                      tile
                      report
                      is-card
                      action-bar
                      only-coin
                      :player-info="playerInfo"
                      style="min-width: 300px"
                      badge-type="relation"
                    />
                  </v-menu>
                  <td
                    v-if="showOperation(guildRank)"
                    class="v-data-table__mobile-row d-flex align-start px-4 py-2"
                  >
                    <div class="v-data-table__mobile-row__header mt-1">
                      <span class="content--text">
                        {{ headers[3].text }}
                      </span>
                    </div>
                    <div class="v-data-table__mobile-row__cell">
                      <div
                        v-if="!isGuildOwner(item.rank)"
                        class="d-flex justify-end flex-wrap mb-2"
                      >
                        <v-row no-gutters>
                          <v-col class="mb-2" align-self="end" cols="12">
                            <v-btn
                              v-if="isGuildOwner(guildRank) && false"
                              :color="isVicepresident(item.rank) ? 'warning' : 'success'"
                              width="134px"
                              height="30px"
                              @click="onVicepresidentClick(item.name, item.rank)"
                            >
                              <v-icon color="black" small>
                                {{
                                  isVicepresident(item.rank)
                                    ? 'mdi-minus-circle'
                                    : 'mdi-plus-circle'
                                }}
                              </v-icon>
                              <span class="black--text custom-text-noto text-caption ml-2">
                                {{ $t('guild_vicepresident') }}
                              </span>
                            </v-btn>
                          </v-col>
                          <v-col align-self="end" cols="12">
                            <v-btn
                              v-if="showKickButton(guildRank, item.rank)"
                              outlined
                              height="30px"
                              color="error"
                              @click="onRemoveClick(item.name)"
                            >
                              <span class="error--text custom-text-noto text-caption">
                                {{ $t('kick_guild') }}
                              </span>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </div>
                    </div>
                  </td>
                </tr>
                <v-divider class="default-content-2"></v-divider>
              </div>
            </tbody>
          </template>
          <!--mobile body-->
          <!--New body-->
          <template v-slot:item="{ item }">
            <tr>
              <td class="px-0">
                <v-menu
                  class="px-0"
                  :disabled="isSelf(item.name)"
                  offset-y
                  absolute
                  content-class="easyPlayer-custom-border"
                >
                  <template class="px-0" v-slot:activator="{ on, attrs }">
                    <div
                      @click="onUserInfoClick(item.name, guildInfoName)"
                      v-bind="attrs"
                      v-on="on"
                      class="h-100 d-flex justify-center align-center cursor-pointer"
                    >
                      <span
                        v-if="item.rank === 1"
                        class="text-center custom-text-noto text-body-2 default-content--text text-no-wrap"
                      >
                        {{ $t('member') }}
                      </span>
                      <v-img
                        v-else
                        :src="getPresidentImg3x(item.rank)"
                        :srcset="`${getPresidentImg(item.rank)} 1x, ${getPresidentImg3x(
                          item.rank
                        )} 2x`"
                        max-width="40"
                        max-height="40"
                      />
                    </div>
                  </template>
                  <easyPlayerInfo
                    tile
                    report
                    is-card
                    action-bar
                    only-coin
                    :player-info="playerInfo"
                    style="min-width: 300px"
                    badge-type="relation"
                  />
                </v-menu>
              </td>
              <td class="px-0">
                <v-menu
                  :disabled="isSelf(item.name)"
                  offset-y
                  absolute
                  content-class="easyPlayer-custom-border"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <div
                      @click="onUserInfoClick(item.name, guildInfoName)"
                      v-bind="attrs"
                      v-on="on"
                      class="d-flex justify-center cursor-pointer"
                    >
                      <v-badge
                        bordered
                        bottom
                        :color="item.onlines ? 'success' : 'offline'"
                        dot
                        offset-x="24"
                        offset-y="20"
                      >
                        <v-list-item-avatar>
                          <v-img :src="item.avatar" contain @error="errorImgHandler(item)">
                            <template v-slot:placeholder>
                              <v-row class="fill-height ma-0" align="center" justify="center">
                                <v-img :src="defaultImg" contain />
                              </v-row>
                            </template>
                          </v-img>
                        </v-list-item-avatar>
                      </v-badge>
                    </div>
                  </template>
                  <easyPlayerInfo
                    tile
                    report
                    is-card
                    action-bar
                    only-coin
                    :player-info="playerInfo"
                    style="min-width: 300px"
                    badge-type="relation"
                  />
                </v-menu>
              </td>
              <td class="px-0">
                <v-menu
                  :disabled="isSelf(item.name)"
                  offset-y
                  absolute
                  content-class="easyPlayer-custom-border"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <div
                      @click="onUserInfoClick(item.name, guildInfoName)"
                      v-bind="attrs"
                      v-on="on"
                      class="px-4 h-100 d-flex align-center"
                    >
                      <span class="text-center custom-text-noto text-subtitle-1 primary--text">
                        {{ item.name }}
                      </span>
                    </div>
                  </template>
                  <easyPlayerInfo
                    tile
                    report
                    is-card
                    action-bar
                    only-coin
                    :player-info="playerInfo"
                    style="min-width: 300px"
                    badge-type="relation"
                  />
                </v-menu>
              </td>
              <td v-if="guildRank > 1 && !isCard">
                <div v-if="!isGuildOwner(item.rank)" class="d-flex justify-around">
                  <!--目前討論為任命副會長功能先暫時隱藏  看後續狀況再做開放 考慮此為狀況未定   故先用 false 直接隱藏 後續如有特別訂在  在移置UI Config-->
                  <v-btn
                    v-if="isGuildOwner(guildRank) && false"
                    class="mx-1 px-0 d-flex justify-start"
                    depressed
                    :color="isVicepresident(item.rank) ? 'warning' : 'success'"
                    width="134px"
                    height="30px"
                    @click="onVicepresidentClick(item.name, item.rank)"
                  >
                    <v-icon class="ml-3" color="black" small>
                      {{ isVicepresident(item.rank) ? 'mdi-minus-circle' : 'mdi-plus-circle' }}
                    </v-icon>
                    <span class="black--text custom-text-noto text-caption ml-2">
                      {{ $t('guild_vicepresident') }}
                    </span>
                  </v-btn>
                  <div v-if="showKickButton(guildRank, item.rank)" class="mx-1">
                    <div
                      class="d-flex align-center justify-center outline-button"
                      v-ripple
                      @click="onRemoveClick(item.name)"
                    >
                      <span class="error--text custom-text-noto text-caption">
                        {{ $t('kick_guild').toUpperCase() }}
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </template>
          <!--New body-->
          <template v-if="$vuetify.breakpoint.xsOnly" v-slot:footer="{ props: { pagination } }">
            <div class="v-data-footer">
              <v-row no-gutters>
                <v-col>
                  <div class="v-data-footer__select d-flex justify-start ml-3">
                    <span> {{ $t('items_per_page') }}</span>
                    <v-select
                      class="py-0 mt-3 mb-3"
                      v-model="select"
                      hide-details
                      height="32"
                      @input="onSelect"
                      :items="pagePaginationitem(itemsPerPageOptions)"
                    ></v-select>
                    <span class="v-data-footer__pagination">
                      {{ pagePagination(pagination) }}
                    </span>
                  </div>
                </v-col>
              </v-row>
              <v-row no-gutters>
                <v-col>
                  <v-btn
                    class="v-data-footer__icons-before"
                    icon
                    :disabled="pagination.pageStart === 0"
                    @click="currentPage = pagination.page - 1 === 0 ? 1 : pagination.page - 1"
                  >
                    <v-icon dark> mdi-chevron-left </v-icon>
                  </v-btn>
                  <v-btn
                    class="v-data-footer__icons-after"
                    icon
                    :disabled="pagination.pageStop === pagination.itemsLength"
                    @click="
                      currentPage =
                        pagination.page + 1 === pagination.pageCount
                          ? pagination.pageCount
                          : pagination.page + 1
                    "
                  >
                    <v-icon dark> mdi-chevron-right </v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </v-data-table>
        <guildConfirmDialog
          v-if="guildConfirmStatus"
          :confirm-obj="confirmObj"
          :show-guild-confirm-dialog-status.sync="guildConfirmStatus"
        />
      </template>
    </v-col>
  </v-row>
</template>

<script>
  import images from '~/mixins/images'
  import guildMgr from '~/mixins/guildMgr'
  import cloneDeep from 'lodash/cloneDeep'
  import scssLoader from '~/mixins/scssLoader.js'
  import relationship from '~/mixins/relationship'
  export default {
    name: 'guildInfoList',
    components: {
      easyPlayerInfo: () => import('~/components/player_info/easyPlayerInfo'),
      guildConfirmDialog: () => import('~/components/guild/guildConfirmDialog')
    },
    props: {
      isCard: {
        type: Boolean,
        default: false
      },
      isInfoPage: {
        type: Boolean,
        default: false
      },
      height: {
        type: [Number, String],
        default: 550
      },
      itemsPerPage: {
        type: Number,
        default: 15
      },
      showGuildRank: {
        type: Number,
        default: 1
      },
      guildInfoName: {
        type: String,
        default: ''
      },
      itemsPerPageOptions: {
        type: Array,
        default: () => [10, 20, 30, -1]
      },
      guildInfoList: {
        type: Array,
        default: () => []
      }
    },
    mixins: [scssLoader, guildMgr, images, relationship],
    data() {
      return {
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        guildInfoShowList: [],
        select: this.itemsPerPage,
        itemsPage: this.itemsPerPage,
        currentPage: 1,
        guildConfirmStatus: false,
        confirmObj: {},
        guildMasterListHeaders: [
          {
            text: this.$t('guild_class'),
            value: 'rank',
            align: 'center',
            width: '10%',
            class: 'grey-4 primary--text px-0',
            sortable: false
          },
          {
            text: this.$t('avatar'),
            value: 'avatar',
            align: 'center',
            width: '10%',
            class: 'grey-4 primary--text ',
            sortable: false
          },
          {
            text: this.$t('nickname'),
            value: 'name',
            width: '25%',
            class: 'grey-4 primary--text ',
            sortable: false
          },
          {
            text: this.$t('operation'),
            value: 'operation',
            width: '25%',
            class: 'grey-4 primary--text ',
            sortable: false
          }
        ],
        guildListHeaders: [
          {
            text: this.$t('guild_class'),
            value: 'rank',
            align: 'center',
            width: '10%',
            class: 'grey-4 primary--text px-0',
            sortable: false
          },
          {
            text: this.$t('avatar'),
            value: 'avatar',
            align: 'center',
            width: '10%',
            class: 'grey-4 primary--text ',
            sortable: false
          },
          {
            text: this.$t('nickname'),
            value: 'name',
            width: '40%',
            class: 'grey-4 primary--text ',
            sortable: false
          }
        ],
        playerInfo: {
          username: '',
          level: 0,
          levelVip: 0,
          money: 0,
          thumbPath: '',
          online: false,
          guildName: ''
        }
      }
    },
    watch: {
      guildInfoList: {
        async handler(value) {
          this.guildInfoShowList = cloneDeep(value)
        }
      }
    },
    computed: {
      friendList({ $store }) {
        return $store.getters['social/friendList']
      },
      ownName({ $store }) {
        return $store.getters['role/userName']
      },
      selfGuildName({ $store }) {
        return $store.getters['guild/guildName']
      },
      displayList() {
        return this.guildInfoShowList.map((item) => ({ ...item }))
      }
    },

    async mounted() {
      this.guildInfoShowList = cloneDeep(this.guildInfoList)
    },

    methods: {
      errorImgHandler(item) {
        item.avatar = this.defaultImg
      },
      isSelf(username) {
        return this.ownName === username
      },
      isSelfGuild() {
        return this.selfGuildName === this.guildInfoName
      },
      pagePaginationitem(itemArray) {
        let newItemArray = cloneDeep(itemArray)
        newItemArray[newItemArray.findIndex((x) => x === -1)] = this.$t('all')
        return newItemArray
      },
      onSelect() {
        if (this.select === this.$t('all')) this.itemsPage = -1
        else this.itemsPage = this.select
      },
      scrollToTop() {
        try {
          window.scrollTo({ top: 0, behavior: 'auto' })
        } catch (error) {
          console.log(error)
        }
      },
      handlePageChange(page) {
        this.currentPage = page
      },
      async handleCurrentItems(items) {
        const originalList = this.guildInfoShowList
        await this.updateAvatarsRecursive(items, originalList)
        this.$forceUpdate()
      },
      async updateAvatarsRecursive(items, originalList, index = 0) {
        if (index >= items.length) return
        const currentItem = items[index]
        try {
          const newAvatar = await this.getUserUrl(currentItem)
          const originalItem = originalList.find((item) => item.name === currentItem.name)
          if (originalItem) {
            originalItem.avatar = newAvatar
            currentItem.avatar = newAvatar
          }
        } catch (error) {
          const originalItem = originalList.find((item) => item.name === currentItem.name)
          if (originalItem) {
            originalItem.avatar = this.defaultImg
            currentItem.avatar = this.defaultImg
          }
        }
        await this.updateAvatarsRecursive(items, originalList, index + 1)
      },
      pagePagination(pagination) {
        return pagination.pageCount === 0
          ? '-'
          : `${pagination.pageStart + 1}-${pagination.pageStop} ${this.$t('total_page')}
              ${pagination.itemsLength} ${this.$t('quantity')}`
      },
      async onRemoveClick(userName) {
        const remove = async () => {
          await this.removeGuildMembership(userName)
        }
        this.confirmObj = {
          confirm: 'kick_guild',
          confirmInfo: this.$t('guild_kick_member_noty', { player: userName }),
          cancel: 'guild_think',
          onConfirmNotify: remove
        }
        this.guildConfirmStatus = true
      },
      async onVicepresidentClick(userName, rank) {
        const sendVicepresidentEvent = async () => {
          if (rank === 1) await this.addGuildVicePresident(userName)
          if (rank === 2) await this.removeGuildVicePresident(userName)
        }
        this.confirmObj = {
          confirm: rank === 1 ? 'appoint' : 'lift',
          confirmInfo: this.$t(
            rank === 1 ? 'appoint_guild_vicepresident' : 'lift_guild_vicepresident',
            { player: userName }
          ),
          cancel: 'cancel',
          onConfirmNotify: sendVicepresidentEvent
        }
        this.guildConfirmStatus = true
      },
      getPresidentImg(rank) {
        switch (rank) {
          case 2:
            return this.getImage('guild/icon_President2.png')
          case 3:
            return this.getImage('guild/icon_President.png')
        }
      },
      getPresidentImg3x(rank) {
        switch (rank) {
          case 2:
            return this.getImage('guild/<EMAIL>')
          case 3:
            return this.getImage('guild/<EMAIL>')
        }
      },
      async getUserUrl(item) {
        const index = this.friendList.findIndex((friendItem) => friendItem.username === item.name)
        const userData = { userName: item.name }
        return index !== -1
          ? this.friendList[index].thumbPath
          : await this.$store.dispatch('role/getThumbUrl', userData)
      },
      async showInfoCardDialog(userName, guildName) {
        this.$nuxt.$loading.start()
        const role = await this.getPlayerData(userName, guildName)
        this.setSelectPlayerInfo(role)
        this.$nuxt.$loading.finish()
        this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', true)
      },
      async onUserInfoClick(userName, guildName) {
        // 先獲取空數據，以免顯示上一個用戶資訊
        this.playerInfo = {
          username: '',
          level: 0,
          levelVip: 0,
          money: 0,
          thumbPath: '',
          online: false,
          guildName: ''
        }
        if (this.isSelf(userName)) this.showInfoCardDialog(userName, guildName)
        else this.playerInfo = await this.getPlayerData(userName, guildName)
        this.updatePlayerInfo(this.playerInfo)
      },
      async updatePlayerInfo(userdata) {
        await this.$store.dispatch('social/setSingleFriendStatus', userdata)
      },
      showKickButton(guildRank, rank) {
        return guildRank > rank
      },
      isGuildOwner(rank) {
        return rank === 3
      },
      isVicepresident(rank) {
        return rank === 2
      },
      showOperation(rank) {
        return rank > 1 && this.isSelfGuild() && this.isInfoPage
      }
    }
  }
</script>

<style lang="scss">
  $grey-4: map-get($colors, 'grey-4');
  $grey-6: map-get($colors, 'grey-6');
  $error: map-get($colors, 'error');
  #guild-list-data-table {
    .character-info-avatar-badge {
      position: relative;
      .badge-border {
        position: absolute;
        top: 0;
      }
    }
    .v-data-table-header-mobile {
      th {
        background: $grey-4 !important;
      }
    }
    .v-data-table {
      tbody {
        tr {
          &:hover {
            background: $grey-4 !important;
          }
        }
      }
    }
    .h-100 {
      height: 100%;
    }
    .outline-button {
      display: inline-block;
      width: 90px;
      height: 30px;
      padding: 4px 16px;
      border: 1px solid $error; /* 使用顏色 #2196F3 作為輪廓顏色 */
      border-radius: 4px;
      color: $error;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }
  }
</style>
