<template>
  <v-footer
    v-if="!showGameModeStatus"
    color="footer-fill"
    class="pt-4 pb-10 px-0"
    :class="{
      'pb-0': $vuetify.breakpoint.smOnly,
      'mb-10': $vuetify.breakpoint.smAndDown,
      'notch-left': hasLeftNotch,
      'notch-right': hasRightNotch
    }"
    width="100%"
    bottom
  >
    <v-row no-gutters justify="center" class="px-lg-0 px-md-6 px-4 w-100">
      <v-col cols="12" lg="9" class="mw-75-v">
        <v-sheet class="footer-fill mx-auto vh-height-60">
          <v-slide-group v-model="model" class="slide-group" center-active show-arrows>
            <v-slide-item
              v-for="(data, index) in footerConfig.creditList"
              class="d-flex align-center py-4"
              :key="index"
              v-slot="{ toggle }"
            >
              <v-card
                :class="['custom-card', 'no-cursor', data.class]"
                elevation="0"
                :height="data.height"
                :width="data.width"
                @click="toggle"
              >
                <v-img
                  contain
                  :height="data.height"
                  :width="data.width"
                  :src="require('~/assets/image/footerLogo/' + data.img)"
                >
                </v-img>
              </v-card>
            </v-slide-item>
          </v-slide-group>
        </v-sheet>
        <v-divider class="my-4"></v-divider>
      </v-col>
    </v-row>

    <v-row no-gutters justify="center" class="px-xl-0 px-lg-0 px-md-6 px-sm-4 px-4">
      <v-col cols="12" lg="9" class="mw-75-v">
        <!-- news & download 2 btn -->
        <v-row no-gutters class="custom-text-noto">
          <!-- 建立捷徑 -->
          <v-btn text class="small" @click="toPage('/teach/pwa')">
            <v-row no-gutters align="center" class="text-button footer-button--text">
              <v-icon>mdi-tray-arrow-down</v-icon>
              <span class="ml-2">{{ $t('shortcut_tutorial') }}</span>
            </v-row>
          </v-btn>
        </v-row>
        <!-- contact & notice -->
        <v-row no-gutters justify="space-between" class="pt-4">
          <!-- contact -->
          <v-col class="custom-text-noto text-body-2" cols="12" md="6">
            <v-row no-gutters class="white--text pr-5">
              <v-col cols="12">
                <div class="d-flex justify-start align-center flex-nowrap mb-4">
                  <v-img
                    max-width="90"
                    height="25"
                    contain
                    :src="require('~/assets/image/japan_01/logo-pacify.png')"
                  />
                  <v-divider class="mx-4" vertical></v-divider>
                  <v-img
                    max-width="153"
                    height="25"
                    contain
                    :src="require('~/assets/image/japan_01/logo.png')"
                  />
                </div>
              </v-col>
              <v-col cols="12">
                <p class="mb-0">
                  PACIFY SERVICE CO., LTD.<br />
                  {{ 'Contact：' + serviceMail }}
                </p>
              </v-col>
            </v-row>
          </v-col>
          <!-- notice -->
          <v-col
            class="white--text custom-text-noto text-body-2 pt-xl-0 pt-lg-0 pt-md-0 pt-sm-4 pt-4"
            cols="12"
            md="6"
          >
            <v-row no-gutters class="flex-nowrap">
              <div>
                <ul type="disc" class="ml-4 mb-0 white--text custom-text-noto text-body-2">
                  <li v-for="(noty, index) in footerConfig.footerNotyArray" :key="index">
                    {{ $t(noty) }}
                  </li>
                </ul>
              </div>
            </v-row>
          </v-col>
          <v-col cols="12" class="pt-4"> <v-divider /> </v-col>
        </v-row>
        <!-- license & lanuage -->
        <v-row no-gutters align="center" justify="space-between" class="pt-4 pb-2">
          <!-- license -->
          <v-col cols="12" md="6" class="custom-text-noto text-body-2">
            <v-card elevation="0" color="transparent" class="pr-5">
              <v-btn
                v-for="(info, index) in companyInfoList"
                :key="index"
                elevation="0"
                target="blank"
                text
                :small="$vuetify.breakpoint.xsOnly ? true : false"
                class="no-shadow px-0"
                color="transparent"
                @click="openDialog(info.id)"
              >
                <span class="white--text">
                  {{ $t(info.name) }}
                </span>
                <span v-show="index !== companyInfoList.length - 1" class="white--text px-1">
                  |
                </span>
              </v-btn>
            </v-card>
          </v-col>
          <!-- lanuage -->
          <v-col cols="12" md="6" class="pa-0 pt-xl-0 pt-lg-0 pt-md-0 pt-4">
            <v-card elevation="0" color="transparent" class="d-flex align-center justify-start">
              <locale v-if="localeStatus" />
              <v-card
                v-for="(item, idx) in footerConfig.communityList"
                :key="idx"
                class="cursor-pointer rouned-circle"
                :class="localeStatus ? 'ml-3' : 'ml-1 mr-2'"
                color="transparent"
                width="24"
                height="24"
                @click="goTo(item.link)"
              >
                <v-img
                  v-if="item.iconFile"
                  :src="getImage('icon/' + item.iconFile)"
                  width="100%"
                  height="100%"
                />
                <v-icon v-if="item.icon" class="footer-item--text white" v-text="item.icon" />
              </v-card>
            </v-card>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-footer>
</template>

<script>
  import images from '~/mixins/images'
  import orientation from '@/mixins/orientation.js'
  const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
  const STATION = process.env.STATION
  const stationConfig = require(`@/station/${STATION}/${NUXT_ENV}`).default

  export default {
    mixins: [images, orientation],
    components: {
      //翻譯 目前不需要  先註解
      locale: () => import('~/components/locale')
    },

    data() {
      const localeStatus = stationConfig.customLocaleList.length > 1

      return {
        localeStatus,
        footerList: this.$store.getters['navigation/footer'],
        model: null
      }
    },
    computed: {
      footerConfig() {
        return this.$UIConfig.footer
      },
      companyInfoList({ $store }) {
        return $store.getters[`${STATION}/companyInfo/list`]
      },
      serviceMail({ $store }) {
        return $store.getters[`${STATION}/companyInfo/serviceMail`]
      },
      showGameModeStatus({ $store }) {
        return $store.getters['menu/showGameModeStatus']
      }
    },
    methods: {
      goTo(link) {
        this.$lineOpenWindow.open(link, '_blank')
      },
      toPage(link) {
        //換頁
        this.$router.push({ path: this.localePath(link) })
      },
      openDialog(type) {
        this.$store.commit(`${STATION}/companyInfo/SET_COMPANY_POLICY_TYPE`, type)
        this.$nuxt.$emit('root:showCompanyDialogStatus', true)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .vh-height-60 {
    height: 60px !important;
    display: flex;
    align-items: center !important;
  }
  .slide-group {
    display: flex;
    align-items: center !important;
  }
  @media screen and (max-width: 627px) {
    .no-cursor {
      cursor: pointer !important;
    }
  }
  @media screen and (min-width: 628px) {
    .no-cursor {
      cursor: default !important;
    }
  }

  :deep(.v-slide-group__prev),
  :deep(.v-slide-group__next) {
    width: 44px !important; /* 設定寬度 */
    height: 44px !important; /* 設定高度 */
    min-width: 44px !important; /* 確保最小寬度 */
    flex: 0 0 44px !important; /* 防止伸縮 */
  }

  /* 設定按鈕本身的大小 */
  :deep(.custom-arrow-btn) {
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
  }

  /* 移除 v-card 的漣漪效果 */
  :deep(.custom-card::before),
  :deep(.custom-card::after) {
    display: none !important;
  }

  :deep(.custom-card .v-ripple__container) {
    display: none !important;
  }

  /* 移除 v-slide-item 的漣漪效果 */
  :deep(.v-slide-item::before),
  :deep(.v-slide-item::after) {
    display: none !important;
  }

  :deep(.v-slide-item .v-ripple__container) {
    display: none !important;
  }

  /* 移除點擊時的背景效果 */
  :deep(.v-card--variant-elevated) {
    background: transparent !important;
  }

  /* 移除 hover 效果 */
  :deep(.custom-card:hover::before),
  :deep(.custom-card:hover::after),
  :deep(.v-slide-item:hover::before),
  :deep(.v-slide-item:hover::after) {
    display: none !important;
    opacity: 0 !important;
  }

  /* 移除選中效果 */
  :deep(.v-slide-item--active) {
    transform: none !important;
  }

  /* 移除所有轉換效果 */
  :deep(.custom-card),
  :deep(.v-slide-item) {
    transition: none !important;
  }

  /* 確保圖片不受影響 */
  :deep(.v-img) {
    transition: none !important;
    transform: none !important;
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
