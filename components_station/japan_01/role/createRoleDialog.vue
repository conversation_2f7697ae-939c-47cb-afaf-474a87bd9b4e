<template>
  <div>
    <v-dialog
      v-model="showCreateRoleDialogStatusTemp"
      :fullscreen="breakpoint.xsOnly"
      persistent
      max-width="600"
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card class="dialog-fill">
        <customDialogTitle :title="$t('create_role').toUpperCase()" @closeDialog="closeDialog" />
        <div id="create-role-card" :class="breakpoint.xsOnly ? 'scrollable-xs' : 'scrollable-sm'">
          <v-card-text class="pt-4 px-4 pb-4 pt-sm-6 px-sm-6">
            <!-- 倒數計時 -->
            <v-row no-gutters class="pb-6">
              <span class="material-symbols-outlined pr-1"> nest_clock_farsight_analog </span>
              <span>{{ $t('countdown') }}</span
              ><span class="warning--text pl-1"> {{ loginLastTime }} </span>
              <span>{{ $t('second') }}</span>
            </v-row>
            <v-row no-gutters>
              <template v-if="isFromFacebook">
                <v-col class="d-flex justify-center mt-sm-2" xl="3" lg="3" md="3" sm="3" cols="12">
                  <v-img :src="facebookUrl" max-height="130px" max-width="130px">
                    <template v-if="facebookId === ''" #default>
                      <!-- 暫時先用灰色方塊代替 之後要改成用FB的頭像 -->
                      <v-sheet color="grey-4" height="130" width="130" />
                    </template>
                  </v-img>
                </v-col>
              </template>
              <!-- choose_avatar -->
              <template v-else>
                <v-col xl="5" lg="5" md="5" sm="5" cols="12">
                  <!-- title -->
                  <v-row no-gutters class="pb-2">
                    <v-col cols="12">
                      <span class="custom-text-noto text-body-2 default-content--text">{{
                        $t('choose_avatar')
                      }}</span>
                    </v-col>
                  </v-row>
                  <!-- imgs -->
                  <v-row
                    no-gutters
                    id="sticker-box"
                    class="rounded grey-5 py-1 px-1"
                    style="height: 210px; overflow: auto"
                  >
                    <template v-if="breakpoint.smAndUp">
                      <v-col
                        v-for="(sticker, index) in stickerList"
                        :key="sticker"
                        cols="4"
                        class="d-flex justify-center align-center py-1 px-1 sticker-item"
                      >
                        <v-img
                          :src="sticker"
                          max-height="56px"
                          max-width="56px"
                          :class="[
                            'cursor-pointer',
                            { 'gradient-primary-border': stickerSelected === index }
                          ]"
                          @click="stickerSelected = index"
                        />
                      </v-col>
                    </template>
                    <template v-else>
                      <div id="sticker-container">
                        <div id="sticker-list">
                          <div
                            v-for="(sticker, index) in stickerList"
                            :key="sticker"
                            class="d-flex justify-center align-center sticker-item"
                          >
                            <v-img
                              :src="sticker"
                              max-height="56px"
                              max-width="56px"
                              :class="[
                                'cursor-pointer',
                                { 'gradient-primary-border': stickerSelected === index }
                              ]"
                              @click="stickerSelected = index"
                            />
                          </div>
                        </div>
                      </div>
                    </template>
                  </v-row>
                </v-col>
              </template>
              <!-- form -->
              <v-col
                :xl="isFromFacebook ? '9' : '7'"
                :lg="isFromFacebook ? '9' : '7'"
                :md="isFromFacebook ? '9' : '7'"
                :sm="isFromFacebook ? '9' : '7'"
                cols="12"
                :class="[
                  { 'pt-4': !isFromFacebook && breakpoint.xsOnly },
                  { 'pt-6': isFromFacebook && breakpoint.xsOnly },
                  { 'pl-4 pt-7': !isFromFacebook && breakpoint.smAndUp },
                  { 'pl-2': isFromFacebook && breakpoint.smAndUp }
                ]"
              >
                <v-form>
                  <!-- 角色暱稱 -->
                  <v-text-field
                    v-model="inputRoleName"
                    v-validate="{
                      required: true,
                      name_validate: true,
                      text_fullwidth: true,
                      inappropriate_words: compiledData,
                      validate_char_length: 12
                    }"
                    :label="$t('role_name') + '*'"
                    filled
                    shaped
                    :error-messages="errors.first('create_role.role_name')"
                    :data-vv-as="$t('role_name')"
                    data-vv-name="role_name"
                    data-vv-scope="create_role"
                  />
                  <!-- FB暱稱 -->
                  <v-text-field
                    v-if="isFromFacebook"
                    v-model="fbName"
                    :placeholder="$t('fb_name')"
                    background-color="grey-5"
                    filled
                    :error-messages="errors.first('create_role.fb_name')"
                    :data-vv-as="$t('fb_name')"
                    data-vv-name="fb_name"
                    data-vv-scope="create_role"
                    disabled
                  />
                </v-form>
                <!-- create_role_description -->

                <div
                  class="custom-text-noto text-body-2 default-content--text"
                  :class="[$UIConfig.role.roleDescriptionSetting]"
                >
                  <li>
                    {{ $t('create_role_description') }}
                  </li>
                  <li>
                    {{ $t('create_role_description2') }}
                  </li>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions class="pt-0 px-4 px-sm-6 pb-4 pb-sm-6">
            <v-spacer v-if="breakpoint.smAndUp" />

            <v-btn
              :color="$UIConfig.defaultBtnColor"
              class="button-content--text"
              :disabled="!validateStatus"
              :block="breakpoint.xsOnly"
              @click="prepareCreateRole"
              elevation="0"
            >
              {{ $t('create_role') }}
            </v-btn>
          </v-card-actions>
        </div>
      </v-card>
    </v-dialog>
    <createRoleConfirmDeleteDialog
      :create-role-confirm-delete-dialog-status.sync="createRoleConfirmDeleteDialogStatus"
    />
  </div>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import urlQueryGameType from '@/mixins/urlQueryGameType'
  import uploadPhoto from '@/mixins/uploadPhoto.js'
  import literalPrison from '@/mixins/literalPrison.js'
  import analytics from '@/mixins/analytics.js'
  import facebook from '~/mixins/facebook.js'
  import images from '~/mixins/images'
  export default {
    name: 'CreateRoleDialog',
    mixins: [
      hiddenScrollHtml,
      urlQueryGameType,
      images,
      analytics,
      uploadPhoto,
      literalPrison,
      facebook
    ],
    props: {
      showCreateRoleDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      createRoleConfirmDeleteDialog: () => import('~/components/role/createRoleConfirmDeleteDialog')
    },
    created() {
      this.startLoginTimer()
    },
    async mounted() {
      this.stickerList = this.getStickers()
      this.facebookUrl = await this.getUserAvatar()
    },
    data() {
      return {
        showCreateRoleDialogStatusTemp: this.showCreateRoleDialogStatus,
        stickerList: [],
        stickerSelected: 0,
        inputRoleName: '',
        validateStatus: false,
        nicknameRepeatErrorMsg: '',
        facebookUrl: '',
        createRoleConfirmDeleteDialogStatus: false
      }
    },
    computed: {
      isFromFacebook({ $store }) {
        return $store.getters['role/loginType'] === 4
      },
      isThirdParty({ $store }) {
        const type = $store.getters['role/loginType']
        return type === 6 || type === 5 || type === 4
      },
      fbName({ $store }) {
        return $store.getters['role/fbName']
      },
      loginType({ $store }) {
        return $store.getters['role/loginType']
      },
      roleListCount({ $store }) {
        return $store.getters['role/roleCount']
      },
      isBindPhone({ $store }) {
        return $store.getters['role/phoneNumber'].length > 0
      },
      vipLevel({ $store }) {
        return $store.getters['role/vipLevel']
      },
      loginRecord({ $store }) {
        return $store.getters['role/loginRecord']
      },
      //倒數計時
      loginLastTime({ $store }) {
        return $store.getters['role/loginLastTime']
      },
      //倒數計時狀態
      loginTimmerState({ $store }) {
        return $store.getters['role/loginTimmerState']
      },
      userDefalutAvatar({ $store }) {
        return $store.getters['role/userDefalutAvatar']
      },
      userName() {
        return this.$store.getters['role/userName']
      },
      compiledData({ $store }) {
        return $store.getters['literalKeywords/compiledData']
      },
      showGameModeStatus({ $store }) {
        return $store.getters['menu/showGameModeStatus']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showCreateRoleDialogStatus: {
        handler(val) {
          this.showCreateRoleDialogStatusTemp = val
        },
        immediate: true
      },
      showCreateRoleDialogStatusTemp: {
        async handler(val) {
          if (val) {
            this.goStickersTop()
            if (this.roleListCount >= 5) {
              await this.$nextTick(() => {
                const title = this.$t('reminder')
                const message = '<p class="mb-0 text-wrap">' + this.$t('create_role_limit') + '</p>'
                this.showNotyDialog(title, message)
                this.$nuxt.$emit('root:showCreateRoleDialogStatus', false)
                this.$nuxt.$emit('root:showRoleDialogStatus', true)
                this.$nuxt.$emit('root:showLoginDialogStatus', {
                  show: false,
                  onCancelNotify: () => {}
                })
              })
            }
            this.$validator.reset()
          } else {
            this.resetRoleForm()
            this.resetStickerSelected()
          }
        },
        immediate: true
      },
      inputRoleName: {
        async handler() {
          const validateStatus = await this.validateForm()
          this.stickerList[this.stickerSelected] && validateStatus
            ? (this.validateStatus = true)
            : (this.validateStatus = false)
        }
      },
      loginLastTime: {
        handler(val) {
          if (val <= 0) {
            this.logout()
            this.$nuxt.$emit('root:showNotyDialogStatus', false)
            this.$nuxt.$emit('root:showLogoutDialogStatus', false)
            this.$nuxt.$emit('root:showDeviceWhiteDialogStatus', false)
          }
        }
      }
    },
    methods: {
      getStickers() {
        const images = this.userDefalutAvatar
        const imageUrls = images.map((x) => this.getImage('photo_stickers/' + x))
        return imageUrls
      },
      async validateForm() {
        return await this.$validator.validateAll('create_role')
      },
      // 串接送發角色資訊 websocket所使用
      async sendRoleHandler(roleName) {
        const promote = this.$store.getters['social/promote']
        this.$wsClient.send(
          this.$wsPacketFactory.addOverseaCharacter({ username: roleName, promote })
        )
        const res = await this.$xinUtility.waitEvent(
          this.$wsClient.receivedListeners,
          (data) =>
            data.isFeature(this.$xinConfig.FEATURE.ROLE.TYPE.CREATE) ||
            data.isFeature(this.$xinConfig.FEATURE.LOGIN.ID)
        )
        return res
      },
      async prepareCreateRole() {
        if (this.containsKeywords(this.inputRoleName)) {
          this.$notify.error(this.$t('literalNoty'))
          return
        }
        const checkText = (text) =>
          /^(j[o0]y[o0]uspa[li][ac]ce|[o0]ff[il1]c[il1]a[l1i])$/i.test(text)
        if (checkText(this.inputRoleName)) {
          this.$notify.error(this.$t('lang_07'))
          return
        }
        let message = ''
        await this.validateForm()
        // 如果沒有選擇照片
        if (!this.stickerList[this.stickerSelected])
          message = '<p class="mb-0 text-wrap">' + this.$t('role_sticker_error') + '</p>'
        // 傳送角色資訊後,並上傳照片,加入角色列表
        else {
          const role = {
            username: this.inputRoleName
          }
          const res = await this.sendRoleHandler(role.username)
          let label
          switch (this.loginType) {
            case 6:
              label = 'Webapple'
              break
            case 4:
              label = 'Webfacebook'
              break
            case 5:
              label = 'Webgoogle'
              break
            default:
              label = ''
              break
          }
          if (this.isThirdParty) {
            try {
              //因流程可能會未登入成功(角色在線、裝置驗證、裝置驗證失敗)
              if (
                res.type === 0 &&
                res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.PLAYER_INFO.ID
              ) {
                //成功登入時將倒數計時暫停
                this.$store.commit('role/SET_LOGIN_TIMMER_STATE', false)
                // 先發送前三個服務的加入請求
                const initialServices = [
                  this.$xinConfig.LOTTERY_SERVICE.ID, // 進入摸彩服務
                  this.$xinConfig.GAME_SERVICE.ID, // 進入遊戲服務
                  this.$xinConfig.SOCIAL_SERVICE.ID // 進入社群服務
                ]

                initialServices.forEach((serviceId) => {
                  this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(serviceId))
                  this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
                    serviceId,
                    enable: true,
                    connected: true
                  })
                })

                // 等待 SOCIAL_SERVICE 確認
                await this.$xinUtility
                  .waitEvent(
                    this.$wsClient.receivedListeners,
                    (data) =>
                      data.protocolId === this.$xinConfig.PROTOCOL_ID.SERVICE &&
                      data.serviceId === this.$xinConfig.SOCIAL_SERVICE.ID
                  )
                  .then(() => {
                    this.$wsClient.send(this.$wsPacketFactory.initMail())
                    // 延遲五秒後開啟新信件通知
                    setTimeout(() => {
                      this.$store.commit('mail/SET_FIRST_RECIVE', false)
                    }, 5000)
                  })
                  .catch((err) => {
                    console.log('SOCIAL_SERVICE ERROR:', err)
                  })

                // 初始化角色資料
                await this.$store.dispatch('role/profileInit', res)
                // 再加入公會服務
                this.$wsClient.send(
                  this.$wsPacketFactory.getServiceJoin(this.$xinConfig.GUILD_SERVICE.ID)
                )
                this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
                  serviceId: this.$xinConfig.GUILD_SERVICE.ID,
                  enable: true,
                  connected: true
                })

                //取得與伺服器時間差
                await this.$store.dispatch('xinProtocol/getServerLocalTimeDiff')

                //每次臉書登入都上傳照片
                if (label === 'Webfacebook') {
                  //上傳fb照片
                  this.uploadImgRes(this.userName, this.facebookUrl)
                } else {
                  const avatar = this.stickerList[this.stickerSelected]
                  const uploadImgRes = await this.uploadImgRes(this.userName, avatar)
                  if (uploadImgRes === 1) {
                    this.$store.commit('role/INSERT_LIST', {
                      username: this.userName,
                      level: res.level,
                      online: res.online ? 1 : 0,
                      photo: res.photo
                    })
                  }
                  const userData = { userName: this.userName, getCache: false }
                  const thumbUrl = await this.$store.dispatch('role/getThumbUrl', userData)
                  this.$store.commit('role/SET_THUMBURL', thumbUrl)
                }

                this.$nuxt.$emit('root:showLoginDialogStatus', {
                  show: false,
                  onCancelNotify: () => {}
                })
                this.$nuxt.$emit('root:showCreateRoleDialogStatus', false)
                //第三方登入 登入成功後 送出登入事件至google analytics 與 facebook analytics
                this.loginAnalytics(label)
                if (
                  Object.prototype.hasOwnProperty.call(res, 'lastLogoutTime') &&
                  res.lastLogoutTime === '1900/01/01 00:00:00'
                ) {
                  this.createRoleAnalytics()
                }
              } else if (res.commandId === 136) {
                this.$notify.info(res.message)
                this.$nuxt.$emit('root:showCreateRoleDialogStatus', false)
                this.$nuxt.$emit('root:showDeviceWhiteDialogStatus', true)
              } else if (res.commandId === 135) {
                this.$notify.error(res.message)
              }
            } catch (error) {
              const loginTypeString = label + ' login'
              console.log(loginTypeString, error)
            }
          } else if (
            res.type === this.$xinConfig.LOGIN_SERVICE.TYPE.NEW_LOGIN.ID &&
            res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.NEW_LOGIN.COMMAND.ADD_CHAR
          ) {
            await this.roleLogin(res.username)
            const avatar = this.stickerList[this.stickerSelected]
            const uploadImgRes = await this.uploadImgRes(role.username, avatar)
            if (uploadImgRes === 1) {
              this.$store.commit('role/INSERT_LIST', {
                username: res.username,
                level: res.level,
                online: res.online ? 1 : 0,
                photo: res.photo
              })
            }
            const userData = { userName: res.username, getCache: false }
            const thumbUrl = await this.$store.dispatch('role/getThumbUrl', userData)
            this.$store.commit('role/SET_THUMBURL', thumbUrl)
          } else {
            this.$notify.error(res.message)
          }
        }

        if (message !== '') {
          const title = this.$t('reminder')
          this.showNotyDialog(title, message)
        }
      },
      async roleLogin(userName) {
        const title = this.$t('reminder')
        const promote = this.$store.getters['social/promote']

        this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
          serviceId: this.$xinConfig.LOGIN_SERVICE.ID,
          enable: false,
          connected: false
        })

        this.$wsClient.send(this.$wsPacketFactory.selectCharacter({ username: userName, promote }))
        let message = ''
        // 避免剛登入時連續跳出 noty info 的設計，先透過該值關閉通知
        this.$store.commit('mail/SET_FIRST_RECIVE', true)
        try {
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.LOGIN.ID)
          })
          // 因流程可能會未登入成功(角色在線、裝置驗證、裝置驗證失敗)
          if (
            res.type === 0 &&
            res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.PLAYER_INFO.ID
          ) {
            //成功登入時將倒數計時暫停
            this.$store.commit('role/SET_LOGIN_TIMMER_STATE', false)
            // 先發送前三個服務的加入請求
            const initialServices = [
              this.$xinConfig.LOTTERY_SERVICE.ID, // 進入摸彩服務
              this.$xinConfig.GAME_SERVICE.ID, // 進入遊戲服務
              this.$xinConfig.SOCIAL_SERVICE.ID // 進入社群服務
            ]

            initialServices.forEach((serviceId) => {
              this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(serviceId))
              this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
                serviceId,
                enable: true,
                connected: true
              })
            })

            // 等待 SOCIAL_SERVICE 確認
            await this.$xinUtility
              .waitEvent(
                this.$wsClient.receivedListeners,
                (data) =>
                  data.protocolId === this.$xinConfig.PROTOCOL_ID.SERVICE &&
                  data.serviceId === this.$xinConfig.SOCIAL_SERVICE.ID
              )
              .then(() => {
                this.$wsClient.send(this.$wsPacketFactory.initMail())
                // 延遲五秒後開啟新信件通知
                setTimeout(() => {
                  this.$store.commit('mail/SET_FIRST_RECIVE', false)
                }, 5000)
              })
              .catch((err) => {
                console.log('SOCIAL_SERVICE ERROR:', err)
              })
            // 初始化角色資料
            await this.$store.dispatch('role/profileInit', res)
            // 再加入公會服務
            this.$wsClient.send(
              this.$wsPacketFactory.getServiceJoin(this.$xinConfig.GUILD_SERVICE.ID)
            )
            this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
              serviceId: this.$xinConfig.GUILD_SERVICE.ID,
              enable: true,
              connected: true
            })

            //取得與伺服器時間差
            await this.$store.dispatch('xinProtocol/getServerLocalTimeDiff')

            setTimeout(async () => {
              this.$nuxt.$emit('root:showRoleDialogStatus', false)
              this.$nuxt.$emit('root:showLoginDialogStatus', {
                show: false,
                onCancelNotify: () => {}
              })
              this.$nuxt.$emit('root:showPhoneNumBindingDialogStatus', false)
              this.$nuxt.$emit('root:showCreateRoleDialogStatus', false)
              // 站台差異註解
              if (this.$UIConfig.lock.getSlotIdentity) {
                const getSlotIdentity = await this.$waninNorthApi.user.getSlotIdentity({
                  PhoneNumber: this.phoneNumber
                })
                if (getSlotIdentity.status !== 200) {
                  this.$nuxt.$emit('root:showIdentityVerifiDialogStatus', true)
                }
              }
            }, 500)
            this.$nuxt.$emit('root:showWelcomeStatus', true)
            if (
              Object.prototype.hasOwnProperty.call(res, 'lastLogoutTime') &&
              res.lastLogoutTime === '1900/01/01 00:00:00'
            ) {
              this.createRoleAnalytics()
            }
          } else if (res.commandId === 136) {
            this.$notify.info(res.message)
            this.$nuxt.$emit('root:showDeviceWhiteDialogStatus', true)
          } else if (res.commandId === 135) {
            message = res.message
            this.showNotyDialog(title, message)
          }
        } catch (error) {
          console.log('role Page', error)
        }
      },
      async getImgArrayBuffer(src) {
        const response = await fetch(src)
        return await response.arrayBuffer()
      },
      resetRoleForm() {
        this.inputRoleName = ''
      },
      resetStickerSelected() {
        this.stickerSelected = 0
      },
      closeDialog() {
        this.createRoleConfirmDeleteDialogStatus = true
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      goStickersTop() {
        this.$nextTick(() => {
          const stickerList = document.getElementById('sticker-box')
          if (stickerList) stickerList.scrollTop = 0
        })
      },
      startLoginTimer() {
        this.$store.commit('role/SET_LOGIN_LAST_TIME', 300)
        this.$store.dispatch('role/startLoginTimer')
      },
      logout() {
        const reqData = this.$wsPacketFactory.logout()
        this.$wsClient.send(reqData)
        this.$wsClient.disconnect()
        this.$nuxt.$emit('root:showCreateRoleDialogStatus', false)
        this.$nuxt.$emit('root:showLoginDialogStatus', {
          show: true,
          onCancelNotify:
            this.showGameModeStatus && this.$route.params.mode === 'play'
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
        })
        this.$store.dispatch('clear')
        this.$cookies.remove('xinToken', { path: '/' })
      }
    }
  }
</script>

<style lang="scss" scoped>
  #sticker-container {
    width: 100%;
    #sticker-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, 56px);
      grid-gap: 16px;
      justify-content: center;
    }
  }
  li {
    text-indent: -20px;
    margin-left: 20px;
  }
  #create-role-card {
    &.scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    &.scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable-sm {
        max-height: calc(90svh - 52px);
      }
      &.scrollable-xs {
        max-height: calc(100svh - 52px);
      }
    }
  }
</style>
