const apiDomain = require(`~/station/${process.env.STATION}/apiDomain.js`).default

export default class Qpp {
  constructor(axios) {
    this.axios = axios
  }
  async getNoCode() {
    return await this.axios
      .get(apiDomain().qpp.getno, { timeout: 30000 })
      .then((response) => response.data)
      .catch((err) => {
        throw err
      })
  }

  async getIoCode(token) {
    var result = undefined
    await this.axios
      .post(apiDomain().qpp.getio, token)
      .then((response) => {
        if (response.status === 200 && response.data != '') {
          result = response.data
        }
      })
      .catch((err) => {
        return err
      })
    return result
  }
}
