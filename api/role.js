export default class role {
  constructor(axios) {
    this.axios = axios
  }

  async load(type, nickMD5, GameName) {
    const url = '/getimgV2.ashx' // 替換成您的請求 URL
    const headers = { 'Content-Type': 'application/x-www-form-urlencoded' } // 替換成您的請求標頭
    const reqData = {
      type,
      nickMD5,
      GameName
    }
    const params = new URLSearchParams()
    params.append('json', JSON.stringify(reqData))

    let response = await this.axios.post(url, params, { headers })
    if (response.status !== 200) {
      response.data = undefined
    }
    return response.data
  }

  // 上傳角色大頭貼圖片
  async upload(nickMD5, date, aes, crc, image) {
    const headers = { 'Content-Type': 'multipart/form-data' }

    const params = new FormData()
    const reqData = {
      type: 'UpLoadHeadIMG',
      aes: aes,
      nickMD5: nickMD5,
      crc: crc,
      GameName: 'TW',
      data: date
    }
    params.append('json', JSON.stringify(reqData))
    params.append('fileUpload', image)
    const response = await this.axios.post('/getimgV2.ashx', params, { headers })
    if (response.status !== 200) {
      response.data = undefined
    }
    return response.data
  }

  // 個人檔案的功能因
  async getProfile(nickname) {
    const headers = { 'Content-Type': 'application/json; charset=utf-8' }
    const reqData = {
      暱稱: nickname
    }

    var result = undefined

    let response = await this.axios.post('/Personal/Query', reqData, headers)

    if (response.status == 200) {
      try {
        if (response.data.status == 1) {
          result = {
            nickname,
            profile: JSON.parse(response.data.result.profile)
          }
          console.log(response.data.result)
        }
      } catch (err) {
        result = undefined
      }
    }

    /**
     * 回傳的資料格式
     * {
     *   "status": "1",
     *   "message": null,
     *   "errorCode": null,
     *   "result": {
     *     "暱稱": "AF08",
     *     "profile": "{\"Status\":7,\"Thinking\":[{\"Type\":1,\"Content\":2},{\"Type\":1,\"Content\":3}],\"Hobby\":[2,5,4,8,9,10,6,7,15,19,18,14,13,12,16,20,21,17,22,23],\"Favorite\":[41,42,38,39,43,47,46,45],\"Introduction\":[{\"Title\":0,\"Prefix\":7,\"Decription\":20231201,\"IsShow\":true},{\"Title\":1,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":2,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":3,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":4,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":5,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":6,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":7,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":8,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":9,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":10,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":11,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false},{\"Title\":12,\"Prefix\":-1,\"Decription\":-1,\"IsShow\":false}]}"
     *   }
     * }
     */

    return result
  }

  async updateProfile(nickname, profile) {
    const headers = { 'Content-Type': 'application/json; charset=utf-8' }
    const reqData = {
      暱稱: nickname,
      profile
    }

    var result = {
      nickname,
      isSuccess: false
    }

    let response = await this.axios.post('/Personal/Save', reqData, headers)

    if (response.status == 200) {
      if (response.data.status == 1) {
        result.isSuccess = true
      }
    }

    return result
  }

  async loadPictures(nickname) {
    const headers = { 'Content-Type': 'application/json; charset=utf-8' }
    const reqData = {
      暱稱: nickname
    }

    var result = undefined

    let response = await this.axios.post('/Personal/ImageQueryAll', reqData, headers)

    if (response.status == 200) {
      try {
        if (response.data.status == 1) {
          result = {
            nickname,
            pictures: JSON.parse(response.data.result.datas)
          }
        }
      } catch (err) {
        result = undefined
      }
    }

    // {
    //   "status": "1",
    //   "message": null,
    //   "errorCode": null,
    //   "result": {
    //     "datas": [
    //       {
    //         "imageID": 1884,
    //         "暱稱": "TEST5",
    //         "路徑": "xinstars_dev/personalAlbum",
    //         "檔名": "844f49b641ff171959ff201b810af5c2.jpg",
    //         "排序": 1,
    //         "日期": "2023-05-17T10:42:00",
    //         "imageUrl": "https://storage.xin-stars.com/xinstars_dev/personalAlbum/844f49b641ff171959ff201b810af5c2.jpg"
    //       },
    //       {
    //         "imageID": 1883,
    //         "暱稱": "TEST5",
    //         "路徑": "xinstars_dev/personalAlbum",
    //         "檔名": "753b24c5dae271165afe52760facdf51.png",
    //         "排序": 2,
    //         "日期": "2023-05-17T10:42:00",
    //         "imageUrl": "https://storage.xin-stars.com/xinstars_dev/personalAlbum/753b24c5dae271165afe52760facdf51.png"
    //       }
    //     ]
    //   }
    // }

    return result
  }

  async uploadPicture(nickname, image) {
    const headers = { 'Content-Type': 'multipart/form-data' }

    const params = new FormData()
    const reqData = {
      暱稱: nickname
    }
    params.append('json', JSON.stringify(reqData))
    params.append('', image)

    var result = {
      nickname,
      isSuccess: false,
      message: ''
    }

    let response = await this.axios.post('/Personal/UploadImage', reqData, headers)

    if (response.status == 200) {
      if (response.data.status == 1) {
        result.isSuccess = true
      }
    }

    // {
    //   "status": "1",
    //   "message": null,
    //   "errorCode": null,
    //   "result": {
    //     "imageID": 1885,
    //     "暱稱": "TEST5",
    //     "路徑": "xinstars_dev/personalAlbum",
    //     "檔名": "30234f3c276135b0b17af67f28361d97.png",
    //     "排序": 1,
    //     "日期": "2023-05-17T11:27:36.717",
    //     "imageUrl": "https://storage.xin-stars.com/xinstars_dev/personalAlbum/30234f3c276135b0b17af67f28361d97.png"
    //   }
    // }

    return result
  }

  async deletePicture(id) {
    const headers = { 'Content-Type': 'application/json; charset=utf-8' }
    const reqData = {
      id
    }

    var result = {
      isSuccess: false
    }

    let response = await this.axios.post('/Personal/ImageDelete', reqData, headers)

    if (response.status == 200) {
      try {
        if (response.data.status == 1) {
          result.isSuccess = true
        }
      } catch (err) {
        console.error(err)
      }
    }

    // {
    //   "status": "1",   // 刪除成功
    //   "message": null,
    //   "errorCode": null,
    //   "result": null
    // }

    return result
  }

  // [{"imageID":25, "排序":2},{"imageID":26, "排序":3},{"imageID":27, "排序":1}]
  async updatePicturesOrder(orderlyPictureIds) {
    const headers = { 'Content-Type': 'application/json; charset=utf-8' }

    // 調整格式
    let list = orderlyPictureIds.map((id, index) => {
      return {
        imageID: id,
        排序: index + 1
      }
    })

    var result = {
      isSuccess: false
    }

    let response = await this.axios.post('/Personal/ImageUpdate', list, headers)

    if (response.status == 200) {
      try {
        if (response.data.status == 1) {
          result.isSuccess = true
        }
      } catch (err) {
        console.error(err)
      }
    }

    // {
    //   "status": "1",   // 更新成功
    //   "message": null,
    //   "errorCode": null,
    //   "result": null
    // }

    return result
  }

  async uploadFile({ path, clientId, token, file }) {
    const headers = { 'Content-Type': 'multipart/form-data' }

    const params = new FormData()

    params.append('type', 'FILE')
    params.append('fileName', file.name)
    params.append('filePath', path)
    params.append('totalNumber', clientId)
    params.append('randomNumber', token)
    params.append('files', file)

    const response = await this.axios.post('/Personal/SaveUserFile', params, { headers })
    if (response.status !== 200) {
      response.data = undefined
    }
    return response.data
    // 成功回傳
    // {
    //   "status": "1",
    //   "message": null,
    //   "errorCode": null,
    //   "result": null
    // }

    // 失敗回傳
    // {
    //   "status": "0",
    //   "message": "角色驗證失敗!!",
    //   "errorCode": null,
    //   "result": null
    // }
  }

  async deleteFile({ path, clientId, token, fileName }) {
    const headers = { 'Content-Type': 'application/json; charset=utf-8' }

    const params = {
      fileName,
      totalNumber: clientId,
      randomNumber: token,
      filePath: path
    }

    const response = await this.axios.post('/Personal/DeleteUserFile', params, { headers })
    if (response.status !== 200) {
      response.data = undefined
    }
    return response.data
    // 成功回傳
    // {
    //   "status": "1",
    //   "message": null,
    //   "errorCode": null,
    //   "result": null
    // }

    // 失敗回傳
    // {
    //   "status": "0",
    //   "message": "角色驗證失敗!!",
    //   "errorCode": null,
    //   "result": null
    // }
  }

  async download({ userPath, path, file }) {
    let imagePath = `/userfile/${userPath}/${path}/${file}?t=${Date.now()}`

    const response = await this.axios.get(imagePath)
    if (response.status !== 200) {
      response.data = undefined
    }
    return response.data
  }
}
