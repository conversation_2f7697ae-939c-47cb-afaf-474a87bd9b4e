export default class Gash {
  constructor(axios, providerId) {
    this.axios = axios
    this.providerId = providerId
    this.gameId = '1'
    this.platform = 'WebHD'
  }

  async info() {
    var data = {
      Set_CPID: this.providerId,
      Set_GameID: this.gameId
    }

    var result = await this.axios
      .post('/Info', JSON.stringify(data))
      .then((response) => response.data)

    return result
  }

  /**
   * 確認Gash是否可以使用
   * @returns {boolean}
   */
  async isEnable() {
    return this.axios
      .get('/GashEnable')
      .then((response) => response.data === 1)
      .catch(() => false)
  }

  /**
   * 註冊卡號
   * @param {*} username 玩家暱稱
   * @param {*} pin 點卡Pin碼
   * @returns 回傳註冊訊息
   */
  async registerPin(username, pin) {
    var data = {
      Set_CPID: this.providerId,
      Set_GameID: this.gameId,
      Set_Pin: pin,
      Set_UData1: username,
      Set_UData2: this.platform
    }

    var result = await this.axios.post('/UserPin', data).then((response) => response.data)

    // response
    //   {
    //     "Set_CPID": "C001520000259",
    //     "Set_GameID": "1",
    //     "Set_Pin": "PGXUB292VMJZQZPEBS9Y1",
    //     "Set_UData1": "AF08",
    //     "Set_UData2": "WebHD",
    //     "Get_Res": {
    //         "Item": "PIN001",
    //         "Name": "GASH卡",
    //         "Currency": "TWD",
    //         "Price": 5,
    //         "Open_PA": null,
    //         "About": null,
    //         "Count": 1
    //     },
    //     "Get_Exchange": 1,
    //     "Get_CPPoint": 5,
    //     "Get_TID": 770,
    //     "Get_OTP": "133259189618118192-Ba5SuGmNHvdc",
    //     "ClinetIP": "***************",
    //     "RetID": 1,
    //     "RetStr": "作業完成",
    //     "Millisecond": 62
    // }

    return result
  }

  async deposit(tid, otp) {
    var data = {
      Set_CPID: this.providerId,
      Set_OTP: otp,
      Set_TID: tid
    }

    var result = await this.axios.post('/CpPin2', data)
    var [status, message] = result.data.split('\t')
    result.data = {
      status,
      message
    }

    // 原response 為字串，須使用'\t'來拆出字串
    // response

    // { // 儲值成功
    //   status: '1',
    //   message: '作業完成'
    // }

    //
    // { // 錯誤1
    //   status: '0',
    //   message: '動作重覆_x_D'
    // }

    // { // 錯誤2
    //   status: '8006',
    //   message: '無此授權'
    // }

    return result
  }
}
