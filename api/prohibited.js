export default class Prohibited {
  constructor(axios) {
    this.axios = axios
  }

  async getKeywords() {
    const headers = { 'Content-Type': 'multipart/form-data' }
    const response = await this.axios.post('/Personal/QueryMaliciousKeywords', {}, { headers })
    if (response.status !== 200) return undefined
    return response.data
  }

  async checkSemantic(nickname, message, clientId, token) {
    const headers = { 'Content-Type': 'application/json; charset=utf-8' }
    const params = new FormData()
    const reqData = {
      totalNumber: clientId,
      randomNumber: token,
      nickName: nickname,
      message: message
    }
    params.append('json', JSON.stringify(reqData))
    let response = await this.axios.post('/Personal/CheckPlayerSemantic', reqData, headers)
    if (response.status !== 200) return undefined
    return response.data
  }
}
