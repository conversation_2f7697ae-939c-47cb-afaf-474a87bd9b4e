<template>
  <v-container class="px-0 pb-0 pt-6 characterInfo">
    <!-- character -->
    <v-row no-gutters>
      <!-- character-avatar -->
      <v-col md="4" sm="4" cols="12" :xl="isInfoPage ? '3' : '4'" :lg="isInfoPage ? '3' : '4'">
        <v-row no-gutters justify="center" align="center" class="fill-height">
          <div>
            <v-row
              no-gutters
              justify="center"
              align="center"
              class="pb-0"
              @click="openUploadMethodDialog()"
            >
              <div class="img-content" v-if="isFriend || isBlock">
                <!-- 黑名單 -->
                <v-badge bordered bottom color="black" offset-x="53" offset-y="53" v-if="isBlock">
                  <character-avatar
                    :class="{ 'character-avatar': !facebookId && isInfoPage }"
                    no-upload
                    frame
                    :is-info-page="isInfoPage"
                    :is-card="isCard"
                  />
                  <template v-slot:badge>
                    <v-icon class="badge-icon-set">mdi-cancel</v-icon>
                  </template>
                </v-badge>
                <!-- 好友 -->
                <v-badge
                  bordered
                  bottom
                  color="success"
                  offset-x="53"
                  offset-y="53"
                  v-if="isFriend"
                >
                  <character-avatar
                    :class="{ 'character-avatar': !facebookId && isInfoPage }"
                    no-upload
                    frame
                    :is-info-page="isInfoPage"
                    :is-card="isCard"
                  />
                  <template v-slot:badge>
                    <v-icon class="badge-icon-set">mdi-account-multiple</v-icon>
                  </template>
                </v-badge>
              </div>
              <div class="img-content" v-else>
                <character-avatar
                  :class="{ 'character-avatar': !facebookId && isInfoPage }"
                  no-upload
                  frame
                  :is-info-page="isInfoPage"
                  :is-card="isCard"
                />
                <badge
                  right="16px"
                  bottom="16px"
                  icon="photo_camera"
                  v-if="!facebookId && isInfoPage"
                  :is-friend="isFriend"
                  :is-block="isBlock"
                  class="cursor-pointer"
                ></badge>
              </div>
            </v-row>
            <v-row no-gutters justify="center" align="center">
              <span class="custom-text-noto text-body-1 default-content--text">{{
                infoTmp.userName
              }}</span>
            </v-row>
          </div>
        </v-row>
      </v-col>
      <!-- character-info -->
      <v-col
        :xl="isInfoPage ? '9' : '8'"
        :lg="isInfoPage ? '9' : '8'"
        md="8"
        sm="8"
        cols="12"
        align-self="center"
        :class="[{ 'mt-6': $vuetify.breakpoint.xsOnly }]"
      >
        <!-- level -->
        <v-row no-gutters v-if="characterInfo.showLevel" align="center"
          ><div class="mr-2">
            <v-img
              :src="getImage('<EMAIL>')"
              :srcset="getSrcset('level')"
              width="38"
              height="38"
            />
          </div>
          <span
            class="custom-text-noto text-body-1 default-content--text"
            style="font-size: 16px !important"
            >{{ infoTmp.online ? infoTmp.level : '-' }}</span
          >
        </v-row>
        <!-- vipLevel -->
        <v-row no-gutters v-if="characterInfo.showVipLevel" class="mt-3" align="center">
          <v-row no-gutters align="center">
            <div class="mr-2">
              <vipLevelIcon width="38" height="38" :vip-level="infoTmp.vipLevel"></vipLevelIcon>
            </div>
            <span
              class="custom-text-noto text-body-1 default-content--text"
              style="font-size: 16px !important"
            >
              {{
                infoTmp.level !== 0 && infoTmp.vipLevel !== 0
                  ? $t(infoTmp.vipLevelTitle[infoTmp.vipLevel])
                  : '-'
              }}
            </span>
          </v-row>
          <v-spacer />
          <v-icon v-if="isInfoPage" @click="showvipLevelDescDialog"
            >mdi-information-outline
          </v-icon>
        </v-row>
        <!-- guild -->
        <v-row
          no-gutters
          v-if="characterInfo.showGuild"
          class="d-flex flex-nowrap mt-3"
          align="center"
        >
          <div class="mr-2 character-info-avatar-badge">
            <v-img
              :src="getImage('<EMAIL>')"
              :srcset="getSrcset('badge_preset')"
              width="38"
              height="38"
            />
            <v-img
              contain
              :src="getImage('<EMAIL>')"
              :srcset="getSrcset('badge_frame')"
              width="38"
              height="38"
              class="badge-border"
            />
          </div>
          <v-col cols="1" class="flex-grow-1 flex-shrink-0 pr-4 guild-name">
            <span
              class="custom-text-noto text-body-1 default-content--text text-wrap"
              style="font-size: 16px !important"
            >
              {{ infoTmp.guildName.length > 0 ? infoTmp.guildName : '-' }}
            </span>
          </v-col>

          <v-tooltip
            v-if="isInfoPage && !guildLock"
            open-delay="50"
            close-delay="25"
            top
            z-index="3"
            :content-class="`tooltip-content-custom ${
              $vuetify.breakpoint.smAndDown ? 'left-position' : ''
            }`"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-icon v-bind="attrs" v-on="on">mdi-information-outline </v-icon>
            </template>
            <span>{{ $t('guild_feature_coming_soon') }}</span>
          </v-tooltip>
          <v-btn
            v-if="infoTmp.guildName.length > 0 && isCard && showGuildButton"
            elevation="0"
            outlined
            color="primary"
            @click="openGuildInfo(infoTmp.userName)"
          >
            {{ $t('check_guild') }}
          </v-btn>
        </v-row>
        <!-- coin -->
        <v-row no-gutters v-if="characterInfo.showCoin" class="mt-3" align="center">
          <div class="mr-2">
            <v-img
              :src="getImage('<EMAIL>')"
              :srcset="getSrcset('coin')"
              width="38"
              height="38"
            />
          </div>
          <span
            class="custom-text-noto text-body-1 default-content--text"
            style="font-size: 16px !important"
          >
            {{ formatNumber(infoTmp.balance) }}
          </span>
        </v-row>
        <!-- honor -->
        <v-row no-gutters class="mt-3" align="center" v-if="isInfoPage && characterInfo.showHonor">
          <v-row no-gutters align="center">
            <div class="mr-2">
              <v-tooltip
                open-delay="50"
                close-delay="25"
                top
                z-index="3"
                :content-class="`tooltip-content-custom ${
                  $vuetify.breakpoint.xsOnly ? 'left-position' : ''
                }`"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-img
                    v-bind="attrs"
                    v-on="on"
                    :src="getImage('<EMAIL>')"
                    :srcset="getSrcset('Honor')"
                    width="38"
                    height="38"
                  />
                </template>
                <span>{{ $t('honor') }}</span>
              </v-tooltip>
            </div>
            <span
              class="custom-text-noto text-body-1 default-content--text"
              style="font-size: 16px !important"
            >
              {{ infoTmp.honor ? infoTmp.honor : '-' }}
            </span>
          </v-row>
          <v-spacer />
          <v-tooltip
            v-if="isInfoPage"
            open-delay="50"
            close-delay="25"
            bottom
            z-index="3"
            :content-class="`tooltip-content-custom ${
              $vuetify.breakpoint.smAndDown ? 'right-position' : ''
            }`"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-icon v-bind="attrs" v-on="on">mdi-help-circle-outline </v-icon>
            </template>
            <!-- 橫屏手機 nowrap -->
            <span :class="{ nowrap: !isStanding && isMobile }">{{
              $t('personal_info_honor_tooltip')
            }}</span>
          </v-tooltip>
        </v-row>
        <!-- active -->
        <v-row no-gutters class="mt-3" align="center" v-if="isInfoPage && characterInfo.showActive">
          <v-row no-gutters align="center">
            <div class="mr-2">
              <v-tooltip
                open-delay="50"
                close-delay="25"
                top
                z-index="3"
                :content-class="`tooltip-content-custom ${
                  $vuetify.breakpoint.xsOnly ? 'left-position' : ''
                }`"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-img
                    v-bind="attrs"
                    v-on="on"
                    :src="getImage('<EMAIL>')"
                    :srcset="getSrcset('active')"
                    width="38"
                    height="38"
                  />
                </template>
                <span>{{ $t('active') }}</span>
              </v-tooltip>
            </div>
            <span
              class="custom-text-noto text-body-1 default-content--text"
              style="font-size: 16px !important"
            >
              {{ infoTmp.activeValue }}
            </span>
          </v-row>
          <v-spacer />
          <v-tooltip
            v-if="isInfoPage"
            open-delay="50"
            close-delay="25"
            bottom
            z-index="3"
            :content-class="`tooltip-content-custom ${
              $vuetify.breakpoint.smAndDown ? 'right-position' : ''
            }`"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-icon v-bind="attrs" v-on="on">mdi-help-circle-outline </v-icon>
            </template>
            <!-- 橫屏手機 nowrap -->
            <span :class="{ nowrap: !isStanding && isMobile }">{{
              $t('personal_info_active_tooltip')
            }}</span>
          </v-tooltip>
        </v-row>
      </v-col>
    </v-row>
    <vipLevelDescDialog
      v-if="showvipLevelDescDialogStatus"
      :show-member-level-desc-dialog-status.sync="showvipLevelDescDialogStatus"
      :vip-level="infoTmp.vipLevel"
    />
  </v-container>
</template>

<script>
  import images from '~/mixins/images'
  import guildMgr from '~/mixins/guildMgr'
  import scssLoader from '@/mixins/scssLoader.js'

  export default {
    name: 'CharacterInfo',
    mixins: [images, scssLoader, guildMgr],
    components: {
      characterAvatar: () => import('~/components/avatar'),
      vipLevelDescDialog: () => import('~/components/player_info/vipLevelDescDialog.vue'),
      vipLevelIcon: () => import('~/components/player_info/vipLevelIcon'),
      badge: () => import(`~/components/badge`)
    },
    props: {
      info: {
        type: Object,
        default: {}
      },
      isCard: {
        type: Boolean,
        default: false
      },
      isInfoPage: {
        type: Boolean,
        default: false
      },
      isFriend: {
        type: Boolean,
        default: false
      },
      isBlock: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        infoTmp: this.info,
        showvipLevelDescDialogStatus: false
      }
    },
    watch: {
      info: {
        handler() {
          this.infoTmp = this.info
        }
      }
    },
    computed: {
      characterInfo({ $UIConfig }) {
        return $UIConfig.characterInfo
      },
      orientation({ $store }) {
        return $store.getters['deviceManagement/getOrientation']
      },
      facebookId() {
        return this.$store.getters['role/facebookId']
      },
      gameIsPlaying({ $store }) {
        const gameLink = $store.getters['gameHall/gameLink']
        return gameLink === ''
      },
      //是否橫屏
      isStanding() {
        return this.orientation === 0
      },
      //是否手機
      isMobile() {
        return this.$device.isMobile
      },
      guildLock() {
        return this.$UIConfig.lock.guild
      },
      showGuildButton({ $store }) {
        const gameLink = $store.getters['gameHall/gameLink']
        return gameLink === '' && this.guildLock
      }
    },
    async mounted() {
      this.setOrientation() //偵測手機轉向是否改變
      window.addEventListener('orientationchange', this.setOrientation)
    },
    beforeDestroy() {
      window.removeEventListener('orientationchange', this.setOrientation)
    },

    methods: {
      showvipLevelDescDialog() {
        this.showvipLevelDescDialogStatus = true
      },
      //千分位
      formatNumber(num) {
        if (num === undefined) return

        return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
      },
      setOrientation() {
        this.$store.dispatch('deviceManagement/setOrientation')
      },
      openUploadMethodDialog() {
        if (!this.facebookId && this.isInfoPage) {
          this.$nuxt.$emit('root:showUploadPhotoMethodDialogStatus', true)
        }
      },
      async openGuildInfo(userName) {
        const guildInfo = await this.setupGuildByUserName(userName)
        this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', false)
        if (guildInfo.guildName === this.guildName) {
          this.$router.push(this.localePath('/guild/info'))
          this.$nuxt.$emit('root:showChatRoomDialogStatus', false)
        } else this.$nuxt.$emit('root:guildDialogStatus', { show: true, info: guildInfo })
      }
    }
  }
</script>
<!-- 此處加scoped會使部分Class無作用，故在最外層包覆一層class以及將class命名複雜化避免汙染 -->
<style lang="scss">
  $dialog-fill: map-get($colors, 'dialog-fill');
  $default-content-color: map-get($colors, default-content);
  .character-info-avatar-badge {
    position: relative;
    .badge-border {
      position: absolute;
      top: 0;
    }
  }
  .characterInfo {
    .character-avatar {
      cursor: pointer;
    }
    .img-content {
      position: relative;
    }
    .badge-icon-set {
      font-size: 12px !important;
      padding-bottom: 2px;
      padding-left: 0px;
      cursor: pointer;
    }
    .theme--dark.v-badge .v-badge__badge::after {
      border-color: $dialog-fill !important;
    }
    .guild-name {
      min-width: 100px;
      max-width: 100%;
    }
    .v-icon {
      color: $default-content-color;
    }
  }
</style>
