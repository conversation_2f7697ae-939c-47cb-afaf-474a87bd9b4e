<template>
  <v-dialog
    v-model="showMusicPlayerDialogStatusTemp"
    persistent
    max-width="460"
    content-class="rounded-lg"
  >
    <v-card class="dialog-fill">
      <customDialogTitle :title="$t('background_music').toUpperCase()" @closeDialog="closeDialog" />
      <v-card-text class="pa-4 pa-sm-6">
        <v-container fluid class="pa-0">
          <v-row no-gutters>
            <v-select
              v-model="selectedMusic"
              filled
              shaped
              dense
              height="40px"
              item-text="title"
              :items="musicList"
              :label="$t('background_music')"
              item-color="primary"
              item-value="value"
            />
          </v-row>
          <v-row no-gutters>
            <v-col cols="12">
              <span class="default-content--text text-subtitle-2 custom-text-noto">{{
                $t('lobby_volume')
              }}</span>
            </v-col>
            <v-col cols="12" class="mt-4">
              <v-slider
                hide-details
                dense
                :max="volMax"
                :min="volMin"
                color="primary"
                track-color="track-color"
                v-model="volume"
                id="music-slider"
                class="music-slider-container"
              >
                <template v-slot:prepend>
                  <span v-if="volume === 0" class="material-symbols-outlined"> no_sound </span>
                  <span v-else class="material-symbols-outlined" @click="volume = 0">
                    volume_up
                  </span>
                </template>
                <template v-slot:append>
                  <span class="body-2 default-content--text volume-percentage">{{ volume }}%</span>
                </template>
              </v-slider>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
  import throttle from 'lodash/throttle'
  import debounce from 'lodash/debounce'

  export default {
    name: 'musicPlayerDialog',
    mixins: [require('~/mixins/music').default],
    props: {
      showMusicPlayerDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    data() {
      return {
        showMusicPlayerDialogStatusTemp: false,
        selectedMusic: -1,
        volMax: 100,
        volMin: 0
      }
    },
    created() {
      // 創建localStorage的防抖
      this.debouncedSaveMusicPreference = debounce(this.saveMusicPreferenceToLocalStorage, 300)
      // 創建音量節流
      this.throttledSetVolume = throttle((val) => {
        this.$store.commit('music/SET_VOLUME', val)
      }, 50)
    },
    beforeDestroy() {
      // 取消防抖
      if (this.debouncedSaveMusicPreference && this.debouncedSaveMusicPreference.cancel) {
        this.debouncedSaveMusicPreference.cancel()
      }
      // 取消節流
      if (this.throttledSetVolume?.cancel) {
        this.throttledSetVolume.cancel()
      }
    },
    computed: {
      isPlaying({ $store }) {
        return $store.getters['music/isPlaying']
      },
      tracks({ $store }) {
        return $store.getters['music/tracks']
      },
      currentTrack({ $store }) {
        return $store.getters['music/currentTrack']
      },
      musicList() {
        let locale = this.$i18n.locale
        let musicMap = [
          { title: this.$t('play_in_order'), value: -1 },
          ...this.tracks.map((track, index) => ({
            title: track.name[locale],
            src: track.src,
            value: index
          }))
        ]
        return musicMap
      },
      volume: {
        get() {
          return this.$store.getters['music/volume']
        },
        set(val) {
          // 使用節流更新音量
          this.throttledSetVolume(val)
          this.debouncedSaveMusicPreference()
        }
      },
      isPlayByOrder({ $store }) {
        return $store.getters['music/isPlayByOrder']
      }
    },
    watch: {
      showMusicPlayerDialogStatus: {
        immediate: true,
        handler(val) {
          this.showMusicPlayerDialogStatusTemp = val
        }
      },
      selectedMusic: {
        handler(val) {
          if (val === -1) {
            this.$store.commit('music/SET_IS_PLAY_BY_ORDER', true)
          } else {
            this.$store.commit('music/SET_IS_PLAY_BY_ORDER', false)
            this.$store.commit('music/SET_CURRENT_TRACK', val)
          }
          this.saveMusicPreferenceToLocalStorage()
        }
      }
    },
    mounted() {
      if (this.$store.getters['music/isPlayByOrder']) {
        this.selectedMusic = -1
      } else {
        this.selectedMusic = this.currentTrack
      }
    },
    methods: {
      closeDialog() {
        this.$nuxt.$emit('root:showMusicPlayerDialogStatus', false)
      }
    }
  }
</script>
<style scoped lang="scss">
  .track-color {
    color: rgba(var(--v-primary-variant-3), 0.4) !important;
  }
  .music-slider-container {
    align-items: center;
    :deep(.v-slider) {
      margin-right: 0 !important;
    }
  }
  .volume-percentage {
    width: 38px;
    text-align: right;
  }
</style>
