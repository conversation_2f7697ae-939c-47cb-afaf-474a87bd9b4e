<template>
  <v-col cols="12" class="swiper-container">
    <div class="swiper-item mr-lg-0 mr-md-n6 mr-sm-n4 mr-n4" :class="swiperItemPaddingB">
      <swiper
        class="swiper"
        :options="swiperOption"
        ref="swiper"
        @transitionEnd="swiperIndexChangeEvent()"
      >
        <swiper-slide
          v-for="(game, index) in gameList"
          :key="`gamecard_${game.id}_${index}`"
          :class="{ 'mr-lg-0 mr-md-6 mr-sm-4 mr-4': index === gameList.length - 1 }"
          :style="swiperSlideStyle"
        >
          <slot name="card" :game="game"></slot>
        </swiper-slide>
      </swiper>
    </div>
    <v-btn
      v-show="btnStatus.pre && showSlideBtn"
      fab
      min-width="0"
      small
      class="swiper-custom-button-prev button-content--text"
      :style="{ left: btnPosition.left, top: btnPosition.top }"
      color="gradient-button"
      @click="slideButton('prev')"
    >
      <span class="material-symbols-outlined"> navigate_before </span>
    </v-btn>
    <v-btn
      v-show="btnStatus.next && showSlideBtn"
      fab
      min-width="0"
      small
      class="swiper-custom-button-next button-content--text"
      :style="{ right: btnPosition.right, top: btnPosition.top }"
      color="gradient-button"
      @click="slideButton('next')"
    >
      <span class="material-symbols-outlined"> navigate_next </span>
    </v-btn>
  </v-col>
</template>
<script>
  // 請參考以下網址 https://v1.github.surmon.me/vue-awesome-swiper/
  //Q:Swiper.js使用遇到的问题总结onSlideChangeEnd回调偶尔触发，偶尔不触发等
  //A:https://blog.csdn.net/xm1037782843/article/details/87981190
  import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
  import analytics from '@/mixins/analytics.js'

  export default {
    name: 'SwiperCustom',
    mixins: [analytics],
    components: {
      Swiper,
      SwiperSlide
    },
    props: {
      // 遊戲清單
      gameList: {
        type: Array,
        default: () => []
      },
      // 輪播卡片的寬度(RWD)
      swiperSlideStyle: {
        type: Object,
        default: () => {
          return {
            boxSizing: 'border-box',
            width: 'auto'
          }
        }
      },
      // 是否顯示左右按鈕
      showSlideBtn: {
        type: Boolean,
        required: true
      },
      // 左右按鈕的位置
      btnPosition: {
        type: Object,
        default: () => {
          return {
            left: '-15px',
            right: '-15px',
            top: '40%'
          }
        }
      },
      // swiper-item 的 padding-bottom 設定
      swiperItemPaddingB: {
        type: String,
        default: 'pb-0'
      },
      // 是否禁用觸控
      disableTouch: {
        type: Boolean,
        default: false
      },
      // 是否有自定義swiper參數
      customSwiperOption: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        // 左右按鈕顯示狀態
        btnStatus: {
          pre: false,
          next: true
        }
      }
    },
    computed: {
      swiperOption() {
        return {
          allowTouchMove: !this.disableTouch,
          slidesPerView: 'auto',
          spaceBetween: 16,
          fade: {
            crossFade: true
          },
          observer: true,
          observeParents: true,
          // 合併自定義SwiperOption
          ...this.customSwiperOption
        }
      }
    },
    methods: {
      // 左右切換輪播卡片位置
      slideButton(leftAndRight) {
        if (this.$refs['swiper']) {
          if (leftAndRight === 'prev') this.$refs['swiper'].$swiper.slidePrev()
          if (leftAndRight === 'next') this.$refs['swiper'].$swiper.slideNext()
          this.swiperIndexChangeEvent()
        }
      },
      // 取得輪播卡片位置，判定左右按鈕是否該顯示
      // this.$refs['swiper'].$swiper.progress: 數值範圍為 0 ~ 1
      async swiperIndexChangeEvent() {
        const preActive = (value) => value > 0 && value <= 1
        const nextActive = (value) => value >= 0 && value < 1
        if (this.$refs['swiper']) {
          this.btnStatus.pre = preActive(this.$refs['swiper'].$swiper.progress)
          this.btnStatus.next = nextActive(this.$refs['swiper'].$swiper.progress)
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .swiper-container {
    position: relative;
    overflow: visible;

    .swiper-item {
      overflow: hidden;
    }

    .swiper-custom-button-prev {
      position: absolute;
      transform: translateY(-50%);
      z-index: 10;
    }
    .swiper-custom-button-next {
      position: absolute;
      transform: translateY(-50%);
      z-index: 10;
    }
  }
</style>
