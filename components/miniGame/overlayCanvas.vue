<template>
  <div v-if="!miniGameIsPlay" class="overlay-container">
    <div class="overlay-background">
      <div class="content-area">
        <img :src="endGameImage" class="character-img" alt="character" />
        <v-row no-gutters class="text-area">
          <v-col cols="12" v-for="(line, index) in messageLines" :key="index">
            <span class="main-text text-body-2">
              {{ line }}
            </span>
          </v-col>
        </v-row>
      </div>
    </div>
  </div>
</template>

<script>
  import miniGame from '@/mixins/miniGame.js'
  export default {
    name: 'OverlayTemplate',
    mixins: [miniGame],
    props: {
      width: {
        type: [String, Number],
        default: '100%'
      },
      height: {
        type: [String, Number],
        default: '100%'
      }
    },
    computed: {
      containerStyle() {
        return {
          width: typeof this.width === 'number' ? `${this.width}px` : this.width,
          height: typeof this.height === 'number' ? `${this.height}px` : this.height
        }
      },
      messageLines() {
        const message = this.$t(
          this.miniGame.hasNextGame ? 'miniGameCanvasNoty' : 'miniGameCanvasNoty1'
        )
        return message.split('\r\n')
      }
    }
  }
</script>

<style scoped>
  .overlay-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
  }

  .overlay-background {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: stretch; /* 改變為 stretch */
  }

  .content-area {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%; /* 添加寬度 100% */
    box-sizing: border-box;
  }

  .character-img {
    width: 120px;
    height: auto;
    animation: float 2s ease-in-out infinite;
  }

  .text-area {
    text-align: center;
    position: relative;
    width: 100%;
    overflow: hidden;
    background: linear-gradient(
      90deg,
      rgba(237, 193, 167, 0) 0%,
      rgba(237, 193, 167, 0.8) 50%,
      rgba(237, 193, 167, 0) 100%
    );
  }

  .main-text {
    color: var(--Grey-grey-4, #4c3535);
    display: block; /* 確保文字橫向填滿 */
    width: 100%;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }
</style>
