<template>
  <div class="drawing-canvas-container">
    <div class="content-wrapper">
      <v-img :src="backgroundImage" :width="width" :height="width" class="background-image" contain>
        <canvas
          ref="canvas"
          :width="width"
          :height="height"
          class="canvas rounded-lg overlay-canvas"
        ></canvas>
      </v-img>
    </div>
  </div>
</template>

<script>
  import miniGame from '@/mixins/miniGame.js'
  export default {
    name: 'DrawingCanvas',
    props: {
      width: {
        type: Number,
        default: 255
      },
      height: {
        type: Number,
        default: 255
      },
      maxCoordinate: {
        type: Number,
        default: 255
      }
    },
    mixins: [miniGame],
    computed: {},

    mounted() {
      this.initCanvas()
    },

    methods: {
      mapCoordinate(value, dimension) {
        return (value / this.maxCoordinate) * dimension
      },
      initCanvas() {
        const canvas = this.$refs.canvas
        const ctx = canvas.getContext('2d')
        this.clearAndDraw(ctx)
      },

      drawPoints(ctx) {
        this.points.forEach((point) => {
          const { x, y, color } = point
          const mappedX = this.mapCoordinate(x, this.width)
          const mappedY = this.mapCoordinate(y, this.height)

          ctx.fillStyle = `rgba(${color.r}, ${color.g}, ${color.b}, 0.8)`
          ctx.beginPath()
          ctx.arc(mappedX, mappedY, 0.2, 0, Math.PI * 2)
          ctx.fill()
        })
      },

      clearAndDraw(ctx) {
        const canvas = this.$refs.canvas
        ctx.save()
        ctx.resetTransform()
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        ctx.restore()
        ctx.setTransform(1, 0, 0, -1, 0, this.height)
        this.drawPoints(ctx)
      },

      redraw() {
        const canvas = this.$refs.canvas
        const ctx = canvas.getContext('2d')
        this.clearAndDraw(ctx)
      }
    },

    watch: {
      points: {
        handler() {
          this.$nextTick(() => {
            this.redraw()
          })
        },
        deep: true
      }
    }
  }
</script>

<style scoped>
  .drawing-canvas-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  .content-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  .content-wrapper.has-text {
    gap: 10px; /* 圖片和文字之間的間距 */
  }

  .background-image {
    position: relative;
    transition: all 0.3s ease;
  }

  .text-overlay {
    position: relative; /* 改為相對定位 */
    text-align: center;
    margin-top: 10px;
  }

  .loading-text {
    font-size: 1.2em;
    color: #000;
    white-space: nowrap;
  }

  .overlay-canvas {
    position: absolute;
    top: 0;
    left: 0;
    background-color: transparent;
    z-index: 1;
  }

  .canvas {
    background-color: rgba(255, 255, 255, 0.8);
  }
</style>
