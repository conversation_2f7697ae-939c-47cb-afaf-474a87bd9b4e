<template>
  <div>
    <v-dialog
      v-model="confirmGoAppDialogStatus"
      persistent
      max-width="400px"
      content-class="rounded-lg"
    >
      <v-card elevation="0" tile color="dialog-fill" class="pa-4 pa-sm-6">
        <v-card-title
          class="d-flex justify-center align-center text-h6 font-weight-regular custom-text-noto grey-1--text pa-0"
        >
          {{ $t('hint') }}
        </v-card-title>
        <v-card-text class="default-content--text text-body-2 custom-text-noto px-0 py-6">
          <span>{{ $t('go_app_noty') }}</span>
        </v-card-text>
        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="$vuetify.breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
              ><v-btn
                :class="['text-button', breakpoint.xsOnly ? 'w-100' : '']"
                elevation="0"
                text
                @click="closeDialog"
              >
                {{ $t('cancel') }}
              </v-btn></v-col
            >
            <v-col :cols="$vuetify.breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                :class="['button-content--text text-button', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                @click="goApp"
              >
                {{ $t('sure') }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
  import analytics from '@/mixins/analytics.js'

  export default {
    name: 'confirmGoAppDialog',
    mixins: [analytics],
    props: {
      confirmGoAppDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {}
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:confirmGoAppDialogStatus', false)
      },
      goApp() {
        if (this.$device.isAndroid) {
          this.grandPrizeCompletedAnalytics('phone_android')
        } else if (this.$device.isIos || this.$device.isMacOS) {
          this.grandPrizeCompletedAnalytics('phone_ios')
        } else {
          this.grandPrizeCompletedAnalytics('phone_pc')
        }

        this.closeDialog()
        const url = 'https://www.xin-stars.com/goStore'
        this.$lineOpenWindow.open(url, '_blank')
      }
    }
  }
</script>
