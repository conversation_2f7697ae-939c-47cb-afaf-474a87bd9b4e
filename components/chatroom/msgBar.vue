<template>
  <v-row
    no-gutters
    align="center"
    :class="[
      'postion-relative',
      'flex-nowrap',
      'py-2',
      'px-4',
      'msg-bar-row',
      bgColor,
      { 'pb-10': $device.isMobile && !isKeyboardOpen },
      {
        'pb-6': $device.isMobile && !isKeyboardOpen && (orientation === 90 || orientation === 270)
      },
      { 'notch-left': hasLeftNotch && isCustomerServiceMsgbar }
    ]"
  >
    <template v-if="isCustomer">
      <v-btn
        block
        :color="msgBar.customerBtn.color"
        class="button-content--text"
        @click="goCustomer"
      >
        <v-icon
          v-if="msgBar.customerBtn.isVuetifyIcon"
          :color="msgBar.customerBtn.color"
          class="button-content--text mr-2"
        >
          {{ msgBar.customerBtn.icon }}
        </v-icon>
        <span v-else class="material-symbols-outlined button-content--text mr-2">
          {{ msgBar.customerBtn.icon }}
        </span>
        {{ $t('online_customer_service') }}
      </v-btn>
    </template>
    <template v-else>
      <v-textarea
        v-if="customerService"
        class="msg-bar-text"
        v-model="msg"
        :placeholder="placeholder"
        background-color="dialog-fill"
        auto-grow
        solo
        flat
        dense
        hide-details
        rows="1"
        row-height="15"
        maxlength="300"
        :disabled="textFieldDisabled"
        @keydown.enter="sendCustomerServiceMessageEvent"
        @input="limitInput300Length"
        @focus="focusEvent"
        @blur="blurEvent"
      >
        <template v-slot:append>
          <v-icon
            :color="msgBar.sendIconColor"
            :disabled="sendDisabled"
            class="ml-4"
            @click.stop="sendMessageEvent(false)"
            >mdi-send-variant
          </v-icon>
        </template>
      </v-textarea>
      <v-text-field
        v-else
        v-model="msg"
        :placeholder="placeholder"
        background-color="dialog-fill"
        solo
        flat
        dense
        hide-details
        maxlength="50"
        :disabled="textFieldDisabled"
        @keydown.enter.exact.prevent="sendMessageEvent(false)"
        @input="limitInputLength"
        @focus="focusEvent"
        @blur="blurEvent"
      >
        <template v-slot:append>
          <v-icon
            color="primary-variant-1"
            :disabled="sendDisabled"
            class="ml-4"
            @click.stop="sendMessageEvent(false)"
            >mdi-send-variant
          </v-icon>
        </template>
      </v-text-field>
      <v-expand-x-transition>
        <v-row
          v-if="isChatBtnsShow && showMiniGame && selectedChat === 0"
          no-gutters
          justify="space-between"
          align="center"
          class="flex-grow-0 flex-shrink-0"
        >
          <v-btn
            class="ma-1"
            outlined
            color="primary"
            :disabled="!answerBtnActive || !answerCount > 0"
            @click="sendMessageEvent(true)"
          >
            {{ $t('miniGameAnswerBtn') }}
          </v-btn>
        </v-row>
      </v-expand-x-transition>

      <v-row
        v-if="isChatBtnsShow"
        no-gutters
        justify="space-between"
        align="center"
        class="flex-grow-0 flex-shrink-0 flex-nowrap extra-btns"
        :class="{ 'is-extra-btns-fold': isExtraBtnsFold }"
        :style="{ 'max-width': `${chatBtnsWidth}px` }"
      >
        <msgSticker
          v-if="isMsgStickerShow"
          class="ml-1"
          :sticker-disabled="stickerDisabled"
          @stickerBtnWidth="sumChatBtnsWidth"
          @sendStickerEvent="sendStickerEvent"
        />
        <msgCustomSticker
          v-if="isMsgCustomStickerShow"
          class="ml-1"
          :custom-sticker-disabled="customStickerDisabled"
          @customStickerBtnWidth="sumChatBtnsWidth"
          @sendCustomStickerEvent="sendCustomStickerEvent"
        />
      </v-row>

      <v-row
        v-else
        no-gutters
        justify="space-between"
        align="center"
        class="flex-grow-0 flex-shrink-0 flex-nowrap extra-btns"
        :class="{ 'is-extra-btns-fold': isExtraBtnsFold }"
        :style="{ 'max-width': `${customerServiceBtnsWidth}px` }"
      >
        <customerServiceCustomSticker
          v-if="isCustomerServiceCustomStickerShow"
          class="ml-1"
          @customStickerBtnWidth="sumCustomerServiceBtnsWidth"
          @sendCustomerServiceCustomStickerEvent="sendCustomerServiceCustomStickerEvent"
        />
      </v-row>

      <v-row no-gutters v-if="!onlyText" justify="space-between" align="center" class="pl-4">
        <v-icon color="default-content" :disabled="micDisabled">mdi-microphone</v-icon>
      </v-row>
    </template>
  </v-row>
</template>

<script>
  import orientation from '~/mixins/orientation'
  import miniGame from '@/mixins/miniGame.js'
  import playerSemantic from '~/mixins/playerSemantic'
  export default {
    name: 'msgBar',
    mixins: [orientation, miniGame, playerSemantic],
    props: {
      bgColor: {
        type: String,
        default: 'dialog-fill-2'
      },
      targetOffline: {
        type: Boolean,
        default: false
      },
      isCustomer: {
        type: Boolean,
        default: false
      },
      customerService: {
        type: Boolean,
        default: false
      },
      onlyText: {
        type: Boolean,
        default: false
      },
      noTarget: {
        type: Boolean,
        default: false
      },
      isChatBtnsShow: {
        type: Boolean,
        default: false
      },
      isCustomerServiceMsgbar: {
        type: Boolean,
        default: false
      }
    },
    components: {
      msgSticker: () => import('~/components/chatroom/msgSticker'),
      msgCustomSticker: () => import('~/components/chatroom/msgCustomSticker'),
      customerServiceCustomSticker: () =>
        import('~/components/chatroom/customerServiceCustomSticker')
    },
    data() {
      return {
        msg: '',
        msgFocus: false,
        chatBtnsWidth: 0,
        customerServiceBtnsWidth: 0
      }
    },
    computed: {
      selectedChat({ $store }) {
        return $store.getters['chat/selectedChat']
      },
      textFieldDisabled() {
        return this.targetOffline || this.noTarget
      },
      sendDisabled() {
        return this.targetOffline || this.msg.length === 0 || this.noTarget
      },
      stickerDisabled() {
        return this.targetOffline || this.noTarget
      },
      customStickerDisabled() {
        return this.targetOffline || this.noTarget
      },
      micDisabled() {
        return this.targetOffline || this.noTarget
      },
      placeholder() {
        let text = ''
        if (this.noTarget) text = this.$t('chat_not_possible')
        else if (this.targetOffline) text = this.$t('cannot_chat_with_offline_players')
        else text = this.$t('input_message')
        return text
      },
      msgBar() {
        return this.$UIConfig.msgBar
      },
      isKeyboardOpen({ $store }) {
        return $store.getters['chat/isKeyboardOpen']
      },
      isExtraBtnsFold() {
        return this.$vuetify.breakpoint.xsOnly && this.msgFocus && this.msg.length !== 0
      },
      isMsgStickerShow() {
        return this.$UIConfig.lock.chatroom.msgSticker
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      isMsgCustomStickerShow() {
        return this.$UIConfig.lock.chatroom.msgCustomSticker
      },
      isCustomerServiceCustomStickerShow() {
        return this.$UIConfig.lock.chatroom.customerServiceCustomSticker
      }
    },
    watch: {
      textFieldDisabled(val) {
        if (val) this.clearMsg()
      }
    },

    methods: {
      goCustomer() {
        this.$emit('goCustomer')
      },
      sendCustomerServiceMessageEvent(event) {
        const userAgent = this.$device.userAgent
        const isSafari = userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Version') !== -1
        const isIphone = userAgent.indexOf('iPhone') !== -1 && userAgent.indexOf('Version') !== -1
        const isIpadPro = isSafari && !isIphone && 'ontouchend' in document // 判斷是否為ipadPro
        const handheldDevice =
          this.$device.isMobile || this.$device.isSamsung || this.$device.isTablet || isIpadPro

        if (!handheldDevice && !event.shiftKey) {
          event.preventDefault()
          this.sendMessageEvent(false)
        }
      },
      async sendMessageEvent(isAnswer) {
        const message = this.msg.trim()
        if (!message || this.msg.replace(/\n/g, '').length === 0) {
          this.$notify.warning(this.$t('message_cannot_be_empty'))
          return
        }
        if (isAnswer) {
          this.$emit('sendAnswerEvent', message)
          return
        }
        const isBlock = await this.checkMessage(this.userName, message)

        if (!isBlock) {
          this.$emit('sendMessageEvent', message)
        } else {
          this.$emit('sendForbiddenEvent', message)
        }
      },
      sendCustomerServiceCustomStickerEvent(message) {
        this.$emit('isCustomStickerEvent', true)
        this.$emit('sendMessageEvent', message)
      },
      limitInputLength() {
        if (this.msg.length > 50) {
          this.msg = this.msg.slice(0, 50)
        }
      },
      limitInput300Length() {
        if (this.msg.length > 300) {
          this.msg = this.msg.slice(0, 300)
        }
      },
      focusEvent() {
        this.msgFocus = true
        if (this.$device.isMobile) {
          this.$store.commit('chat/SET_IS_KEYBOARD_OPEN', true)
        }
      },
      blurEvent() {
        this.msgFocus = false
        if (this.$device.isMobile) {
          this.$store.commit('chat/SET_IS_KEYBOARD_OPEN', false)
        }
      },
      clearMsg() {
        this.msg = ''
      },
      sumChatBtnsWidth(width) {
        this.chatBtnsWidth += width + 4
      },
      sumCustomerServiceBtnsWidth(width) {
        this.customerServiceBtnsWidth += width + 4
      },
      sendStickerEvent(stickerId) {
        this.$emit('sendStickerEvent', stickerId)
      },
      sendCustomStickerEvent(paths) {
        this.$emit('sendCustomStickerEvent', paths)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .postion-relative {
    position: relative;
  }
  .msg-bar-text::v-deep {
    > .v-input__control {
      > .v-input__slot {
        > .v-input__append-inner {
          align-self: center;
          margin: 0;
        }
      }
    }
    textarea {
      max-height: 140px;
      overflow: auto;

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #886d6d;
      }
    }
  }
  .extra-btns {
    transition: max-width 0.3s ease-in-out;
    overflow-x: hidden;
    &.is-extra-btns-fold {
      max-width: 0 !important;
    }
  }
  @media (orientation: landscape) {
    .msg-bar-row {
      &.notch-left {
        padding-left: calc(16px + env(safe-area-inset-left)) !important;
      }
    }
  }
</style>
