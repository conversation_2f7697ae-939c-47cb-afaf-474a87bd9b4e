<template>
  <v-dialog v-model="showSettingDialogStatus" persistent max-width="457" content-class="rounded-lg">
    <v-card class="dialog-fill">
      <customDialogTitle :title="$t('chat_setting').toUpperCase()" @closeDialog="closeDialog" />
      <v-card-text
        :class="[
          'scrollable dialog-fill pa-4 pa-sm-6 scrollable-x-none',
          breakpoint.xsOnly ? '' : 'dialog-content-height'
        ]"
      >
        <!-- setting -->
        <v-row no-gutters>
          <v-col cols="12" class="mb-4">
            <span class="text-subtitle-2 default-content--text custom-text-noto mb-4">{{
              $t('background_transparency')
            }}</span>

            <v-slider
              v-model="opacity"
              :max="opacityaAttribute.max"
              :min="opacityaAttribute.min"
              color="primary-variant-1"
              hide-details
              class="chat-setting-slider-container"
            ></v-slider>
          </v-col>
          <v-col cols="12">
            <v-row no-gutters justify="space-between">
              <span class="text-body-1 default-content--text custom-text-noto mb-4">{{
                $t('clear_messages')
              }}</span>

              <v-btn
                class="button-content--text"
                :color="$UIConfig.defaultBtnColor"
                @click="clearMessages"
                elevation="0"
                >{{ $t('clear') }}</v-btn
              >
            </v-row>
          </v-col>
        </v-row>
        <template v-if="!onlyText">
          <v-divider class="my-6" />
          <!-- all channel -->
          <v-row no-gutters>
            <v-col cols="12">
              <v-row no-gutters>
                <span class="text-subtitle-2 default-content--text custom-text-noto mb-2">{{
                  $t('global_messages')
                }}</span>
              </v-row>
              <v-switch
                v-model="globalSticker"
                inset
                hide-details
                color="primary"
                :label="$t('sticker')"
                class="mt-4"
              ></v-switch>
              <v-switch
                v-model="globalCustomSticker"
                inset
                hide-details
                color="primary"
                :label="$t('custom_sticker')"
                class="mt-4"
              ></v-switch>
              <v-switch
                v-model="globalVoice"
                inset
                hide-details
                color="primary"
                :label="$t('voice')"
                class="mt-4"
              ></v-switch>
              <v-switch
                v-model="globalAutoVoice"
                inset
                hide-details
                color="primary"
                :label="$t('auto_play_voice')"
                class="mt-4"
              ></v-switch>
            </v-col>
          </v-row>

          <v-divider class="my-6" />
          <!-- channel -->
          <v-row no-gutters>
            <v-col cols="12">
              <v-row no-gutters>
                <span class="text-subtitle-2 default-content--text custom-text-noto mb-2">{{
                  $t('channel_messages')
                }}</span>
              </v-row>
              <v-switch
                v-model="channelSticker"
                inset
                hide-details
                color="primary"
                :label="$t('sticker')"
                class="mt-4"
              ></v-switch>
              <v-switch
                v-model="channelCustomSticker"
                inset
                hide-details
                color="primary"
                :label="$t('custom_sticker')"
                class="mt-4"
              ></v-switch>
              <v-switch
                v-model="channelVoice"
                inset
                hide-details
                color="primary"
                :label="$t('voice')"
                class="mt-4"
              ></v-switch>
              <v-switch
                v-model="channelAutoVoice"
                inset
                hide-details
                color="primary"
                :label="$t('auto_play_voice')"
                class="mt-4"
              ></v-switch>
            </v-col>
          </v-row>
        </template>
        <v-divider class="my-6" />
        <!-- whisper -->
        <v-row no-gutters>
          <v-col cols="12">
            <v-row no-gutters>
              <span class="text-subtitle-2 default-content--text custom-text-noto mb-2">{{
                $t('whisper_messages')
              }}</span>
            </v-row>
            <template v-if="!onlyText">
              <v-switch
                v-model="whisperSticker"
                inset
                hide-details
                color="primary"
                :label="$t('sticker')"
                class="mt-4 ml-1"
              >
              </v-switch>
              <v-switch
                v-model="whisperCustomSticker"
                inset
                hide-details
                color="primary"
                :label="$t('custom_sticker')"
                class="mt-4 ml-1"
              >
              </v-switch>
              <v-switch
                v-model="whisperVoice"
                inset
                hide-details
                color="primary"
                :label="$t('voice')"
                class="mt-4 ml-1"
              >
              </v-switch>
              <v-switch
                v-model="whisperAutoVoice"
                inset
                hide-details
                color="primary"
                :label="$t('auto_play_voice')"
                class="mt-4 ml-1"
              >
              </v-switch>

              <v-switch
                v-model="whisperNotySound"
                inset
                hide-details
                color="primary"
                :label="$t('play_notification_sound')"
                class="mt-4 ml-1"
              >
              </v-switch>
            </template>
            <v-switch
              v-model="whisperOnlyFriend"
              inset
              hide-details
              color="primary"
              :label="$t('show_friends_whispers_only')"
              class="mt-4 ml-1"
            ></v-switch>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
  import chat from '~/mixins/chatroom/chat'
  export default {
    name: 'settingDialog',
    mixins: [chat],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    props: {
      showSettingDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        opacityaAttribute: {
          min: 20,
          max: 100
        },
        onlyText: true
      }
    },
    computed: {
      opacity: {
        get({ $store }) {
          return $store.getters['chat/opacity'] * 100
        },
        set(val) {
          this.$store.commit('chat/SET_OPACITY', val / 100)
          this.saveSettingToLocalStorage()
        }
      },
      globalSticker: {
        get({ $store }) {
          return $store.getters['chat/globalSetting'].stiker
        },
        set(val) {
          this.$store.commit('chat/SET_GLOBAL_STIKER', val)
          this.saveSettingToLocalStorage()
        }
      },
      globalCustomSticker: {
        get({ $store }) {
          return $store.getters['chat/globalSetting'].customStiker
        },
        set(val) {
          this.$store.commit('chat/SET_GLOBAL_CUSTOM_STIKER', val)
          this.saveSettingToLocalStorage()
        }
      },
      globalVoice: {
        get({ $store }) {
          return $store.getters['chat/globalSetting'].voice
        },
        set(val) {
          this.$store.commit('chat/SET_GLOBAL_VOICE', val)
          this.saveSettingToLocalStorage()
        }
      },
      globalAutoVoice: {
        get({ $store }) {
          return $store.getters['chat/globalSetting'].autoVoice
        },
        set(val) {
          this.$store.commit('chat/SET_GLOBAL_AUTO_VOICE', val)
          this.saveSettingToLocalStorage()
        }
      },
      channelSticker: {
        get({ $store }) {
          return $store.getters['chat/channelSetting'].stiker
        },
        set(val) {
          this.$store.commit('chat/SET_CHANNEL_STIKER', val)
          this.saveSettingToLocalStorage()
        }
      },
      channelCustomSticker: {
        get({ $store }) {
          return $store.getters['chat/channelSetting'].customStiker
        },
        set(val) {
          this.$store.commit('chat/SET_CHANNEL_CUSTOM_STIKER', val)
          this.saveSettingToLocalStorage()
        }
      },
      channelVoice: {
        get({ $store }) {
          return $store.getters['chat/channelSetting'].voice
        },
        set(val) {
          this.$store.commit('chat/SET_CHANNEL_VOICE', val)
          this.saveSettingToLocalStorage()
        }
      },
      channelAutoVoice: {
        get({ $store }) {
          return $store.getters['chat/channelSetting'].autoVoice
        },
        set(val) {
          this.$store.commit('chat/SET_CHANNEL_AUTO_VOICE', val)
          this.saveSettingToLocalStorage()
        }
      },
      whisperSticker: {
        get({ $store }) {
          return $store.getters['chat/whisperSetting'].stiker
        },
        set(val) {
          this.$store.commit('chat/SET_WHISPER_STIKER', val)
          this.saveSettingToLocalStorage()
        }
      },
      whisperCustomSticker: {
        get({ $store }) {
          return $store.getters['chat/whisperSetting'].customStiker
        },
        set(val) {
          this.$store.commit('chat/SET_WHISPER_CUSTOM_STIKER', val)
          this.saveSettingToLocalStorage()
        }
      },
      whisperVoice: {
        get({ $store }) {
          return $store.getters['chat/whisperSetting'].voice
        },
        set(val) {
          this.$store.commit('chat/SET_WHISPER_VOICE', val)
          this.saveSettingToLocalStorage()
        }
      },
      whisperAutoVoice: {
        get({ $store }) {
          return $store.getters['chat/whisperSetting'].autoVoice
        },
        set(val) {
          this.$store.commit('chat/SET_WHISPER_AUTO_VOICE', val)
          this.saveSettingToLocalStorage()
        }
      },
      whisperNotySound: {
        get({ $store }) {
          return $store.getters['chat/whisperSetting'].notySound
        },
        set(val) {
          this.$store.commit('chat/SET_WHISPER_NOTY_SOUND', val)
          this.saveSettingToLocalStorage()
        }
      },
      whisperOnlyFriend: {
        get({ $store }) {
          return $store.getters['chat/whisperSetting'].onlyFriend
        },
        set(val) {
          this.$store.commit('chat/SET_WHISPER_ONLY_FRIEND', val)
          this.saveSettingToLocalStorage()
        }
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showSettingDialogStatus', false)
      },
      clearMessages() {
        this.$store.commit('chat/CLEAR_ALL_MSG')
        this.$store.commit('chat/SET_ALL_CHAT_NOTY', 0)
        this.$store.commit('chat/SET_WHISPER_NOTY', 0)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .chat-setting-slider-container {
    :deep(.v-slider) {
      margin: 0 !important;
    }
  }
  .scrollable-x-none {
    overflow-x: hidden;
  }
</style>
