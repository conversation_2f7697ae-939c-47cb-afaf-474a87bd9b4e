<template>
  <div class="msg-sticker scrollbar-thin">
    <v-overlay :value="$vuetify.breakpoint.xsOnly && stickerMenuStatus"></v-overlay>
    <v-menu
      v-model="stickerMenuStatus"
      :close-on-content-click="false"
      class="sticker-menu"
      :content-class="menuContentClass"
      attach
      eager
    >
      <template v-slot:activator="{ attrs }">
        <v-btn
          v-bind="attrs"
          icon
          color="default-content"
          :width="stickerBtnWidth"
          :disabled="stickerDisabled"
          @mousedown="switchStickerMenu"
        >
          <v-icon>mdi-emoticon-outline</v-icon>
        </v-btn>
      </template>
      <v-card flat class="pa-6 pt-3 rounded-0" color="dialog-fill">
        <v-card-title class="pa-0 pb-3">
          {{ $t('sticker') }}
        </v-card-title>
        <div v-show="stickerDownloadStatus === 'loading'" class="sticker-tabs-container px-4 py-3">
          <div class="sticker-tabs-wrapper">
            <div class="sticker-tabs">
              <div class="sticker-tab rounded">
                <v-row class="fill-height ma-0" align="center" justify="center">
                  <v-skeleton-loader dark width="51" height="48" type="card"></v-skeleton-loader>
                </v-row>
              </div>
            </div>
          </div>
          <div
            class="sticker-tabs-items pt-4"
            :style="{
              'max-width': $vuetify.breakpoint.xsOnly && isMobileAndLandscape ? 'initial' : ''
            }"
          >
            <div
              class="sticker-tab-item"
              :class="{ 'overflow-x-auto overflow-y-hidden': isMobileAndLandscape }"
            >
              <v-row
                class="gutters-16"
                :class="{
                  'flex-nowrap': isMobileAndLandscape,
                  'mb-1': isMobileAndLandscape && $device.isIos
                }"
              >
                <v-col
                  v-for="item in 6"
                  :key="item"
                  :cols="$vuetify.breakpoint.xsOnly && isMobileAndLandscape ? 'auto' : 4"
                >
                  <v-row class="fill-height ma-0" align="center" justify="center">
                    <v-skeleton-loader
                      tile
                      dark
                      width="80"
                      height="80"
                      type="card"
                    ></v-skeleton-loader>
                  </v-row>
                </v-col>
              </v-row>
            </div>
          </div>
        </div>
        <div v-if="stickerDownloadStatus === 'success'" class="sticker-tabs-container px-4 py-3">
          <div
            class="sticker-tabs-wrapper"
            :class="{ 'sticker-tab-grab': isGrab }"
            @mousedown.prevent="handleMouseDown"
            @mousemove.prevent="handleMouseMove"
            @mouseup="handleMouseUp"
            @mouseleave="handleMouseleave"
            ref="stickerTabsWrapperDom"
          >
            <div class="sticker-tabs">
              <div
                class="sticker-tab rounded"
                :class="{ 'sticker-tab-active': isActive(sticker.id) }"
                v-for="sticker in stickerList"
                :key="sticker.id"
                :ref="'stickerTabDom' + sticker.id"
                @click="selectStickerTab(sticker)"
              >
                <v-hover v-slot="{ hover }">
                  <div class="sticker-tab-img">
                    <!-- 不同狀態額外寫 v-img，是為了提前確認圖片是否存在 -->
                    <!-- 若藉由 src 動態換圖，hover/active 圖片不存在時，玩家操作(hover/click)才會跑 error 改讀預設圖 -->
                    <v-img
                      width="51"
                      height="48"
                      :src="sticker.normal"
                      @error="setOtherImg(sticker)"
                    >
                      <template v-slot:placeholder>
                        <v-row class="fill-height ma-0" align="center" justify="center">
                          <v-skeleton-loader
                            dark
                            width="51"
                            height="48"
                            type="card"
                          ></v-skeleton-loader>
                        </v-row>
                      </template>
                    </v-img>
                    <v-img
                      v-show="hover"
                      width="51"
                      height="48"
                      eager
                      :src="sticker.hover"
                      @error="setOtherImg(sticker)"
                    ></v-img>
                    <v-img
                      v-show="isActive(sticker.id)"
                      width="51"
                      height="48"
                      eager
                      :src="sticker.active"
                      @error="setOtherImg(sticker)"
                    ></v-img>
                  </div>
                </v-hover>
              </div>
            </div>
          </div>
          <div
            class="sticker-tabs-items pt-4"
            :style="{
              'max-width': $vuetify.breakpoint.xsOnly && isMobileAndLandscape ? 'initial' : ''
            }"
          >
            <div
              v-show="sticker.id === stickerTab"
              v-for="sticker in stickerList"
              class="sticker-tab-item"
              :class="{ 'overflow-x-auto overflow-y-hidden': isMobileAndLandscape }"
              :key="sticker.id"
            >
              <v-row
                class="gutters-16"
                :class="{
                  'flex-nowrap': isMobileAndLandscape,
                  'mb-1': isMobileAndLandscape && $device.isIos
                }"
              >
                <v-col
                  v-for="item in sticker.items"
                  :key="item.id"
                  :cols="$vuetify.breakpoint.xsOnly && isMobileAndLandscape ? 'auto' : 4"
                >
                  <v-img
                    width="80"
                    aspect-ratio="1"
                    :src="item.img"
                    :class="{ 'sticker-tab-item-default': isDefaultImg(item.img) }"
                    @click="sendStickerEvent(item)"
                    @error="setOtherImg(item)"
                    @load="setLoadedStatus(sticker.id, item.id)"
                  >
                    <template v-slot:placeholder>
                      <v-row class="fill-height ma-0" align="center" justify="center">
                        <v-skeleton-loader
                          tile
                          dark
                          width="80"
                          height="80"
                          type="card"
                        ></v-skeleton-loader>
                      </v-row>
                    </template>
                  </v-img>
                </v-col>
              </v-row>
            </div>
          </div>
        </div>
        <v-card-text v-else-if="stickerDownloadStatus === 'fail'" class="sticker-download-fail">
          <div>
            <v-img
              width="60"
              aspect-ratio="1"
              :src="stickerDownloadFailImg"
              @error="setOtherFormat"
            ></v-img>
          </div>
          <span>{{ $t('download_fail') }}</span>
        </v-card-text>
      </v-card>
    </v-menu>
  </div>
</template>

<script>
  const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
  const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

  export default {
    name: 'msgSticker',
    props: {
      stickerDisabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        stickerBtnWidth: 36,
        stickerDownloadStatus: 'loading',
        stickerList: [],
        stickerMenuStatus: false,
        stickerTab: null,
        isGrab: false,
        isGrabMoved: false,
        isStickerTabsFocus: true,
        startX: 0,
        scrollLeft: 0,
        stickerDefaultWebp: require('~/assets/image/chatroom/sticker/chat_sticker_default.webp'),
        stickerDefaultPng: require('~/assets/image/chatroom/sticker/chat_sticker_default.png'),
        stickerDownloadFailImg: null,
        stickerDownloadFailWebp: require('~/assets/image/chatroom/sticker/chat_sticker_download_fail.webp'),
        stickerDownloadFailPng: require('~/assets/image/chatroom/sticker/chat_sticker_download_fail.png')
      }
    },
    computed: {
      menuContentClass() {
        return this.$vuetify.breakpoint.xsOnly
          ? `w-100 elevation-0 ${this.isMobileAndLandscape ? ' isMobileAndLandscape' : ''}`
          : `elevation-24 ${this.isMobileAndLandscape ? ' isMobileAndLandscape' : ''}`
      },
      orientation({ $store }) {
        return $store.getters['deviceManagement/getOrientation']
      },
      isMobileAndLandscape() {
        return this.$device.isMobile && this.orientation !== 0
      }
    },
    watch: {
      stickerMenuStatus: {
        handler(val) {
          if (val) {
            window.addEventListener('focus', this.handleFocus)
            window.addEventListener('blur', this.handleBlur)
          } else {
            window.removeEventListener('focus', this.handleFocus)
            window.removeEventListener('blur', this.handleBlur)
          }
        }
      }
    },
    mounted() {
      this.initSticker()
      this.$emit('stickerBtnWidth', this.stickerBtnWidth)
    },
    methods: {
      async initSticker() {
        await this.getStickerList()
        if (this.stickerList.length) {
          const lastChatSticker = JSON.parse(localStorage.getItem('lastChatSticker'))
          this.stickerTab = lastChatSticker ? lastChatSticker.tabId : this.stickerList[0].id
        }
      },
      selectStickerTab(sticker) {
        if (this.isGrabMoved) return
        const tabId = sticker.id
        this.stickerTab = tabId
        localStorage.setItem('lastChatSticker', JSON.stringify({ tabId: tabId }))
      },
      isActive(tabId) {
        return this.stickerTab === tabId
      },
      handleMouseDown(e) {
        this.isGrabMoved = false
        this.isGrab = true
        this.startX = e.clientX
        this.scrollLeft = this.$refs.stickerTabsWrapperDom.scrollLeft
      },
      handleMouseMove(e) {
        // 因網站被重新聚焦時，click/mousedown 會異常觸發 mousemove 事件，所以加上 isStickerTabsFocus 判斷
        if (!this.isGrab || !this.isStickerTabsFocus) return
        this.isGrabMoved = true
        const endX = e.clientX
        const moveX = endX - this.startX
        this.$refs.stickerTabsWrapperDom.scrollLeft = this.scrollLeft - moveX
      },
      handleMouseUp() {
        if (!this.isGrab) return
        this.isGrab = false
      },
      handleMouseleave() {
        if (!this.isGrab) return
        this.isGrab = false
      },
      handleFocus() {
        setTimeout(() => (this.isStickerTabsFocus = true), 50)
      },
      handleBlur() {
        this.isStickerTabsFocus = false
      },
      // 取得貼圖清單
      async getStickerList() {
        try {
          const stickerData = await this.$axios.get(
            process.env.IMAGE_URL +
              `/chat_stickers/sticker_provider/${loadConfig.client_id}/sticker_provider.json?` +
              Math.random()
          )
          this.stickerDownloadStatus = 'success'
          const baseLink = `${process.env.IMAGE_URL}/chat_stickers/sticker_img/${loadConfig.client_id}/`
          this.stickerList = stickerData.data.map((data) => ({
            ...data,
            normal: baseLink + data.normal,
            hover: baseLink + data.hover,
            active: baseLink + data.active,
            items: data.items.map((item) => ({
              id: item.id,
              img: baseLink + item.img,
              loaded: false
            }))
          }))
        } catch (e) {
          this.stickerDownloadStatus = 'fail'
          this.stickerDownloadFailImg = this.stickerDownloadFailWebp
        }
      },
      isDefaultImg(img) {
        return img === this.stickerDefaultWebp || img === this.stickerDefaultPng
      },
      setOtherImg(sticker) {
        // 更換為預設圖(webp) or 更改副檔名(從 webp 轉 png)
        const modifyImg = (img) =>
          img.endsWith('.png') ? this.stickerDefaultWebp : img.replace('.webp', '.png')

        // 判斷為貼圖的類別圖 or 內容圖(stickerList 的子項目 items)
        if (sticker.normal) {
          const parentItem = this.stickerList.find((item) => item.id === sticker.id)
          // 若為預設圖，則確保使用 png 格式
          // 若非預設圖，更換為預設圖(webp) or 更改副檔名(從 webp 轉 png)
          parentItem.normal = this.isDefaultImg(parentItem.normal)
            ? this.stickerDefaultPng
            : modifyImg(parentItem.normal)
          parentItem.hover = this.isDefaultImg(parentItem.normal)
            ? this.stickerDefaultPng
            : modifyImg(parentItem.hover)
          parentItem.active = this.isDefaultImg(parentItem.normal)
            ? this.stickerDefaultPng
            : modifyImg(parentItem.active)
        } else {
          this.stickerList.some((item) => {
            const subItem = item.items.find((sub) => sub.id === sticker.id)
            if (subItem) {
              // 若為預設圖，則確保使用 png 格式
              // 若非預設圖，更換為預設圖(webp) or 更改副檔名(從 webp 轉 png)
              subItem.img = this.isDefaultImg(subItem.normal)
                ? this.stickerDefaultPng
                : modifyImg(subItem.img)
              return true // 結束迴圈
            }
            return false
          })
        }
      },
      setOtherFormat() {
        this.stickerDownloadFailImg = this.stickerDownloadFailPng
      },
      sendStickerEvent(sticker) {
        if (!sticker.loaded) return
        this.stickerMenuStatus = false
        this.$emit('sendStickerEvent', sticker.id)
      },
      // 更改貼圖內容加載狀態
      setLoadedStatus(tabId, tabItemId) {
        const parentItem = this.stickerList.find((item) => item.id === tabId)
        if (parentItem) {
          const subItem = parentItem.items.find((item) => item.id === tabItemId)
          subItem && (subItem.loaded = true)
        }
      },
      async switchStickerMenu() {
        this.stickerMenuStatus = !this.stickerMenuStatus
        // 若此動作為關閉貼圖彈窗，則不執行以下步驟
        if (!this.stickerMenuStatus) return
        // 若貼圖清單下載失敗，則嘗試重新下載
        if (this.stickerDownloadStatus === 'fail') {
          this.stickerDownloadStatus = 'loading'
          await this.initSticker()
        }
        // 若貼圖清單重新下載仍失敗，則不執行以下步驟
        if (this.stickerDownloadStatus === 'fail') return
        this.$nextTick(() => {
          requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              // 等待一點時間，確保 tab 清單出現，再操縱水平滾軸位置
              setTimeout(() => {
                const wrapper = this.$refs.stickerTabsWrapperDom
                const activeTab = this.$refs[`stickerTabDom${this.stickerTab}`][0]

                const wrapperOffsetLeft = wrapper.offsetLeft
                const wrapperWidth = wrapper.offsetWidth
                const tabOffsetLeft = activeTab.offsetLeft
                const tabWidth = activeTab.offsetWidth

                wrapper.scrollLeft =
                  tabOffsetLeft - wrapperOffsetLeft - (wrapperWidth - tabWidth) / 2
              }, 50)
            })
          })
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  $grey-5: map-get($colors, 'grey-5');
  $btn-disable-color: map-get($colors, btn-disable);

  .sticker-menu {
    display: initial;
    > .v-menu__content::v-deep {
      top: initial !important;
      bottom: 0;
      left: initial !important;
      right: 0;
      border-radius: 12px 12px 0px 0px;
      @media screen and (min-width: 600px) {
        bottom: 100%;
        max-width: 352px;
        border-radius: 8px;
        &.isMobileAndLandscape {
          bottom: 0;
          right: 16px;
        }
      }
      .v-card__title {
        font-size: 16px;
        font-weight: 400;
        line-height: 28px;
        letter-spacing: 0.15px;
      }
    }

    .sticker-tabs-container {
      background-color: $grey-5;
      border-radius: 8px !important;
      @media screen and (min-width: 600px) {
        border-radius: 4px !important;
      }
      .sticker-tabs-wrapper {
        overflow-x: auto;
        &.sticker-tab-grab,
        &.sticker-tab-grab .sticker-tab {
          cursor: grabbing !important;
        }
        .sticker-tabs {
          display: flex;
          margin-bottom: 4px;
          .sticker-tab {
            cursor: pointer;
            &:not(:last-child) {
              margin-right: 8px;
            }
            &.sticker-tab-active {
              pointer-events: none;
              background-color: $btn-disable-color;
            }
            .sticker-tab-img {
              position: relative;
              .v-image:not(:first-child) {
                position: absolute;
                top: 0;
                left: 0;
              }
            }
          }
        }
      }
      .sticker-tabs-items {
        max-width: 272px;
        margin: 0px auto;

        .v-image {
          cursor: pointer;

          &.sticker-tab-item-default {
            pointer-events: none;
          }
        }
      }
    }

    .sticker-download-fail {
      min-height: 272px;
      background-color: $grey-5;
      border-radius: 8px !important;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      @media screen and (min-width: 600px) {
        border-radius: 4px !important;
        min-width: 304px;
      }
      span {
        font-size: 12px;
        color: $btn-disable-color;
      }
    }
  }

  .gutters-16 {
    margin: -8px;
    .col {
      padding: 8px;
    }
  }
</style>
