<template>
  <v-container v-show="!value" fluid class="cookie_notice pa-0">
    <v-row no-gutters justify="center" align="center" class="py-sm-10 py-6 px-6">
      <i18n path="click_accept_privacy_policy" tag="span">
        <template v-slot:privacy_policy>
          <a
            class="text-decoration-none pl-1"
            :href="cookieNotice.herf"
            :target="cookieNotice.target"
            @click="cookieNotice.click ? goToPrivacy() : null"
            >{{ $t('privacy_policy') }}
          </a>
        </template>
      </i18n>

      <div
        :class="[
          {
            'w-100': $vuetify.breakpoint.smAndDown,
            'd-flex justify-center': $vuetify.breakpoint.smAndDown
          },
          'ml-md-4',
          'ml-0',
          'mt-md-0',
          'mt-2'
        ]"
      >
        <v-btn elevation="0" outlined color="primary" @click="accept">
          <span>{{ $t('accept') }}</span>
        </v-btn>
      </div>
    </v-row>
  </v-container>
</template>

<script>
  const STATION = process.env.STATION

  export default {
    name: 'CookieNotice',
    props: {
      value: { type: Boolean, dafault: false }
    },
    computed: {
      cookieNotice() {
        return this.$UIConfig.cookieNotice
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      }
    },
    methods: {
      accept() {
        let maturityDate = this.$moment().add(365, 'days')
        this.$localStorage.set('accept_cookie_policy', { expired: maturityDate })
        // 沒登入才刷新
        // 站台差異註解
        if (!this.$UIConfig.cookieNotice.reflashOnAccept) {
          if (!(this.isLogin || this.hasAuthKey())) {
            location.reload()
          }
        } else {
          if (!this.isLogin) {
            location.reload()
          }
        }
        this.$nuxt.$emit('game:acceptCookiePolicyMessage', true)
        this.$emit('input', true)
      },
      goToPrivacy() {
        const companyInfoIdx = this.cookieNotice.companyInfoIdx
        const companySetting = this.cookieNotice.companySetting
        this.$store.commit(`${STATION}/companyInfo/${companySetting}`, companyInfoIdx)
        this.$nuxt.$emit('root:showCompanyDialogStatus', true)
      },
      hasAuthKey() {
        return this.$route.query.loginAuth
      }
    }
  }
</script>
<style scoped lang="scss">
  .cookie_notice {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    display: block;
    z-index: 1000; // 為了蓋過其他 dialog
    background: rgba(black, 90%);
  }
</style>
