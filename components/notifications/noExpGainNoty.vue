<template>
  <v-dialog
    v-model="showNotyNoExpGainNotyDialogStatusTmp.show"
    persistent
    max-width="380"
    content-class="rounded-lg"
  >
    <v-card color="transparent" class="pa-4 pa-sm-6">
      <v-card-title
        class="custom-text-noto text-h6 justify-center font-weight-regular grey-1--text pa-0"
        v-text="$t('reminder')"
      />
      <v-card-text class="default-content--text px-0 py-6">
        {{ $t('game_no_experience_accumulation_warning') }}
      </v-card-text>
      <v-card-actions no-gutters class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
            ><v-btn
              elevation="0"
              :class="['custom-text-noto default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              text
              @click="reconsider"
            >
              {{ $t('reconsider').toUpperCase() }}
            </v-btn></v-col
          >
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              elevation="0"
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              @click="playGame"
            >
              {{ $t('start_playing').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  export default {
    name: 'noExpGainNoty',
    mixins: [hiddenScrollHtml],
    props: {
      showNotyNoExpGainNotyDialogStatus: {
        type: Object,
        default: () => ({ show: false, onConfirmNotify: () => {}, onCancelNotify: () => {} })
      },
      hasRobot: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        showNotyNoExpGainNotyDialogStatusTmp: this.showNotyNoExpGainNotyDialogStatus
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showNotyNoExpGainNotyDialogStatus: {
        handler(status) {
          this.showNotyNoExpGainNotyDialogStatusTmp = status
        }
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showNotyNoExpGainNotyDialogStatus', {
          show: false,
          onConfirmNotify: () => {},
          onCancelNotify: () => {}
        })
      },
      playGame() {
        this.showNotyNoExpGainNotyDialogStatusTmp.onConfirmNotify?.()
        this.closeDialog()
      },
      reconsider() {
        this.showNotyNoExpGainNotyDialogStatusTmp.onCancelNotify?.()
        this.closeDialog()
      }
    }
  }
</script>
