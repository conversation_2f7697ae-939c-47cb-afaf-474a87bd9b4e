<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog v-model="localShowNotyBothRobotExpNotyDialogStatus.show" persistent max-width="380">
    <v-card color="transparent">
      <v-card-title
        class="pa-6 custom-text-noto text-h6 justify-center font-weight-regular grey-1--text"
        v-text="$t('reminder')"
      />
      <v-card-text class="pl-6 pr-6 pt-0 default-content--text">
        {{ $t('game_both_warnings') }}
      </v-card-text>
      <v-card-actions no-gutters>
        <v-spacer />
        <v-btn
          elevation="0"
          class="mx-2 mb-4 custom-text-noto default-content--text"
          text
          @click="reconsider"
        >
          {{ $t('reconsider').toUpperCase() }}
        </v-btn>

        <v-btn
          elevation="0"
          class="mx-2 mb-4 mr-2 button-content--text"
          :color="$UIConfig.defaultBtnColor"
          @click="playGame"
        >
          {{ $t('start_playing').toUpperCase() }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  export default {
    name: 'BothRobotExpNoty',
    mixins: [hiddenScrollHtml],
    props: {
      showNotyBothRobotExpNotyDialogStatus: {
        type: Object,
        default: () => ({ show: false, onConfirmNotify: () => {}, onCancelNotify: () => {} })
      }
    },
    data() {
      return {
        localShowNotyBothRobotExpNotyDialogStatus: this.showNotyBothRobotExpNotyDialogStatus
      }
    },
    watch: {
      showNotyBothRobotExpNotyDialogStatus: {
        handler(status) {
          this.localShowNotyBothRobotExpNotyDialogStatus = status
        }
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showNotyBothRobotExpNotyDialogStatus', {
          show: false,
          onConfirmNotify: () => {},
          onCancelNotify: () => {}
        })
      },
      playGame() {
        this.localShowNotyBothRobotExpNotyDialogStatus.onConfirmNotify?.()
        this.closeDialog()
      },
      reconsider() {
        this.localShowNotyBothRobotExpNotyDialogStatus.onCancelNotify?.()
        this.closeDialog()
      }
    }
  }
</script>
