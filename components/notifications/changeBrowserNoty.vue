<template>
  <div>
    <v-alert
      v-model="showChangeBrowserNotyStatusTmp"
      dense
      rounded="0"
      type="info"
      transition="scale-transition"
    >
      <v-row align="center">
        <v-col cols="8" sm="10" class="grow default-content--text">
          {{ $t('change_browser_noty') }}
          <v-progress-circular :rotate="180" :size="30" :width="3" :value="timer">
            {{ timer / 10 }}
          </v-progress-circular>
        </v-col>
        <v-col cols="4" sm="2" class="shrink">
          <v-btn color="default-content" class="info--text" @click="checkBrowser">{{
            $t('click_open')
          }}</v-btn>
        </v-col>
      </v-row>
    </v-alert>
  </div>
</template>
<script>
  export default {
    name: 'changeBrowserNoty',
    props: {
      showChangeBrowserNotyStatus: {
        type: <PERSON><PERSON>an
      }
    },
    data() {
      return {
        showChangeBrowserNotyStatusTmp: this.showChangeBrowserNotyStatus,
        interval: {},
        timer: 300
      }
    },
    watch: {
      showChangeBrowserNotyStatus: {
        immediate: true,
        handler(val) {
          this.showChangeBrowserNotyStatusTmp = val
        }
      },
      timer: {
        handler(val) {
          if (val === 0) {
            this.showChangeBrowserNotyStatusTmp = false
            this.$emit('update:showChangeBrowserNotyStatus', false)
            clearInterval(this.interval)
          }
        }
      }
    },
    beforeDestroy() {
      clearInterval(this.interval)
    },
    mounted() {
      document.writeln('請按上或下方的箭頭，選擇「在 Safari 開啟」以繼續進行活動。')

      this.interval = setInterval(() => {
        if (this.timer === 0) {
          return (this.timer = 300)
        }
        this.timer -= 10
      }, 1000)
    },
    methods: {
      checkBrowser() {
        // ANDROID 吃 document IOS 吃 window
        if (!this.$device.isMobile && this.$ua._parsed.name === 'Webview') {
          return false
        } else if (this.$device.isAndroid) {
          document.location =
            'intent://' +
            document.location.host +
            document.location.search +
            '#Intent;scheme=' +
            document.location.protocol +
            ';end'
          // APPLE 系列目前只有LINE APP 有提供方法，其他都不行
          // } else if (this.$device.isIos || this.$device.isMacOS) {
          // window.location.replace(window.location.protocol + '://' + window.location.host)
        }
      }
    }
  }
</script>
