<template>
  <v-container fluid class="pa-0">
    <v-row
      no-gutters
      class="py-4 px-8 rounded-lg"
      d-flex
      id="pwaInstallBlock"
      :class="$vuetify.breakpoint.xsOnly ? 'flex-column' : ''"
    >
      <!-- 文字 -->
      <v-col class="pa-0" cols="10" md="10">
        <v-row
          no-gutters
          class="button-content--text custom-text-noto font-weight-bold"
          :class="$vuetify.breakpoint.xsOnly ? 'text-h6' : 'text-h5'"
        >
          <span class="text-nowrap">{{ $t('webPwaInstall') }}</span>
        </v-row>
        <v-row
          no-gutters
          class="button-content--text custom-text-noto text-subtitle-2 text-start"
          :class="$vuetify.breakpoint.xsOnly ? 'mt-0' : 'mt-2'"
        >
          {{ $t('webPwaInstalltext') }}
        </v-row>
      </v-col>
      <!-- 按鈕 -->
      <v-col
        class="pa-0 d-flex align-center"
        cols="2"
        :class="$vuetify.breakpoint.xsOnly ? 'mt-3' : ''"
      >
        <v-row
          no-gutters
          class="d-flex"
          :class="$vuetify.breakpoint.smAndDown ? 'justify-start' : 'justify-end'"
        >
          <v-btn
            elevation="0"
            rounded
            color="gradient-button"
            class="btn-animation button-content--text"
            @click="toPage('/teach/pwa')"
          >
            {{ $t('goToTeach') }}
          </v-btn>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  import images from '~/mixins/images'
  export default {
    name: 'pwaInstallBlock',
    mixins: [images],
    data() {
      return {}
    },
    created() {},
    async mounted() {
      // 獲取要設定背景的元素（這可以是 body 元素或任何其他元素）
      const targetElement = document.getElementById('pwaInstallBlock') // 替換為你的元素 ID
      // 設定背景圖片 URL
      const backgroundImageUrl = await this.getImage('pwa/pwa-card-bg.svg')
      // 使用 style 屬性設定背景圖片
      targetElement.style.backgroundImage = `url(${backgroundImageUrl})`
      targetElement.style.backgroundPositionX = 'center'
      targetElement.style.backgroundPositionY = 'center'
    },
    methods: {
      toPage(link) {
        //換頁
        this.$router.push({ path: this.localePath(link) })
      }
    }
  }
</script>
<style scoped>
  #pwaInstallBlock {
    background-size: cover;
  }
  .text-nowrap {
    white-space: pre;
    word-wrap: break-word;
  }
</style>
