<!-- eslint-disable vue/no-v-html -->
<template>
  <div>
    <v-card
      height="100%"
      color="transparent"
      class="pa-2"
      elevation="0"
      @click="showPaymentMethod(providerInfo)"
    >
      <v-img
        :lazy-src="providerInfo.img"
        :aspect-ratio="180 / 116"
        height="100%"
        :src="providerInfo.img"
        class="rounded-lg"
      >
        <template #placeholder>
          <v-row class="fill-height ma-0" align="center" justify="center">
            <v-progress-circular indeterminate color="grey lighten-5" />
          </v-row>
        </template>
        <div
          v-if="providerInfo.maintained"
          class="d-flex v-card--reveal gradient-game-maintenance"
          style="height: 100%"
        >
          <span class="material-symbols-outlined white--text" style="font-size: 48px">
            construction
          </span>
        </div>
      </v-img>
    </v-card>
    <v-card color="transparent" elevation="0">
      <v-card-text
        class="pa-0 px-2 text-center default-content--text text-subtitle-2 custom-text-noto textwrap"
      >
        <div>
          <span> {{ showPaymentDict(providerInfo.dict) }}</span>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  const STATION = process.env.STATION
  export default {
    name: 'PaymentproviderInfoCard',
    mixins: [analytics],
    props: {
      providerInfo: { type: Object, default: () => {} }
    },
    async mounted() {
      if (this.providerInfo.dict === 'gash') {
        const index = this.providerInfo.id - 1
        const status = true
        this.$store.commit(`${STATION}/payment/SET_MAINTAIN_STATUS`, {
          index,
          status: !status
        })
      }
    },
    methods: {
      showPaymentMethod(providerInfo) {
        if (providerInfo.maintained) {
          return
        }

        this.$emit('showPaymentMethod', providerInfo.id, providerInfo)
      },
      showPaymentDict(dict) {
        const dictText = this.$UIConfig.paymentIndex.dictUpper
          ? this.$t(dict).toUpperCase()
          : this.$t(dict)
        return this.$UIConfig.paymentIndex.paymentDict.format(dictText, this.$t('stored_value'))
      }
    }
  }
</script>
<style lang="scss" scoped>
  .textwrap {
    white-space: pre-wrap;
  }
</style>
