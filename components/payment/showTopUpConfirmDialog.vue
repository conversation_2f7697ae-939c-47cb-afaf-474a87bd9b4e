<template>
  <div>
    <v-dialog
      v-model="showTopUpConfirmDialogStatusTmp"
      persistent
      max-width="400px"
      content-class="rounded-lg"
    >
      <v-card elevation="0" tile color="dialog-fill" class="pa-4 pa-sm-6">
        <v-card-title
          class="custom-text-noto text-h6 justify-center default-content--text pa-0"
          v-text="confirmData.title"
        />
        <v-card-text class="px-0 py-6">
          <div
            v-if="breakpoint.xsOnly"
            class="default-content--text message-break"
            v-html="confirmData.message"
          />
          <pre v-else class="default-content--text" v-html="confirmData.message" />
        </v-card-text>
        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
              ><v-btn
                :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                elevation="0"
                text
                @click="closeDialog"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                :disabled="disabledTopUpBtnStatus"
                @click="doTopUp(confirmData.data.paymentProviderId, confirmData.data.req)"
              >
                {{ confirmData.btnText }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
  const STATION = process.env.STATION
  export default {
    name: 'ShowTopUpConfirmDialog',
    props: {
      showTopUpConfirmDialogStatus: { type: Boolean, default: false },
      showConvertConfirmDialogStatus: { type: Boolean, default: false },
      showTopUpSuccessDialogStatus: { type: Boolean, default: false },
      storedType: { type: String, default: '' }
    },
    data() {
      return {
        showTopUpConfirmDialogStatusTmp: this.showTopUpConfirmDialogStatus,
        disabledTopUpBtnStatus: false,
        showSuccessDialogStatus: false,
        successStoredData: {}
      }
    },
    computed: {
      confirmData({ $store }) {
        return $store.getters[`${STATION}/payment/confirmData`]
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      selfPoints({ $store }) {
        return $store.getters['role/points']
      },
      selfLevel({ $store }) {
        return $store.getters['role/level']
      },
      storedPoint({ $store }) {
        return $store.getters[`${STATION}/payment/storedPoint`]
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showTopUpConfirmDialogStatus(val) {
        this.showTopUpConfirmDialogStatusTmp = val
      }
    },
    methods: {
      closeDialog() {
        this.showTopUpConfirmDialogStatusTmp = false
        this.$emit('update:showTopUpConfirmDialogStatus', false)
      },
      async doTopUp(paymentProviderId, req) {
        this.disabledTopUpBtnStatus = true
        setTimeout(() => {
          this.disabledTopUpBtnStatus = false
        }, 1000)

        try {
          let res = []
          switch (paymentProviderId) {
            case 1:
              {
                this.$wsClient.send(this.$wsPacketFactory.useYoeCard(req.cardNumber, req.cardPwd))
              }
              break
            case 2:
              {
                this.$wsClient.send(this.$wsPacketFactory.useDigitalYoeCard(req.cardSerialNumber))
              }
              break
            case 3:
              {
                this.$wsClient.send(this.$wsPacketFactory.useGashCard(req.cardGashPwd))
                res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
                  return data.isFeature(this.$xinConfig.FEATURE.BANK.TYPE.VERIFY)
                })
                if (!res.isSuccess) {
                  this.$notify.error(res.message)
                  this.closeDialog()
                }
              }
              break

            default:
              break
          }
        } catch (error) {
          console.log(error)
        }
      }
    }
  }
</script>
<style lang="scss">
  @media (max-width: 600px) {
    .message-break {
      white-space: pre-wrap;
      overflow-wrap: break-word;
    }
  }
</style>
