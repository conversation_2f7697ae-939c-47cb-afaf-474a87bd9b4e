<template>
  <div>
    <v-dialog
      v-model="showVipTopUpSuccessDialogStatusTmp"
      persistent
      max-width="400px"
      content-class="rounded-lg"
    >
      <v-card elevation="0" tile color="dialog-fill" class="pa-4 pa-sm-6">
        <customDialogTitle :title="$t('hint')" @closeDialog="closeDialog" class="pa-0" />
        <v-card-text class="default-content--text text-body-2 custom-text-noto px-0 py-6">
          {{ $t('vip_payment_success_msg') }}
        </v-card-text>
        <v-card-actions class="pa-0">
          <v-btn
            :class="[
              'button-content--text text-button custom-text-noto',
              breakpoint.xsOnly ? 'w-100' : ''
            ]"
            :color="$UIConfig.defaultBtnColor"
            elevation="0"
            @click="openMailDialog"
          >
            {{ $t('sure') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
  const STATION = process.env.STATION
  export default {
    name: 'showVipTopUpSuccessDialog',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    props: {
      showVipTopUpSuccessDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {
        disabledTopUpBtnStatus: false,
        showVipTopUpSuccessDialogStatusTmp: this.showVipTopUpSuccessDialogStatus
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showVipTopUpSuccessDialogStatus(val) {
        this.$store.commit(`${STATION}/payment/CLEAR_STOREDPOINT`)
        this.showVipTopUpSuccessDialogStatusTmp = val
      }
    },
    methods: {
      closeDialog() {
        this.showVipTopUpSuccessDialogStatusTmp = false
        this.$emit('update:showVipTopUpSuccessDialogStatus', false)
      },
      openMailDialog() {
        this.closeDialog()
        this.$nuxt.$emit('root:showPaymentDialogStatus', false)
        this.$nuxt.$emit('root:mailDialogStatus', { show: true, name: '' })
      }
    }
  }
</script>
