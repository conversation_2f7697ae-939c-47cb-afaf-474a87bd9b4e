<template>
  <div>
    <v-dialog
      v-model="showTopUpSuccessDialogStatusTmp"
      persistent
      max-width="400px"
      content-class="rounded-lg"
    >
      <v-card elevation="0" tile color="dialog-fill" class="pa-4 pa-sm-6">
        <v-card-title class="custom-text-noto text-h6 justify-center pa-0">
          {{ $t('top_up_success_title') }}
        </v-card-title>
        <v-card-text class="default-content--text px-0 py-6" v-html="showMessage(storedType)" />
        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
              ><v-btn
                :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                elevation="0"
                text
                @click="closeDialog"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                @click="doConvert"
              >
                {{ $t('convert').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
  const STATION = process.env.STATION
  export default {
    name: 'showTopUpSuccessDialog',
    props: {
      showTopUpConfirmDialogStatus: { type: Boolean, default: false },
      showConvertConfirmDialogStatus: { type: Boolean, default: false },
      showTopUpSuccessDialogStatus: { type: Boolean, default: false },
      storedType: { type: String, default: '' }
    },
    data() {
      return {
        disabledTopUpBtnStatus: false,
        showTopUpSuccessDialogStatusTmp: this.showTopUpSuccessDialogStatus
      }
    },
    computed: {
      selfPoints({ $store }) {
        return $store.getters['role/points']
      },
      selfLevel({ $store }) {
        return $store.getters['role/level']
      },
      currentPoint({ $store }) {
        return $store.getters[`${STATION}/payment/currentPoint`]
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showTopUpSuccessDialogStatus(val) {
        this.$store.commit(`${STATION}/payment/CLEAR_CURRENTPOINT`)
        this.showTopUpSuccessDialogStatusTmp = val
      }
    },
    methods: {
      closeDialog() {
        this.$store.commit(`${STATION}/payment/CLEAR_CURRENTPOINT`)
        this.showTopUpSuccessDialogStatusTmp = false
        this.$emit('update:showTopUpSuccessDialogStatus', false)
      },
      showMessage(storedType) {
        let message = ''
        if (storedType === 'BEGINNER') {
          message =
            this.$t('top_up_success_noty1', { points: this.currentPoint }) +
            '<br />' +
            '<br />' +
            this.$t('top_up_success_noty2_1') +
            '<br />' +
            '<br />' +
            this.$t('top_up_success_noty4') +
            '<br />' +
            this.$t('top_up_success_noty5')
        } else {
          message =
            this.$t('top_up_success_noty6', { points: this.currentPoint }) +
            '<br />' +
            '<br />' +
            this.$t('top_up_success_noty4') +
            '<br />' +
            this.$t('top_up_success_noty5')
        }

        return message
      },
      async doConvert() {
        this.$emit('update:showConvertConfirmDialogStatus', true)
        this.$emit('update:showTopUpSuccessDialogStatus', false)
      }
    }
  }
</script>
