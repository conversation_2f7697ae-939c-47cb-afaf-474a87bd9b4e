<template>
  <div>
    <v-dialog
      v-model="showPaymentDialogStatusTmp"
      :fullscreen="breakpoint.xsOnly"
      max-width="620"
      persistent
      transition="dialog-transition"
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card elevation="0" tile color="dialog-fill" id="paymentDialog">
        <customDialogTitle
          :title="$t('stored_mall')"
          @closeDialog="closeDialog"
          :disabled="disableBtn"
        />
        <div :class="breakpoint.xsOnly ? 'scrollable-xs' : 'scrollable-sm'">
          <!-- 標籤 -->
          <v-tabs
            ref="tabs"
            background-color="transparent"
            color="primary"
            class="px-4 px-sm-6"
            show-arrows
            v-model="selectType"
          >
            <v-tab
              href="#yoeGame"
              class="a-link"
              key="yoeGame"
              :disabled="disableBtn"
              v-if="$UIConfig.paymentIndex.showGiftPack"
            >
              {{ $t('yoeGame') }}
            </v-tab>
            <v-tab class="a-link" href="#buy_points" key="buy_points" :disabled="disableBtn">
              {{ $t('buy_points') }}
            </v-tab>
            <v-tab
              v-if="payVip && $UIConfig.paymentIndex.showVipTab"
              class="a-link"
              href="#vip_buy_points"
              key="vip_buy_points"
              :disabled="disableBtn"
            >
              {{ $t('vip_discount') }}
            </v-tab>
            <v-tab href="#convert" class="a-link" key="convert" :disabled="disableBtn">
              {{ $t('convert') }}
            </v-tab>
            <v-tab href="#coffer" class="a-link" key="coffer" :disabled="disableBtn">
              {{ $t('coffer') }}
            </v-tab>
          </v-tabs>
          <!-- 內容 -->
          <v-tabs-items v-model="selectType">
            <!-- 商城 -->
            <v-tab-item key="yoeGame" value="yoeGame" v-if="$UIConfig.paymentIndex.showGiftPack">
              <v-container fluid class="pa-6 pt-4">
                <template v-if="yoeShopArray && !maintainSystem[0].maintaining">
                  <giftPackArea :provider-list="yoeShopArray" />
                </template>
                <div v-else class="no-purchase-items custom-text-noto mb-3">
                  <p class="mb-1">{{ $t('no_purchase_items') }}</p>
                  <span>{{ $t('contact_support') }}</span>
                </div>
                <span class="custom-text-noto gift-pack-remark mt-6">{{ $t('no_refund') }}</span>
              </v-container>
            </v-tab-item>
            <!-- 購點 -->
            <v-tab-item key="buy_points" value="buy_points">
              <v-container fluid class="px-2 pt-3 pb-4 px-sm-4 pt-sm-5 pb-sm-6">
                <!-- 序號購點 -->
                <v-row
                  v-if="paymentProviderList.filter((item) => item.paymentMethod === 1).length > 0"
                  class="px-2 py-1 align-center"
                >
                  <v-col class="default-content--text">
                    {{ $t('serial_number') + $t('buy_points') }}
                  </v-col>
                </v-row>
                <v-row
                  v-if="paymentProviderList.filter((item) => item.paymentMethod === 1).length > 0"
                  no-gutters
                  align="center"
                >
                  <v-col
                    v-for="(providerInfo, index) in paymentProviderList.filter(
                      (item) => item.paymentMethod === 1
                    )"
                    :key="index"
                    cols="6"
                    sm="4"
                    md="4"
                    lg="4"
                    class="cursor-pointer"
                  >
                    <paymentProviderCard
                      :provider-info="providerInfo"
                      @showPaymentMethod="showPaymentMethod"
                    />
                  </v-col>
                </v-row>
                <v-divider
                  v-if="paymentProviderList.filter((item) => item.paymentMethod === 1).length > 0"
                  class="mx-2 my-4 my-sm-6"
                />
                <!-- 線上購點 -->
                <v-row class="px-2 py-1 align-center">
                  <v-col class="default-content--text">
                    {{ $t('top_up_title') }}
                  </v-col>
                </v-row>
                <v-row no-gutters align="center">
                  <v-col
                    v-for="(providerInfo, index) in paymentProviderList.filter(
                      (item) => item.paymentMethod === 2
                    )"
                    :key="index"
                    align-self="start"
                    cols="6"
                    sm="4"
                    md="4"
                    lg="4"
                    class="cursor-pointer"
                  >
                    <paymentProviderCard
                      :provider-info="providerInfo"
                      @showPaymentMethod="showPaymentMethod"
                    />
                  </v-col>
                </v-row>
              </v-container>
            </v-tab-item>
            <!-- VIP購點 -->
            <v-tab-item
              v-if="payVip && $UIConfig.paymentIndex.showVipTab"
              key="vip_buy_points"
              value="vip_buy_points"
            >
              <v-container fluid class="px-6">
                <!-- 文字 -->
                <v-row no-gutters class="flex-column">
                  <div class="text-body-1 custom-text-noto mb-2 mt-6 default-content--text">
                    {{ $t('vip_discount_text1') }}
                  </div>
                  <div class="text-body-2 custom-text-noto default-content--text">
                    {{ $t('vip_discount_text2') }}
                  </div>
                  <div class="text-body-2 custom-text-noto default-content--text">
                    {{ $t('vip_discount_text3') }}
                  </div>
                  <div
                    class="text-body-2 custom-text-noto default-content--text"
                    style="white-space: pre-line"
                  >
                    {{ $t('vip_discount_text4') }}
                  </div>
                </v-row>
                <v-row class="align-center justify-left">
                  <!-- 可購買上限 輸入框 -->
                  <v-col cols="12" sm="6" md="6" lg="6">
                    <v-text-field
                      v-model="selfLimit"
                      disabled
                      name="limit"
                      :label="$t('buy_cap')"
                      filled
                      shaped
                    />
                  </v-col>
                  <!-- 新台幣 輸入框 -->
                  <v-col cols="12" sm="6" md="6" lg="6">
                    <v-text-field
                      name="payNTDollar"
                      ref="payNTDollar"
                      v-model="payNTDollar"
                      v-validate="{
                        required: true,
                        coin_min: 1,
                        is_number: true,
                        insufficient_points_vip: selfLimit / 100
                      }"
                      min="0"
                      :error-messages="errors.first('payNTDollar.payNTDollar')"
                      data-vv-scope="payNTDollar"
                      :label="`${$t('n_t_dollar')}*`"
                      filled
                      @input="validateNTNumber"
                      :suffix="$t('hundred')"
                      shaped
                    />
                  </v-col>
                </v-row>
                <!-- 回饋%數及星幣 -->
                <v-card color="info-card" class="rounded-xl">
                  <v-card-title
                    class="d-flex justify-center align-center gradient-primary-left border-style-title"
                  >
                    <span class="custom-text-noto secondary--text text-h5 font-weight-bold">
                      +{{ selfDiscount }}% {{ $t('rebate') }}
                    </span>
                  </v-card-title>
                  <v-card-text color="secondary--text" class="pa-2 border-style-bottom">
                    <!-- 購買後可獲得 -->
                    <v-row no-gutters justify="center">
                      <span class="text-subtitle-2 default-content--text custom-text-noto">{{
                        $t('can_get')
                      }}</span>
                    </v-row>
                    <!-- 獲得星幣 -->
                    <v-row no-gutters justify="center" align="center">
                      <span class="text-h5 font-weight-bold primary--text custom-text-noto mr-1">
                        <count-animate :start-val="0" :end-val="totalGetXinCoin" :duration="1000" />
                      </span>
                      <span class="text-subtitle-2 default-content--text custom-text-noto">
                        {{ $t('xin_coin') }}
                      </span>
                    </v-row>
                    <!-- XXX + XXX 星幣 -->
                    <v-row no-gutters justify="center">
                      <span class="text-caption default-content--text custom-text-noto"
                        >{{
                          $UIConfig.paymentIndex.xinCoinFormat.format(
                            formatNumber(originalGetXinCoin),
                            $t('xin_coin'),
                            formatNumber(rebateXinCoin),
                            $t('discount'),
                            $t('xin_coin')
                          )
                        }}
                      </span>
                    </v-row>
                  </v-card-text>
                </v-card>
                <v-row no-gutters>
                  <span class="text-body-2 mt-6 mb-3 custom-text-noto">{{
                    $t('vip_discount_text5')
                  }}</span>
                </v-row>
                <!-- 確定按鈕 -->
                <v-row>
                  <v-col>
                    <v-card color="transparent" elevation="0">
                      <v-card-actions class="pa-0">
                        <v-spacer />
                        <v-btn
                          :disabled="disabledConvertNTBtnStatus"
                          class="mx-2 button-content--text"
                          :color="$UIConfig.defaultBtnColor"
                          elevation="0"
                          @click="openAlert"
                        >
                          {{ $t('buy').toUpperCase() }}
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-col>
                </v-row>
              </v-container>
            </v-tab-item>
            <!-- 兌換 -->
            <v-tab-item key="convert" value="convert">
              <v-container fluid :class="['pa-4 pa-sm-6', $UIConfig.paymentIndex.convertClass]">
                <!-- 點數兌換 文字 -->
                <v-row no-gutters>
                  <v-col class="d-flex align-center default-content--text mb-4">
                    <span>
                      {{
                        $UIConfig.paymentIndex.redeemPoints.format(
                          $t('points'),
                          $t('convert'),
                          $t('convert_point')
                        )
                      }}
                    </span>
                  </v-col>
                </v-row>
                <v-row no-gutters class="align-center justify-left">
                  <!-- 目前點數 輸入框 -->
                  <v-col cols="12" sm="6" md="6" lg="6" class="pr-sm-2">
                    <v-text-field
                      v-model="selfPoints"
                      disabled
                      name="current_points"
                      :label="$t('current_points')"
                      filled
                      shaped
                    />
                  </v-col>
                  <!-- 兌換點數 輸入框 -->
                  <v-col cols="12" sm="6" md="6" lg="6" class="pl-sm-2">
                    <v-text-field
                      ref="points"
                      v-model="points"
                      maxlength="9"
                      v-validate="{
                        required: true,
                        coin_min: 1,
                        is_number: true,
                        insufficient_points: selfPoints
                      }"
                      min="0"
                      name="convert_points"
                      :error-messages="errors.first('convert_points.convert_points')"
                      :label="$t('convert_points') + '*'"
                      :data-vv-as="$t('convert_points')"
                      data-vv-scope="convert_points"
                      filled
                      @input="validateNumber"
                      shaped
                    />
                  </v-col>
                </v-row>
                <!-- 預計兌換星幣 -->
                <v-row class="align-center" no-gutters>
                  <v-col class="grey-3--text">
                    {{
                      $UIConfig.paymentIndex.redeemXinCoins.format(
                        $t('expected_xin_coin_conversion'),
                        $t('expected').toUpperCase(),
                        $t('convert').toUpperCase(),
                        $t('xin_coin').toUpperCase()
                      )
                    }}
                    =
                    <count-animate :start-val="0" :end-val="points * 100" :duration="1000" />
                  </v-col>
                </v-row>
                <!-- 確定按鈕 -->
                <v-row no-gutters>
                  <v-col>
                    <v-card color="transparent" elevation="0">
                      <v-card-actions class="pa-0 mt-4">
                        <v-spacer />

                        <v-btn
                          :disabled="disabledConvertBtnStatus"
                          :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                          :color="$UIConfig.defaultBtnColor"
                          elevation="0"
                          @click="doShowConvertConfirmDialog"
                        >
                          {{ $t('sure').toUpperCase() }}
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-col>
                </v-row>
              </v-container>
            </v-tab-item>
            <!-- 保險箱 -->
            <v-tab-item key="coffer" value="coffer">
              <!-- 固定最小高度，切換tab時才不會因為v-if而跳一下 -->
              <div style="min-height: 360px">
                <!-- 加v-if是為了讓data重設 -->
                <coffer v-if="selectType == 'coffer'" @setDisableBtn="setDisableBtn" />
              </div>
            </v-tab-item>
          </v-tabs-items>
        </div>
      </v-card>
      <showTopUpDialog
        :selected-provider="selectedProvider"
        :show-top-up-dialog-status.sync="showTopUpDialogStatus"
        :show-top-up-confirm-dialog-status.sync="showTopUpConfirmDialogStatus"
      />
      <showTopUpConfirmDialog
        :show-top-up-confirm-dialog-status.sync="showTopUpConfirmDialogStatus"
        :show-convert-confirm-dialog-status.sync="showConvertConfirmDialogStatus"
        :show-top-up-success-dialog-status.sync="showTopUpSuccessDialogStatus"
        :stored-type="storedType"
      />
      <showTopUpSuccessDialog
        v-if="showTopUpSuccessDialogStatus"
        :show-top-up-success-dialog-status.sync="showTopUpSuccessDialogStatus"
        :show-top-up-confirm-dialog-status.sync="showTopUpConfirmDialogStatus"
        :show-convert-confirm-dialog-status.sync="showConvertConfirmDialogStatus"
        :stored-type="storedType"
      />
      <showConvertConfirmDialog
        @resetConvertPoints="resetConvertPoints"
        :show-convert-confirm-dialog-status.sync="showConvertConfirmDialogStatus"
      />
      <showVipPayAlertDialog
        v-if="showVipPayAlertDialogStatus"
        :show-vip-pay-alert-dialog-status.sync="showVipPayAlertDialogStatus"
        @doVipPayment="doVipPayment"
      />
      <toBeFormalDialog
        v-if="showTobeFormalDialogStatus"
        @goToGiftPack="goToGiftPack"
        :show-tobe-formal-dialog-status.sync="showTobeFormalDialogStatus"
      />
    </v-dialog>
  </div>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import analytics from '@/mixins/analytics.js'
  import scssLoader from '@/mixins/scssLoader.js'
  import payment from '@/mixins/payment.js'
  const STATION = process.env.STATION
  export default {
    name: 'PaymentIndexDialog',
    mixins: [hiddenScrollHtml, analytics, scssLoader, payment],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      paymentProviderCard: () => import('~/components/payment/paymentProviderCard'),
      giftPackArea: () => import(`~/components_station/${STATION}/payment/giftPackArea`),
      showTopUpDialog: () => import('~/components/payment/showTopUpDialog'),
      showConvertConfirmDialog: () => import('~/components/payment/showConvertConfirmDialog'),
      showTopUpSuccessDialog: () => import('~/components/payment/showTopUpSuccessDialog'),
      showTopUpConfirmDialog: () => import('~/components/payment/showTopUpConfirmDialog'),
      showVipPayAlertDialog: () => import('~/components/payment/showVipPayAlertDialog'),
      coffer: () => import(`~/components_station/${STATION}/payment/coffer`),
      toBeFormalDialog: () => import('~/components/payment/toBeFormalDialog')
    },
    props: {
      showPaymentDialogStatus: { type: Boolean, required: true, default: false }
    },
    data() {
      return {
        showPaymentDialogStatusTmp: this.showPaymentDialogStatus,
        showConvertConfirmDialogStatus: false,
        showTopUpDialogStatus: false,
        showTopUpConfirmDialogStatus: false,
        showTopUpSuccessDialogStatus: false,
        points: '',
        convertBalance: 0,
        selectType: 'yoeGame',
        disabledConvertBtnStatus: true,
        selectedProvider: {},
        successDialogData: {},
        successStoredData: {},
        storedType: '',
        //支付新台幣
        payNTDollar: '',
        disabledConvertNTBtnStatus: true,
        //按下購買後的提示
        showVipPayAlertDialogStatus: false,
        //非正式會員使用保險箱會跳出叫你成為正式會員的dialog
        showTobeFormalDialogStatus: false,
        //當load時不應該可以做其他動作，故將按鈕disable
        disableBtn: false,
        paymentType: 0,
        vipStoreValue: 0
      }
    },
    async created() {
      await this.$store.dispatch('role/updateUserDetail')
      if (this.payVip && this.selfLimit !== 0) {
        this.selectType = 'vip_buy_points'
        this.bankInit()
      }
    },
    computed: {
      paymentProviderList({ $store }) {
        return $store.getters[`${STATION}/payment/list`]
      },
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      selfLevel({ $store }) {
        return $store.getters['role/level']
      },
      selfPoints({ $store }) {
        return $store.getters['role/points']
      },
      selfLimit({ $store }) {
        return $store.getters['role/limit']
      },
      selfDiscount({ $store }) {
        return $store.getters['role/discount']
      },
      //回饋星幣
      rebateXinCoin() {
        return this.originalGetXinCoin * (this.selfDiscount / 100)
      },
      // 原始可拿到星幣
      originalGetXinCoin() {
        return this.payNTDollar * 10000
      },
      //總共可拿到星幣 (原始+回饋)
      totalGetXinCoin() {
        return this.originalGetXinCoin + this.rebateXinCoin
      },
      payVip({ $store }) {
        return $store.getters[`${STATION}/payment/vipState`]
      },
      //此次儲值是否為VIP儲值
      isVipTopUp({ $store }) {
        return $store.getters[`${STATION}/payment/isVipTopUp`]
      },
      vipLevel({ $store }) {
        return $store.getters['role/vipLevel']
      },
      yoeShopArray({ $store }) {
        return $store.getters['yoeShop/yoeShopArray']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      selectType: {
        async handler(newType, oldType) {
          this.$store.commit(`${STATION}/payment/CLEAR_STOREDPOINT`)

          if (newType === 'yoeGame') {
            // 等待 iframe 元素載入完成
            await this.$store.dispatch('yoeShop/fetchYoeGame')
            this.$nextTick(() => {
              const iframe = this.$store.getters['yoeShop/iframe']
              // 進入yoeGame時檢查QUERY_PENDING_PURCHASE
              if (iframe) {
                this.$store.dispatch('yoeShop/sendMessage', {
                  message: {
                    type: this.$store.getters['yoeShop/sendType'].QUERY_PENDING_PURCHASE
                  },
                  sendType: this.$store.getters['yoeShop/sendType'],
                  baseYoeUrl: this.$store.getters['yoeShop/yoeURL']
                })
              }
            })
          }

          if (newType === 'convert') {
            await this.bankInit()
            this.points = this.selfPoints !== 0 ? this.selfPoints : ''
            requestAnimationFrame(() => {
              this.$validator.validate('convert_points.*').then((valid) => {
                this.disabledConvertBtnStatus = !valid
                this.$validator.reset('convert_points')
              })
            })
          } else {
            this.points = 0
          }
          if (newType === 'vip_buy_points') {
            this.payNTDollar = ''
            this.disabledConvertNTBtnStatus = true
          }
          if (newType === 'coffer') {
            if (this.vipLevel === 0) {
              //無解 目前只能用setTimeout強制讓tab跳回去
              //使用nextTick會有畫面閃爍的問題
              //不使用的話tab會直接跑到保險箱
              setTimeout(() => {
                this.selectType = oldType
              }, 1)
              this.showTobeFormalDialogStatus = true
            }
          }
        }
      },
      showPaymentDialogStatus: {
        async handler(status) {
          await this.$store.dispatch('maintain/fetch')
          if (this.maintainSystem[0].maintaining) {
            return
          }
          this.showPaymentDialogStatusTmp = status

          //清除導連url條件: 商城開啟時 && 商城導連url
          if (this.showPaymentDialogStatusTmp === true && this.$route.query.redirect === 'store') {
            this.$nuxt.$emit('root:clearRedirect')
          }
        },
        immediate: true
      },
      points: {
        handler(val) {
          //畫面渲染完再做驗證
          this.$nextTick(() => {
            this.$validator.validate('convert_points.*').then((valid) => {
              if (valid) {
                this.$store.commit(`${STATION}/payment/CLEAR_STOREDPOINT`)
                this.$store.commit(`${STATION}/payment/SET_STOREDPOINT`, parseInt(val))
                this.disabledConvertBtnStatus = false
              } else {
                this.$store.commit(`${STATION}/payment/CLEAR_STOREDPOINT`)
                this.disabledConvertBtnStatus = true
              }
            })
          })
        }
      },
      payNTDollar: {
        handler() {
          //畫面渲染完再做驗證
          this.$nextTick(() => {
            this.$validator.validate('payNTDollar.*').then((valid) => {
              if (valid) {
                this.disabledConvertNTBtnStatus = false
              } else {
                this.disabledConvertNTBtnStatus = true
              }
            })
          })
        }
      }
    },
    async mounted() {
      // 先移除監聽再新增監聽，避免重複監聽
      this.$wsClient.receivedListeners.remove(this.handleStoreRecived)
      this.$wsClient.receivedListeners.add(this.handleStoreRecived)
      this.$store.dispatch('yoeShop/fetchYoeGame')
    },
    methods: {
      resetConvertPoints() {
        this.points = 0
        this.$validator.reset('convert_points')
      },
      doShowConvertConfirmDialog() {
        this.showConvertConfirmDialogStatus = true
      },
      formatPrice(value) {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      },
      closeConvertFinalDialog() {
        this.showConvertConfirmDialogStatus = false
      },
      async closeDialog() {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) {
          return
        }

        this.bankOut()
        this.$wsClient.receivedListeners.remove(this.handleStoreRecived)
        this.$store.commit(`${STATION}/payment/CLEAR_STOREDPOINT`)
        this.showPaymentDialogStatusTmp = false
        this.disabledConvertBtnStatus = true
        this.$emit('update:showPaymentDialogStatus', false)
      },
      storedFinal(point, storedPoint) {
        if (storedPoint !== 0) {
          this.showTopUpConfirmDialogStatus = false
          //判斷此次是否為vip儲值
          if (!this.isVipTopUp) {
            this.$store.commit('role/SET_POINTS', this.selfPoints + storedPoint)
            this.$store.commit(`${STATION}/payment/SET_STOREDPOINT`, storedPoint)
            this.$store.commit(`${STATION}/payment/SET_CURRENTPOINT`, storedPoint)
            this.showTopUpSuccessDialogStatus = true
            this.$notify.success(this.$t('stored_value') + this.$t('success'))
          } else {
            this.$nuxt.$emit('root:showVipTopUpSuccessDialogStatus', true)
            this.$store.dispatch('role/updateUserDetail')
            this.$notify.success(this.$t('stored_value') + this.$t('success'))
          }
          this.paymentAnalyticsToPlatform({
            username: this.$store.getters['role/userName'],
            type: this.paymentType,
            point: this.paymentType === 6 ? this.vipStoreValue : storedPoint
          })
        } else {
          this.$notify.error(this.$t('system_busy'))
        }
      },
      async showPaymentMethod(paymentProviderId, providerInfo) {
        this.$store.commit(`${STATION}/payment/SET_VIP_TOP_UP`, false)
        this.selectedProvider = providerInfo
        let getLink = ''
        let analyticsStatus = false

        switch (paymentProviderId) {
          case 1:
          case 2:
          case 3:
            this.showTopUpDialogStatus = true
            break
          case 4:
            {
              await this.bankInit()
              this.$wsClient.send(this.$wsPacketFactory.paymentLinkForLocal())
              getLink = await this.$xinUtility.waitEvent(
                this.$wsClient.receivedListeners,
                (data) => {
                  return data.isFeature(this.$xinConfig.FEATURE.BANK.TYPE.PAYMENT)
                }
              )
            }
            break
          case 5:
            {
              await this.bankInit()
              this.$wsClient.send(this.$wsPacketFactory.paymentLinkForOversea())
              getLink = await this.$xinUtility.waitEvent(
                this.$wsClient.receivedListeners,
                (data) => {
                  return data.isFeature(this.$xinConfig.FEATURE.BANK.TYPE.PAYMENT)
                }
              )
            }
            break
          default:
        }

        if (getLink !== '') {
          this.$lineOpenWindow.open(getLink.url, '_blank')
          analyticsStatus = true
        }

        if (analyticsStatus) {
          let label = ''
          switch (paymentProviderId) {
            case 4:
              label = 'Webcreditcard'
              break
            case 5:
              label = 'Weboverseas'
              break

            default:
              break
          }
          this.paymentClickAnalytics(label)
        }
        this.paymentType = paymentProviderId
      },

      handleStoreRecived(data) {
        this.storedType = 'NORMAL'
        const setBeginner = () => {
          this.$store.commit('role/SET_LEVEL', 1)
          this.$store.commit('role/SET_VIPLEVEL', 1)
          this.$store.commit('role/SET_BALANCE', 0)
          this.storedType = 'BEGINNER'
        }
        if (data.storeType === this.$xinConfig.STORE_POINT_TYPE.BEGINNER && !this.isVipTopUp) {
          //首儲更新玩家資料
          if (this.selfLevel === 0) {
            setBeginner()
          }
          this.storedFinal(data.point, data.storedPoint)
        } else if (data.storeType === this.$xinConfig.STORE_POINT_TYPE.NORMAL && !this.isVipTopUp) {
          this.storedFinal(data.point, data.storedPoint)
        }
        // VIP購點
        else if (data.isFeature(this.$xinConfig.FEATURE.BANK.TYPE.VIP_POINT) && this.isVipTopUp) {
          this.storedFinal(data.point, data.storedPoint)
        } else if (data.isGash && data.isSuccess) {
          if (this.selfLevel === 0) {
            //首儲更新玩家資料
            setBeginner()
          }
          this.storedFinal(data.point, data.storedPoint)
        }
      },
      // 防止輸入數字以外的字元
      validateNumber() {
        this.points = this.points.replace(/[^0-9]/g, '')
        if (this.points[0] === '0') {
          this.payNTDollar = this.payNTDollar.slice(1)
        }
        this.$refs.points.lazyValue = this.points
      },
      validateNTNumber() {
        this.payNTDollar = this.payNTDollar.replace(/[^0-9]/g, '')
        if (this.payNTDollar[0] === '0') {
          this.payNTDollar = this.payNTDollar.slice(1)
        }
        if (this.payNTDollar.length > 6) {
          this.payNTDollar = this.payNTDollar.slice(0, -1)
        }
        this.$refs.payNTDollar.lazyValue = this.payNTDollar
      },
      //vip 購點
      async doVipPayment() {
        await this.bankInit()
        this.$store.commit(`${STATION}/payment/SET_VIP_TOP_UP`, true)
        this.$wsClient.send(this.$wsPacketFactory.paymentLinkForVip(this.payNTDollar * 100))
        let getLink = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          return data.isFeature(this.$xinConfig.FEATURE.BANK.TYPE.PAYMENT)
        })
        if (getLink !== '') {
          if (getLink.isSuccess) {
            this.$lineOpenWindow.open(getLink.url, '_blank')
          } else {
            this.$notify.warning(getLink.message)
          }
        }
      },
      openAlert() {
        this.showVipPayAlertDialogStatus = true
        this.paymentType = 6
        this.vipStoreValue = this.payNTDollar * 100
      },
      //千分位
      formatNumber(num) {
        return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
      },
      goToGiftPack() {
        this.selectType = 'yoeGame'
      },
      setDisableBtn(state) {
        this.disableBtn = state
      }
    },
    beforeDestroy() {
      this.bankOut()
      this.$store.commit(`${STATION}/payment/CLEAR_STOREDPOINT`)
      this.$wsClient.receivedListeners.remove(this.handleStoreRecived)
      this.showPaymentDialogStatusTmp = false
      this.disabledConvertBtnStatus = true
    }
  }
</script>

<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $primary: map-get($colors, 'primary');
  .border-style-title {
    border-left: solid 1px $primary-variant-3 !important;
    border-right: solid 1px $primary-variant-3 !important;
    border-top: solid 1px $primary-variant-3 !important;
  }
  .border-style-bottom {
    border-left: solid 1px $primary-variant-3 !important;
    border-right: solid 1px $primary-variant-3 !important;
    border-bottom: solid 1px $primary-variant-3 !important;
  }

  .v-tabs-items {
    background-color: transparent !important;
  }
  .a-link {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  .v-tab--active {
    color: $primary !important;
  }
  .gift-pack-remark {
    display: block;
    font-size: 14px;
    width: 100%;
    color: #e9b950;
    text-align: center;
  }
  .no-purchase-items {
    text-align: center;
    span {
      font-size: 12px;
      color: #ffffff4d;
    }
  }
  .yoeGame-swiper::v-deep {
    .swiper-item {
      margin-right: 0px !important;

      .swiper-slide:last-child {
        margin-right: 0 !important;
      }
    }
  }
  #paymentDialog {
    .scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    .scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      .scrollable-sm {
        max-height: calc(90svh - 52px);
      }
      .scrollable-xs {
        max-height: calc(100svh - 52px);
      }
    }
  }
</style>
