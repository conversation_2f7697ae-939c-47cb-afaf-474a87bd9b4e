<template>
  <div>
    <v-dialog
      v-model="showConvertConfirmDialogStatusTmp"
      persistent
      max-width="400px"
      content-class="rounded-lg"
    >
      <v-card elevation="0" tile color="dialog-fill" class="pa-4 pa-sm-6">
        <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text pa-0">
          {{ $t('reminder') }}
        </v-card-title>
        <v-card-text class="default-content--text py-6 px-0" v-html="message" />
        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
              <v-btn
                elevation="0"
                :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                text
                @click="closeDialog"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                @click="doConvert"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
  const STATION = process.env.STATION
  export default {
    name: 'ShowConvertConfirmDialog',
    props: {
      showConvertConfirmDialogStatus: { type: Boolean, required: true, default: false }
    },
    data() {
      return {
        showConvertConfirmDialogStatusTmp: this.showConvertConfirmDialogStatus,
        convertBalance: 0,
        message: ''
      }
    },
    computed: {
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      storedPoint({ $store }) {
        return $store.getters[`${STATION}/payment/storedPoint`]
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showConvertConfirmDialogStatus: {
        handler(status) {
          if (this.maintainSystem[0].maintaining) {
            return
          }
          this.showConvertConfirmDialogStatusTmp = status
        }
      },
      storedPoint: {
        handler(val) {
          let convertBalance = val * 100
          const message =
            this.$t('convert_points_noty1', {
              points: parseInt(val),
              amount: this.formatPrice(convertBalance)
            }) +
            '<br />' +
            this.$t('convert_points_noty2')
          this.message = message
        }
      }
    },
    methods: {
      formatPrice(value) {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      },
      closeDialog() {
        this.showConvertConfirmDialogStatusTmp = false
        this.$emit('update:showConvertConfirmDialogStatus', false)
      },
      async doConvert() {
        try {
          let res = []
          this.$wsClient.send(this.$wsPacketFactory.exchangePoint(this.storedPoint))
          res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.BANK.TYPE.EXCHANGE_POINT)
          })
          if (Object.prototype.hasOwnProperty.call(res, 'exchangedPoint')) {
            this.$store.commit('role/SET_POINTS', res.point)
            this.$store.commit('role/SET_BALANCE', res.money)
            this.$notify.success(this.$t('convert') + this.$t('success'))
            this.$store.commit(`${STATION}/payment/SET_STOREDPOINT`, 0)
            this.$emit('resetConvertPoints')
          } else if (Object.prototype.hasOwnProperty.call(res, 'code')) {
            const response = res.message.replace('-', '')
            this.$notify.info(response)
          } else {
            this.$notify.error(this.$t('exchange_failed_content'))
          }
        } catch (error) {
          console.log(error)
        }
        this.$emit('update:showConvertConfirmDialogStatus', false)
        this.$validator.reset('convert_points')
        // 出現畫面尚未消失，但額度先被歸零，所以加上setTimeout
        setTimeout(() => {
          this.$store.commit(`${STATION}/payment/CLEAR_STOREDPOINT`)
        }, 1000)
      }
    }
  }
</script>
