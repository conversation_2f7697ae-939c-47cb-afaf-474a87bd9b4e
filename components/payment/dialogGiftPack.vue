<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog
    fullscreen
    max-width="620px"
    persistent
    v-model="showDialogStatus"
    content-class="gift-pack-dialog-background"
  >
    <div class="ad-gift-pack" v-show="showGiftPackContent">
      <div class="gift-pack-info" :class="giftPackClass">
        <!--關閉按鈕-->
        <v-img
          :src="getImage(adGiftPacks[0].closeImage)"
          class="close-btn"
          @click="closeDialog"
          @error="errorStickerHandler"
        />
        <!--主視覺-->
        <v-img
          :src="
            getImage(
              $vuetify.breakpoint.xsOnly
                ? adGiftPacks[0].mainContent.xsImage
                : adGiftPacks[0].mainContent.image
            )
          "
          class="main-image"
          contain
          eager
          @error="errorStickerHandler"
        />
        <!--階級按鈕-->
        <v-img
          v-if="adGiftPacks[0].infoImage"
          :src="getImage(adGiftPacks[0].infoImage)"
          class="info-image"
          @error="errorStickerHandler"
          @click="openVipLevelDesc"
        />
        <!--購買按鈕-->
        <v-img
          :src="getImage(adGiftPacks[0].purchaseImage)"
          class="purchase-image"
          @error="errorStickerHandler"
          contain
          @click="showPaymentMethod(adGiftPacks[0])"
        />
      </div>
    </div>
    <!-- VIP階級說明視窗 -->
    <vipLevelDescDialog
      v-if="showVipLevelDescDialogStatus"
      :show-member-level-desc-dialog-status.sync="showVipLevelDescDialogStatus"
      :vip-level="userVipLevel"
    />
    <!-- 確認購買對話框 -->
    <yoeConfirmDialog v-model="showConfirmDialog" :action="handleConfirm">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <div class="d-flex flex-wrap">
        <span class="default-content--text mb-2">{{ $t('yoeGameConfirmNoty_head') }}</span>
        <i18n path="yoeGameConfirmNoty_content" tag="span" class="primary--text">
          <template v-slot:content>{{
            isCashBackReceive
              ? $t('yoeGameConfirmNoty_content_rebate')
              : $t('yoeGameConfirmNoty_content_app')
          }}</template>
        </i18n>
        <span v-if="isAppReceive || isCashBackReceive" class="primary--text">
          {{ $t('yoeGameConfirmNoty_appReceive') }}
        </span>
      </div>
    </yoeConfirmDialog>
    <!-- 取消購買 -->
    <yoeConfirmDialog v-model="showCancelDialog" :show-cancel="false" :action="handleCancelConfirm">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <span class="default-content--text">{{ $t('cancelYoeShopNoty') }}</span>
    </yoeConfirmDialog>
    <!--成功-->
    <yoeConfirmDialog v-model="showSuccessDialog" :show-cancel="false" :action="closeDialog">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <div class="d-flex align-center">
        <span class="default-content--text">{{ $t('yoeShopSuccessNoty') }}</span>
      </div>
    </yoeConfirmDialog>
    <!--成功但沒收到訂單-->
    <yoeConfirmDialog v-model="showPurchaseCancelDialog" :show-cancel="false" :action="closeDialog">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <span class="default-content--text">{{
        $t('successButPurchaseFalseNoty').split('\n')[0]
      }}</span
      ><br />
      <span class="default-content--text">
        {{ $t('successButPurchaseFalseNoty').split('\n')[1] }}
      </span>
    </yoeConfirmDialog>
    <!--error code 18-->
    <yoeConfirmDialog v-model="showErrorDialog" :show-cancel="false" :action="handleConfirmError">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <span class="default-content--text">{{ errorMessage }}</span>
    </yoeConfirmDialog>
  </v-dialog>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import analytics from '@/mixins/analytics.js'

  export default {
    name: 'dialogGiftPack',
    mixins: [analytics, hiddenScrollHtml],
    components: {
      vipLevelDescDialog: () => import('~/components/player_info/vipLevelDescDialog.vue'),
      yoeConfirmDialog: () => import('~/components/payment/yoeConfirmDialog')
    },
    props: {
      showDialogGiftPackStatus: {
        type: Boolean,
        required: true
      }
    },
    data() {
      return {
        imageLoadError: false,
        showVipLevelDescDialogStatus: false,
        showConfirmDialog: false,
        showCancelDialog: false,
        showSuccessDialog: false,
        showPurchaseCancelDialog: false,
        showErrorDialog: false,
        errorMessage: '',
        isProcessingOrder: false
      }
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      showDialogStatus: {
        get() {
          return this.showDialogGiftPackStatus
        },
        set(value) {
          this.$emit('update:showDialogGiftPackStatus', value)
        }
      },
      adGiftPacks() {
        return this.$store.getters['yoeShop/adGiftPack']
      },
      remainingBalance({ $store }) {
        return $store.getters['yoeShop/remainingBalance']
      },
      imageUrlPath({ $store }) {
        return $store.getters['image/dialogGiftPackPath']
      },
      shopImage() {
        return (url) => {
          const imageName = this.imageLoadingError ? url.slice(0, -5) + '.png' : url
          const imagePath = this.imageUrlPath + '/' + imageName
          console.log(imagePath)
          return imagePath
        }
      },
      userVipLevel() {
        return this.$store.getters['role/vipLevel']
      },
      showGiftPackContent() {
        return (
          this.adGiftPacks.length > 0 && !(this.showSuccessDialog || this.showPurchaseCancelDialog)
        )
      },
      giftPackClass() {
        if (!this.adGiftPacks.length) return ''
        const adName = this.adGiftPacks[0].adName
        if (adName === '30p_newbie' || adName === '70p_newbie') {
          return 'new-gift-pack'
        }
        if (adName === '100p_newbie') {
          return 'supply-gift-pack'
        }

        return ''
      },
      // 內含僅可APP使用物品確認窗
      isAppUsage() {
        return this.adGiftPacks[0].confirmType === 'appUsage'
      },
      // 內含僅可APP領取物品確認窗
      isCashBackReceive() {
        return this.adGiftPacks[0].confirmType === 'cashBackReceive'
      },
      isAppReceive() {
        return this.adGiftPacks[0].confirmType === 'appReceive'
      }
    },
    mounted() {
      this.$nuxt.$on('yoe:dialogStatusChange', this.handleYoeDialogStatusChange)
      this.$nuxt.$on('yoe:showYoeDialogStatus', this.handleYoeDialogStatusChange)
      this.$nuxt.$on('yoe:showErrorDialog', this.handleErrorDialog)
    },
    beforeDestroy() {
      this.$nuxt.$off('yoe:dialogStatusChange', this.handleYoeDialogStatusChange)
      this.$nuxt.$off('yoe:showYoeDialogStatus', this.handleYoeDialogStatusChange)
      this.$nuxt.$off('yoe:showErrorDialog', this.handleErrorDialog)
    },
    methods: {
      errorStickerHandler() {
        this.imageLoadingError = true
      },
      getImage(pictureName) {
        if (!pictureName) return ''
        return this.shopImage(pictureName)
      },
      async showPaymentMethod(giftPack) {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        if (giftPack.maintained) return

        // 如果在購買中，禁用購買按鈕
        if (this.isProcessingOrder) return

        if (giftPack.confirmType) {
          this.showConfirmDialog = true
        } else {
          this.handleConfirm()
        }
      },
      handleConfirm() {
        this.isProcessingOrder = true
        const giftPack = this.adGiftPacks[0]
        this.$store.commit('yoeShop/SET_PRODUCT_INFO', giftPack)
        this.$nuxt.$emit('root:showYoeDialogStatus', {
          show: true,
          shopItem: giftPack,
          cancel: false,
          purChaseCancel: false
        })
      },
      async handleYoeDialogStatusChange(value) {
        // 維持原有的維護檢查邏輯
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) {
          return
        }
        // 維持原有的狀態處理邏輯
        if (value.cancel) {
          this.showCancelDialog = true
        }
        if (!value.cancel) {
          await this.$store.dispatch('yoeShop/fetchYoeGame')
          this.showSuccessDialog = true
        }
        if (!value.cancel && value.purChaseCancel) {
          this.showPurchaseCancelDialog = true
        }
      },
      handleCancelConfirm() {
        this.showCancelDialog = false
        // 結束購買中狀態
        this.isProcessingOrder = false
      },
      handleErrorDialog(error) {
        if (error.errorCode === 18) {
          this.errorMessage = error.message
          this.showErrorDialog = true
        }
      },
      async handleConfirmError() {
        await this.$store.dispatch('yoeShop/fetchYoeGame')

        const iframe = this.$store.getters['yoeShop/iframe']
        if (iframe) {
          this.sendMessage({
            type: this.sendType.QUERY_PENDING_PURCHASE
          })
        }
        this.closeDialog()
      },
      openVipLevelDesc() {
        this.showVipLevelDescDialogStatus = true
      },
      closeDialog() {
        // 結束購買中狀態
        this.isProcessingOrder = false
        this.$emit('update:showDialogGiftPackStatus', false)
      }
    }
  }
</script>
<style lang="scss" scoped>
  ::v-deep .v-dialog {
    background: none;
  }
  .ad-gift-pack {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #21212175;
    overflow: hidden;
    .gift-pack-info {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      &.new-gift-pack {
        .main-image {
          width: 57vw;
          height: calc(75vw * 0.5) !important;
        }
        .close-btn {
          position: absolute;
          width: 9.52%;
          top: 3.95%;
          right: -5.38%;
          cursor: pointer;
          z-index: 1;
        }
        .info-image {
          width: 19%;
          position: absolute;
          left: 26%;
          top: 6%;
          cursor: pointer;
          transform-origin: center center;
        }
        .purchase-image {
          width: 45%;
          position: absolute;
          left: 50%;
          bottom: 5.35%;
          transform: translateX(-50%);
          cursor: pointer;
          transform-origin: center center;
        }
      }
      &.supply-gift-pack {
        .main-image {
          width: 57vw;
          height: calc(75vw * 0.5) !important;
        }
        .close-btn {
          position: absolute;
          width: 8.52%;
          top: 3.95%;
          right: 4.62%;
          cursor: pointer;
          z-index: 1;
        }
        .info-image {
          width: 19%;
          position: absolute;
          left: 26%;
          top: 6%;
          cursor: pointer;
          transform-origin: center center;
        }
        .purchase-image {
          width: 44%;
          position: absolute;
          left: 46%;
          bottom: 4.5%;
          transform: translateX(-50%);
          cursor: pointer;
          transform-origin: center center;
        }
      }
    }
  }
  @media screen and (max-width: 599px) {
    .ad-gift-pack {
      .gift-pack-info {
        &.new-gift-pack {
          .main-image {
            width: 75vw;
            height: calc(75vw * 1.6666) !important;
          }
          .close-btn {
            width: 12.32%;
            right: 0;
            top: 0%;
          }
          .info-image {
            width: 31%;
            left: 6.59%;
            top: 6.92%;
          }
          .purchase-image {
            width: 75.9%;
            left: 50%;
            bottom: 3.76%;
          }
        }
        &.supply-gift-pack {
          .main-image {
            width: 75vw;
            height: calc(75vw * 1.6666) !important;
          }
          .close-btn {
            width: 12.32%;
            right: 0;
            top: 0%;
          }
          .purchase-image {
            width: 75.9%;
            left: 50%;
            bottom: 1.76%;
          }
        }
      }
    }
  }
</style>
