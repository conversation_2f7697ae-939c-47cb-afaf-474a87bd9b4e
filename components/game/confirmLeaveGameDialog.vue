<template>
  <div>
    <!-- 確認要離開 ?-->
    <v-dialog
      v-model="showConfirmBackGameDialog"
      persistent
      max-width="400px"
      content-class="rounded-lg"
    >
      <v-card elevation="0" tile color="dialog-fill" class="pa-4 pa-sm-6">
        <v-card-title
          class="d-flex justify-center align-center text-h6 font-weight-regular custom-text-noto grey-1--text pa-0"
        >
          {{ $t('hint') }}
        </v-card-title>
        <v-card-text class="default-content--text text-body-2 custom-text-noto px-0 py-6">
          <span>{{ notyText }}</span>
        </v-card-text>
        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
              ><v-btn
                :class="['text-button', breakpoint.xsOnly ? 'w-100' : '']"
                elevation="0"
                text
                @click="closeConfirmDialog"
              >
                {{ $t('cancel') }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                :class="['button-content--text text-button', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                @click="confirmLeaveGame"
              >
                {{ $t('sure') }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
  export default {
    name: 'confirmLeaveGameDialog',
    props: {
      showConfirmBackGameDialog: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        randomNum: 0,
        notyText: ''
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {},
    mounted() {
      this.rearrangeNotyText()
    },
    destroyed() {},
    methods: {
      closeConfirmDialog() {
        this.$emit('update:showConfirmBackGameDialog', false)
      },
      rearrangeNotyText() {
        this.randomNum = Math.floor(Math.random() * 5) + 7
        this.notyText = this.$t('confirmLeaveGameNoty' + this.randomNum)
      },
      confirmLeaveGame() {
        this.$emit('backToParent')
      }
    }
  }
</script>
