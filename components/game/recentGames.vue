<template>
  <v-container fluid class="pa-0">
    <!-- recent games -->
    <v-row v-if="acceptCookiePolicy && propGameList.length > 0" no-gutters>
      <v-col cols="12" :class="{ 'notch-right': hasRightNotch }">
        <v-row no-gutters align="center">
          <span class="material-symbols-outlined gradient-primary--text mail-button-icon-size">
            history
          </span>
          <span class="font-weight-bold gradient-default--text custom-text-noto ml-2 mr-4 mb-1">
            {{ $t('recent_games') }}
          </span>
          <gradientDivider />
        </v-row>
      </v-col>
      <swiper-custom
        class="mt-4"
        :game-list="propGameList"
        :swiper-slide-style="swiperSlideStyle"
        :show-slide-btn="showSlideBtn"
        :btn-position="btnPosition"
      >
        <template v-slot:card="data">
          <gameCard :game="data.game" :v-card-margin-x="'mx-0'" />
        </template>
      </swiper-custom>
    </v-row>
  </v-container>
</template>
<script>
  import gameRelate from '@/mixins/gameRelate.js'
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'RecentGames',
    mixins: [gameRelate, orientation],
    components: {
      gradientDivider: () => import('~/components/gradientDivider.vue'),
      swiperCustom: () => import('~/components/swiperCustom.vue'),
      gameCard: () => import('~/components/game/gameCard.vue')
    },
    props: {
      propGameList: {
        type: Array,
        required: true
      }
    },
    data() {
      return {
        acceptCookiePolicy: true,
        // 是否顯示左右按鈕
        showNavigationButtons: true,
        // 左右按鈕的位置
        btnPosition: {
          left: '-15px',
          right: '-15px',
          top: '40%'
        }
      }
    },
    computed: {
      swiperSlideStyle() {
        const breakWidth = this.$vuetify.breakpoint.width
        const width =
          breakWidth >= 1264
            ? this.$UIConfig.swiperBox.gameCardWidth.lg
            : breakWidth >= 960
            ? this.$UIConfig.swiperBox.gameCardWidth.md
            : breakWidth >= 600
            ? this.$UIConfig.swiperBox.gameCardWidth.sm
            : this.$UIConfig.swiperBox.gameCardWidth.xs
        const body = { boxSizing: 'border-box', width }

        return body
      },
      showSlideBtn() {
        const seriesListLength = this.propGameList.length
        return (
          this.showNavigationButtons &&
          this.$vuetify.breakpoint.lgAndUp &&
          // 卡片張數過少時不顯示左右滑動按鈕
          (this.$vuetify.breakpoint.width <= 1270 ? seriesListLength > 4 : seriesListLength > 6)
        )
      }
    },
    async created() {
      this.$nuxt.$on('game:acceptCookiePolicyMessage', this.setupAcceptCookiePolicy)
    },
    mounted() {
      this.acceptCookiePolicy = !!(
        this.$localStorage.get('accept_cookie_policy').expired &&
        this.$moment(this.$localStorage.get('accept_cookie_policy').expired).isAfter(this.$moment())
      )
    },
    beforeDestroy() {
      this.$nuxt.$off('game:acceptCookiePolicyMessage', this.setupAcceptCookiePolicy)
      this.$store.commit('gameHall/SET_ALL_GAME_LIST', [])
    },
    methods: {
      setupAcceptCookiePolicy(cookiePolicy) {
        this.acceptCookiePolicy = cookiePolicy
      }
    }
  }
</script>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
