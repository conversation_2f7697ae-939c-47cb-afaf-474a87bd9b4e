<template>
  <v-dialog
    v-model="showGameCardConfirmDialogStatusTmp.show"
    persistent
    max-width="400"
    content-class="rounded-lg"
  >
    <v-card color="transparent">
      <customDialogTitle :title="$t('disclaimer').toUpperCase()" @closeDialog="onCancel" />
      <v-card-text class="pa-6">
        <div class="pb-2">
          <span class="custom-text-noto default-content--text text-subtitle-1">
            {{ $t('game_live_dialog_noty') }}
          </span>
        </div>
        <div>
          <span class="custom-text-noto default-content--text text-body-2">
            {{
              getLinkText(
                $t('game_live_dialog_noty2', { brandName: $t('xincity') }),
                $t('game_live_dialog_noty3')
              )[0]
            }}
            <span class="warning--text">
              {{
                getLinkText(
                  $t('game_live_dialog_noty2', { brandName: $t('xincity') }),
                  $t('game_live_dialog_noty3')
                )[1]
              }}
            </span>
            {{
              getLinkText(
                $t('game_live_dialog_noty2', { brandName: $t('xincity') }),
                $t('game_live_dialog_noty3')
              )[2]
            }}
          </span>
        </div>
        <div
          v-if="!showGameCardConfirmDialogStatus.hasExp && showGameCardConfirmDialogStatus.hasRobot"
          class="mt-6"
        >
          <span class="custom-text-noto success--text text-body-2">
            ※{{ $t('game_both_warnings') }}
            <!-- 顯示兩者條件的提示 -->
          </span>
        </div>
        <div v-else-if="!showGameCardConfirmDialogStatus.hasExp" class="mt-6">
          <span class="custom-text-noto success--text text-body-2">
            ※{{ $t('game_no_experience_accumulation_warning') }}
            <!-- 只有 hasExp 的提示 -->
          </span>
        </div>
        <div v-else-if="showGameCardConfirmDialogStatus.hasRobot" class="mt-6">
          <span class="custom-text-noto success--text text-body-2">
            ※{{ $t('game_has_robot_warning') }}
            <!-- 只有 hasRobot 的提示 -->
          </span>
        </div>
        <div class="d-flex justify-center">
          <v-checkbox
            class="pa-0 mt-6 mb-3"
            dense
            v-model="todayDontDisplay"
            :label="`${$t('today_dont_display')}`"
          ></v-checkbox>
        </div>

        <v-btn
          class="button-content--text"
          :color="$UIConfig.defaultBtnColor"
          elevation="0"
          block
          @click="onConfirm"
        >
          {{ $t('start_playing').toUpperCase() }}
        </v-btn>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'gameCardConfirmDialog',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    props: {
      showGameCardConfirmDialogStatus: {
        type: Object,
        default: () => ({
          show: false,
          hasExp: false,
          hasRobot: false,
          onConfirmNotify: () => {},
          onCancelNotify: () => {}
        })
      }
    },
    data() {
      return {
        showGameCardConfirmDialogStatusTmp: this.showGameCardConfirmDialogStatus,
        todayDontDisplay: false
      }
    },
    watch: {
      showGameCardConfirmDialogStatus: function (val) {
        this.showGameCardConfirmDialogStatusTmp = val
      }
    },

    methods: {
      getLinkText(completeText, linkText) {
        const aryText = completeText.split(linkText)
        return [aryText[0], linkText, aryText[1]]
      },
      closeDialog() {
        this.$emit('update:showGameCardConfirmDialogStatus', {
          show: false,
          hasExp: false,
          hasRobot: false,
          onConfirmNotify: () => {},
          onCancelNotify: () => {}
        })
      },
      onConfirm() {
        this.showGameCardConfirmDialogStatusTmp.onConfirmNotify?.()
        if (this.todayDontDisplay) {
          //明天失效
          let today = this.$moment()
          this.$localStorage.set('localStorageLiveConfirm', { expired: today })
        }
        this.closeDialog()
      },
      onCancel() {
        this.showGameCardConfirmDialogStatusTmp.onCancelNotify?.()
        this.closeDialog()
      }
    }
  }
</script>
