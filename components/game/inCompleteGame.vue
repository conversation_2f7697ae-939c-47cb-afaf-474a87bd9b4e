<template>
  <v-container fluid class="pa-0">
    <v-row v-if="propGameList.length > 0 && isLogin" no-gutters>
      <v-col cols="12" :class="{ 'notch-right': hasRightNotch }">
        <v-row no-gutters align="center">
          <span class="font-weight-bold gradient-default--text custom-text-noto my-1 mr-2">
            {{ $t('unfinished_games') }}
          </span>
          <span
            :class="['cursor-pointer', 'material-symbols-outlined']"
            class="grey-3--text material-icons md-20 mr-2"
            @click="showConfirmDialog = true"
          >
            info
          </span>
          <gradientDivider />
        </v-row>
      </v-col>
      <swiper-custom
        class="mt-4"
        :game-list="propGameList"
        :swiper-slide-style="swiperSlideStyle"
        :show-slide-btn="showSlideBtn"
        :btn-position="btnPosition"
      >
        <template v-slot:card="data">
          <gameCard :game="data.game" :v-card-margin-x="'mx-0'" />
        </template>
      </swiper-custom>
    </v-row>
    <confirmDialog
      v-if="showConfirmDialog"
      v-model="showConfirmDialog"
      dialog-width="380"
      :show-cancel="false"
    >
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <span class="default-content--text accept-word-break">{{
        $t('unfinished_games_exit_dialog')
      }}</span>
    </confirmDialog>
  </v-container>
</template>
<script>
  import gameRelate from '@/mixins/gameRelate.js'
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'InCompleteGame',
    mixins: [gameRelate, orientation],
    components: {
      gradientDivider: () => import('~/components/gradientDivider.vue'),
      swiperCustom: () => import('~/components/swiperCustom.vue'),
      gameCard: () => import('~/components/game/gameCard.vue'),
      confirmDialog: () => import('~/components/confirmDialog.vue')
    },
    props: {
      propGameList: {
        type: Array,
        required: true
      }
    },
    data() {
      return {
        // 是否顯示左右按鈕
        showNavigationButtons: true,
        // 左右按鈕的位置
        btnPosition: {
          left: '-15px',
          right: '-15px',
          top: '40%'
        },
        showConfirmDialog: false
      }
    },
    computed: {
      swiperSlideStyle() {
        const breakWidth = this.$vuetify.breakpoint.width
        const width =
          breakWidth >= 1264
            ? this.$UIConfig.swiperBox.gameCardWidth.lg
            : breakWidth >= 960
            ? this.$UIConfig.swiperBox.gameCardWidth.md
            : breakWidth >= 600
            ? this.$UIConfig.swiperBox.gameCardWidth.sm
            : this.$UIConfig.swiperBox.gameCardWidth.xs
        const body = { boxSizing: 'border-box', width }

        return body
      },
      showSlideBtn() {
        const seriesListLength = this.propGameList.length
        return (
          this.showNavigationButtons &&
          this.$vuetify.breakpoint.lgAndUp &&
          // 卡片張數過少時不顯示左右滑動按鈕
          (this.$vuetify.breakpoint.width <= 1270 ? seriesListLength > 4 : seriesListLength > 6)
        )
      },
      isLogin() {
        return this.$store.getters['role/isLogin']
      }
    },

    beforeDestroy() {
      this.$store.commit('gameHall/SET_ALL_GAME_LIST', [])
    }
  }
</script>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
