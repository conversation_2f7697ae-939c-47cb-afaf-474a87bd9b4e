<template>
  <v-container fluid v-show="!maintainSystem[0].maintaining && !status404">
    <!-- title -->
    <v-row align="center" justify="center">
      <div class="pt-2 pt-md-0 pt-lg-0 pt-xl-0 pb-6">
        <linearGradientTitle
          :title="$t(currentCategory(selectedGameCategory).dict + '_game').toUpperCase()"
        />
      </div>
    </v-row>
    <!-- game list card -->

    <v-row justify="end" class="pt-6 align-top px-2" :class="[$UIConfig.gamePage.background]">
      <!-- filter -->
      <v-col cols="12" sm="4" md="3" lg="3" xl="2" class="py-0">
        <v-select
          v-model="gameSortType"
          :label="$t('sort_label')"
          rounded
          outlined
          dense
          height="40px"
          :items="gameHallSortList"
          item-value="sortType"
          item-text="name"
          :hide-details="$vuetify.breakpoint.xsOnly"
          @change="selectedGameSortEvent"
        />
      </v-col>
      <!-- 上錯程式到正式站，先將該段隱藏 -->
      <!-- <v-col
        cols="12"
        sm="4"
        md="3"
        lg="3"
        xl="2"
        class="py-0 mt-xl-0 mt-lg-0 mt-md-0 mt-sm-0 mt-4"
      >
        <v-select
          v-model="providerId"
          :label="$t('provider_label')"
          rounded
          outlined
          dense
          height="40px"
          :items="gameProviders"
          item-value="id"
          item-text="fullname"
          :hide-details="$vuetify.breakpoint.xsOnly"
          @change="selectedProviderSortEvent"
        />
      </v-col> -->

      <v-col
        cols="12"
        sm="4"
        md="3"
        lg="3"
        xl="2"
        class="py-0 mt-xl-0 mt-lg-0 mt-md-0 mt-sm-0 mt-4"
      >
        <v-text-field
          v-model="searchWord"
          ref="searchWord"
          append-icon="mdi-magnify"
          clearable
          outlined
          rounded
          dense
          :label="$t('search_games')"
          @click:clear="clearSearchEvent"
          @click:append="searchWordEvent"
          @keydown.enter="searchWordEvent"
        />
      </v-col>
      <!-- game list -->
    </v-row>

    <v-row
      v-if="gameHallList.length > 0"
      class="text-left align-top justify-start px-2"
      :class="[$UIConfig.gamePage.background]"
    >
      <v-col
        v-for="(game, index) in gameHallList"
        v-show="showEnableStatus(game.enable)"
        :key="`${index}`"
        cols="6"
        sm="4"
        md="3"
        lg="2"
        class="pa-0"
      >
        <gameCard
          :game-category-id="selectedGameCategory"
          :index="index"
          :game="game"
          :show-main-daily-rtp="showMainDailyRtp"
        />
      </v-col>
    </v-row>
    <!-- no_results_found notice -->

    <v-row
      v-else
      align="center"
      justify="center"
      style="min-height: 120px"
      :class="[$UIConfig.gamePage.background]"
    >
      <span
        class="custom-text-noto text-caption grey-3--text py-4"
        style="font-size: 12px !important"
        >{{ $t('no_results_found') }}</span
      >
    </v-row>
    <!-- pagination -->

    <v-row v-if="pageObj.pageTotal" :class="[$UIConfig.gamePage.background]">
      <v-col class="text-center">
        <v-pagination
          v-model="pageNumber"
          :length="pageObj.pageTotal"
          total-visible="7"
          circle
          :color="$UIConfig.defaultBtnColor"
          @input="changePageEvent"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  import gameList from '@/mixins/gameList'
  import analytics from '@/mixins/analytics'
  import urlQueryGameType from '@/mixins/urlQueryGameType'
  import scssLoader from '@/mixins/scssLoader.js'
  const STATION = process.env.STATION
  const outpostList = require(`~/station/outpostList.js`).default
  export default {
    mixins: [gameList, analytics, urlQueryGameType, scssLoader],
    name: 'GameListIndex',
    components: {
      linearGradientTitle: () => import(`~/components_station/${STATION}/linearGradientTitle`),
      gameCard: () => import('~/components/game/gameCard')
    },
    data() {
      const gameHallSortList = [
        {
          sortType: 1,
          name: this.$t('all_game')
        },
        {
          sortType: 2,
          name: this.$t('hot_games')
        },
        {
          sortType: 3,
          name: this.$t(this.$UIConfig.gamePage.sortGameName)
        }
      ]
      const pageObj = {
        showItemLimit: 36,
        pageTotal: 1,
        startIndex: 0,
        endIndex: 0
      }

      return {
        gameHallSortList,
        gameHallList: [],
        gameDefault: require(`~/assets/image/${STATION}/game/game_default.webp`),
        getGameHallList: [],
        pageObj,
        showMainDailyRtp: false
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      showGameList({ $store }) {
        let result = $store.getters['gameHall/gameList']
        // 針對正式站，過濾掉BETA 的遊戲
        const browserDomainName = window.location.hostname
        const outpost = outpostList().includes(browserDomainName) ? 1 : 0
        if (!outpost && this.$nuxt.context.env.NUXT_ENV === 'production') {
          result = result.filter((item) => item.enable !== 2)
        }
        return result
      },
      gameCategory({ $store }) {
        return $store.getters['gameProvider/gameCategory']
      },
      gameProviders({ $store }) {
        const currentCategoryId = this.selectedGameCategory
        return [
          {
            id: 1, // 預設值
            brand: this.$t('all_provider'),
            fullname: this.$t('all_provider')
          },
          ...$store.getters['gameProvider/providers']
            .filter((item) => {
              return item.categoryTypes.includes(currentCategoryId)
            })
            .map((item) => {
              return {
                id: item.id,
                // 將所有字符串轉為大寫
                ...item,
                brand: item.brand?.toUpperCase(),
                fullname: item.fullname?.toUpperCase()
              }
            })
            .sort((a, b) => a.fullname.localeCompare(b.fullname))
        ]
      },
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      status404() {
        return this.$store.getters['maintain/status404']
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      }
    },
    watch: {
      categoryButtonStatus: {
        async handler() {
          const categoryId = this.selectedGameCategory
          if (this.maintainSystem[0].maintaining) return
          const hasCategory = this.currentCategory(categoryId)
          if (!hasCategory) {
            this.$store.commit('maintain/SET_STATUS404', true)
            return
          }
          this.providerId = 1
          await this.$store.dispatch('gameProvider/fetch')
          if (categoryId !== undefined) {
            try {
              await this.fetchGameList(categoryId, this.gameSortType, this.providerId)
            } catch (error) {
              console.log(error)
              this.$store.commit('maintain/SET_STATUS404', true)
            }
          }
        }
      },
      showGameList: {
        async handler() {
          this.$nuxt.$loading.start()

          const validate = await this.$validator.validate('filter.*')
          const filteredList = validate
            ? this.getFilteredGameList(this.showGameList)
            : this.showGameList
          this.getGameHallList = filteredList
          this.doPaginationHandler(this.getGameHallList)
          this.$nuxt.$loading.finish()
        },
        deep: true
      }
    },
    async created() {
      await this.$store.dispatch('gameProvider/fetch')
      this.synchronizeUrlValue()
      if (this.gameCategory.length > 0) {
        await this.fetchGameList(this.selectedGameCategory, this.gameSortType, this.providerId)
        this.showMainDailyRtp = true
      }
    },
    mounted() {
      // 站台差異註解
      if (this.$UIConfig.gamePage.resetUrlValue) {
        this.resetUrlValue()
      }
    },
    beforeDestroy() {
      this.$store.commit('gameHall/SET_GAMELIST', [])
    },
    methods: {
      getFilteredGameList(gameList) {
        let filteredList = gameList
        // 按關鍵字篩選
        if (!this.stringNullOrEmpty(this.searchWord)) {
          filteredList = this.getSearchWordList(filteredList, this.searchWord)
        }
        // 按遊戲提供商篩選
        if (this.providerId !== 1) {
          filteredList = filteredList.filter((game) => game.platformId === this.providerId)
        }
        return filteredList
      },
      filterByProvider(gameList) {
        if (this.providerId === 1) {
          gameList = this.showGameList
        } else {
          gameList = this.showGameList.filter((game) => {
            return game.platformId === this.providerId
          })
        }
        return gameList
      },
      async selectedProviderSortEvent() {
        this.pageNumber = 1
        await this.fetchGameList(this.selectedGameCategory, this.gameSortType, this.providerId)
        this.$store.dispatch('gameProvider/fetch')
      },
      changePageEvent() {
        this.doPagination()
        this.fetchGameListHandler(this.getGameHallList)
        //在 Safari 瀏覽器中，window.scrollTo()方法的behavior參數不支援'smooth'值，只支援'auto'和'instant'兩個值。
        window.scrollTo({ top: 0, behavior: 'auto' })
      },
      async selectedGameSortEvent() {
        this.pageNumber = 1
        await this.fetchGameList(this.selectedGameCategory, this.gameSortType)
        this.$store.dispatch('gameProvider/fetch')
      },
      async validateSearchWordEvent() {
        const isValided = await this.$validator.validate('filter.special_character')
        return isValided
      },
      async searchWordEvent() {
        this.pageNumber = 1
        this.isSearchTriggered = true // 開啟搜尋功能
        await this.fetchGameList(this.selectedGameCategory, this.gameSortType)
        this.executeSearch() // 更新關鍵字到Url
      },
      async clearSearchEvent() {
        this.searchWord = ''
        this.pageNumber = 1
        await this.fetchGameList(this.selectedGameCategory, this.gameSortType)
      },
      currentCategory(categoryId) {
        for (let index = 0; index < this.gameCategory.length; index++) {
          const element = this.gameCategory[index]
          if (Number(element.code) === categoryId) {
            return element
          }
        }
      },
      // 搜索函数
      searchByGameProvider(keyword) {
        // 统一转换为大写支持中文
        const upperKeyword = keyword.toUpperCase()

        return this.gameProviders
          .filter((item) => {
            return item.brand?.includes(upperKeyword) || item.fullname?.includes(upperKeyword)
          })
          .map((item) => item.id)
      },
      getSearchWordList(gameDataList, searchWord) {
        const getGameHallListTmp = this.stringNullOrEmpty(searchWord)
          ? gameDataList
          : this.search(searchWord, gameDataList)

        if (!this.stringNullOrEmpty(searchWord)) {
          this.gameSearchAnalytics({
            username: this.userName,
            content: searchWord,
            gameIds: getGameHallListTmp.map((item) => item.id),
            at: this.$moment().format('YYYY-MM-DDTHH:mm:ssZ')
          })
        }
        // 加入此行，解決手機不會收起鍵盤的問題
        this.$refs.searchWord.blur()
        return getGameHallListTmp
      },
      search(keyword, gameDataList) {
        if (this.stringNullOrEmpty(keyword)) return gameDataList
        const searchResultArrGameName = gameDataList.filter((item) => {
          const gameName = item.name.toLowerCase()
          const index = gameName.indexOf(keyword.toLowerCase())
          return index == -1 ? false : true
        })
        const platformIds = this.searchByGameProvider(keyword)
        const searchResultArrGameProvider = gameDataList.filter((item) => {
          return platformIds.includes(item.platformId)
        })

        return [...new Set([...searchResultArrGameName, ...searchResultArrGameProvider])]
      },
      stringNullOrEmpty(word) {
        return word === null || word === undefined || word === ''
      },
      doPaginationHandler(gameList) {
        this.pageObj.pageTotal = Math.ceil(gameList.length / this.pageObj.showItemLimit)
        this.doPagination()
        this.fetchGameListHandler(gameList)
      },
      doPagination() {
        if (this.pageNumber === 1) {
          this.pageObj.startIndex = 0
          this.pageObj.endIndex = this.pageObj.showItemLimit - 1
        } else {
          this.pageObj.startIndex =
            this.pageNumber * this.pageObj.showItemLimit - this.pageObj.showItemLimit
          this.pageObj.endIndex = this.pageNumber * this.pageObj.showItemLimit - 1
        }
      },
      async fetchGameList(gameCategoryId, sortType, providerId) {
        const lang = this.$i18n.locale
        const limit = 999

        await this.$store.dispatch('gameHall/fetchGameList', {
          gameCategoryId,
          sortType,
          providerId,
          lang,
          limit
        })
      },
      async fetchGameListHandler(gameList) {
        let list = []
        for (let index = this.pageObj.startIndex; index <= this.pageObj.endIndex; index++) {
          if (gameList[index] !== undefined) {
            list.push(gameList[index])
          }
        }
        let rtpGameList = await this.fetchGameRtpHandler(list, list)
        rtpGameList = rtpGameList.map((game) => ({
          ...game,
          categoryType: this.selectedGameCategory
        }))
        this.gameHallList = rtpGameList
      },
      async fetchGameRtpHandler(gameSourceList) {
        let gameIds = []
        gameSourceList.forEach((element) => gameIds.push(element.id))
        if (gameIds.length != 0) {
          try {
            const gameRTPList = await this.$clientApi.game.gameRTPList(gameIds)
            gameSourceList = gameSourceList.map((item) => {
              if (gameRTPList) {
                const match = gameRTPList.list.find((obj) => obj.gameId === item.id)
                if (match) {
                  // 直接合併到gameList，並清除gameId
                  // eslint-disable-next-line no-unused-vars
                  const { gameId, ...rest } = match
                  return { ...item, ...rest }
                }
              }
              return item
            })
          } catch (error) {
            console.log(error)
          }
        }
        this.$nuxt.$loading.finish()
        return gameSourceList
      }
    }
  }
</script>

<!-- 這style目前只有華義用到 -->
<style lang="scss" scoped>
  $card-fill-color: map-get($colors, card-fill);

  .bg-color {
    background-color: rgba($card-fill-color, 0.4);
  }
</style>
