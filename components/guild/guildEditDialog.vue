<template>
  <div>
    <v-dialog
      v-model="showGuildEditTmp"
      :fullscreen="breakpoint.xsOnly"
      width="600px"
      persistent
      scrollable
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card class="pa-0" elevation="0" color="dialog-fill">
        <customDialogTitle
          :title="$t('edit_guild_info').toUpperCase()"
          @closeDialog="closeDialog"
        />

        <v-card-text
          id="guild-edit-card"
          :class="['pa-4 pa-sm-6', breakpoint.xsOnly ? 'container scrollable-xs' : 'scrollable-sm']"
        >
          <div class="d-flex h-100-percent flex-nowrap">
            <div v-if="!breakpoint.xsOnly">
              <avatar
                frame
                :badge="$UIConfig.guildEditDialog.totemEnable"
                edit
                :guild-id="guildId"
              />
            </div>
            <div class="pl-sm-6" style="flex-grow: 1">
              <div v-if="breakpoint.xsOnly" class="d-flex jusitfy-center mb-4">
                <avatar
                  frame
                  :badge="$UIConfig.guildEditDialog.totemEnable"
                  edit
                  :guild-id="guildId"
                />
              </div>
              <v-text-field
                v-model="editGuildName"
                height="42px"
                v-validate="{
                  required: true,
                  text_fullwidth: true,
                  name_validate: true,
                  guild_name_limit: true,
                  inappropriate_words: compiledData,
                  guild_duplicate_name: guildNameList
                }"
                :disabled="!$UIConfig.guildEditDialog.nameEnable"
                data-vv-scope="guildEdit"
                name="guild_name"
                :error-messages="errors.first('guildEdit.guild_name')"
                :label="$t('guild_name')"
                filled
                shaped
                :dense="breakpoint.smAndUp"
                class="error-wrap"
              />
              <v-textarea
                v-model="caption"
                class="error-wrap"
                v-validate="{
                  text_fullwidth: $UIConfig.guildEditDialog.textFullWidthValidEnable,
                  validate_string_length: 150
                }"
                :disabled="!$UIConfig.guildEditDialog.notyEnable"
                data-vv-scope="guildEdit"
                name="caption"
                :error-messages="errors.first('guildEdit.caption')"
                no-resize
                counter="150"
                filled
                shaped
                :height="breakpoint.xsOnly ? captionHeight : '200px'"
                :label="$t('edit_guild_caption')"
                @keydown="handleGuildCaptionKeyDown"
              />
              <v-btn
                block
                :disabled="disabledSubmit || caption.length > 150"
                class="button-content--text mt-2 mb-sm-0"
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                @click="setupGuildValue(caption, editGuildName, guildTotemTemp)"
              >
                {{ $t('confirm_modify') }}
              </v-btn>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <guildConfirmDialog
      v-if="guildConfirmStatus"
      :confirm-obj="confirmObj"
      :show-guild-confirm-dialog-status.sync="guildConfirmStatus"
    />
  </div>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import uploadPhoto from '@/mixins/uploadPhoto.js'
  import relationship from '@/mixins/relationship.js'
  import scssLoader from '@/mixins/scssLoader.js'
  import converter from '~/mixins/converter'
  import guildMgr from '~/mixins/guildMgr'
  import images from '~/mixins/images'
  export default {
    name: 'guildEditInfoDialog',
    mixins: [relationship, scssLoader, images, guildMgr, uploadPhoto, hiddenScrollHtml, converter],
    components: {
      guildConfirmDialog: () => import('~/components/guild/guildConfirmDialog'),
      customDialogTitle: () => import('~/components/customDialogTitle'),
      avatar: () => import('~/components/guild/guildAvatar')
    },
    props: {
      showGuildEditDialogStatus: { type: Boolean, required: true, default: false }
    },
    data() {
      return {
        showGuildEditTmp: this.showGuildEditDialogStatus,
        guildConfirmStatus: false,
        disabledSubmit: false,
        confirmObj: {},
        caption: '',
        editGuildName: '',
        onCloseDialogEvent: undefined,
        rules: [(v) => v.length <= 150 || this.$t('exceeds_character_limit')]
      }
    },
    created() {
      this.caption = this.replaceKeywords(this.guildCaption)
      this.editGuildName = this.guildName
    },
    async mounted() {
      this.updateListInfo()
    },
    async destroyed() {
      await this.$store.dispatch('maintain/fetch')
      if (this.maintainSystem[0].maintaining) return
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      contentHeight() {
        return 472
      },
      captionHeight() {
        const windowsVH = window.innerHeight - 324 - 85
        return windowsVH
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showGuildEditDialogStatus: {
        async handler(status) {
          if (this.maintainSystem[0].maintaining) return
          this.showGuildEditTmp = status
        }
      },
      editGuildName: {
        async handler() {
          const validateFormName = 'guildEdit.*'
          const validate = await this.$validator.validate(validateFormName)
          this.disabledSubmit = !validate
        },
        deep: true
      },
      caption: {
        async handler() {
          const validateFormName = 'guildEdit.*'
          const validate = await this.$validator.validate(validateFormName)
          this.disabledSubmit = !validate
        },
        deep: true
      }
    },
    methods: {
      closeDialog(isSetupSuccess = false) {
        const callCloseDialog = () => {
          this.showGuildEditTmp = false
          this.$emit('update:showGuildEditDialogStatus', false)
          this.guildTotemTemp = undefined
          const updateAvatar = this.$store.getters['guild/updateGuildAvatar']
          this.$store.commit('guild/SET_UPDATE_GUILD_AVATAR', !updateAvatar)
        }
        const isChange =
          this.caption !== this.guildCaption ||
          this.editGuildName !== this.guildName ||
          this.guildTotemTemp !== undefined
        if (isChange && !isSetupSuccess) {
          this.confirmObj = {
            confirm: 'leave',
            confirmInfo: this.$t('guild_leave_edit_noty'),
            cancel: 'cancel',
            onConfirmNotify: callCloseDialog
          }
          this.guildConfirmStatus = true
        } else {
          callCloseDialog()
        }
      },
      async updateListInfo() {
        await this.$store.dispatch('guild/fetchSelfGuildDetail')
        await this.$store.dispatch('guild/fetchGuildRanking')
      },
      async setupGuildValue(caption, name, avatar) {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        const confirmFun = async () => {
          let isChangeGuildCaptionSuccess = true
          let isChangeGuildNameSuccess = true
          let isChangeGuildTotemSuccess = true
          const guildThumbName = `星城公會_${this.guildId}`
          if (caption !== this.guildCaption) {
            const { success } = await this.changeGuildCaption(caption)
            isChangeGuildCaptionSuccess = success
          }
          if (name !== this.guildName) {
            const { success } = await this.changeGuildName(name)
            isChangeGuildNameSuccess = success
          }
          if (this.guildTotemTemp) {
            const uploadImgRes = await this.uploadImgRes(guildThumbName, avatar)
            if (uploadImgRes === 1) {
              const { success } = await this.changeGuildTotem()
              isChangeGuildTotemSuccess = success
            }
          }
          if (
            isChangeGuildCaptionSuccess &&
            isChangeGuildNameSuccess &&
            isChangeGuildTotemSuccess
          ) {
            this.closeDialog(true)
          }
        }
        this.confirmObj = {
          confirm: 'apply',
          confirmInfo: `${this.$t('guild_apply_edit_noty')}
            <br />
            <span class="primary--text text-body-2 custom-text-noto">※
            ${this.$t('guild_name_noty')}
            </span>`,
          cancel: 'cancel',
          onConfirmNotify: confirmFun
        }
        this.guildConfirmStatus = true
      },
      handleGuildCaptionKeyDown(e) {
        const currentLength = this.caption ? this.caption.length : 0
        // 允許刪除鍵和方向鍵
        if (
          e.key === 'Backspace' ||
          e.key === 'Delete' ||
          e.key === 'ArrowLeft' ||
          e.key === 'ArrowRight' ||
          e.key === 'ArrowUp' ||
          e.key === 'ArrowDown'
        ) {
          return
        }
        // 如果已達到最大長度，阻止輸入
        if (currentLength >= 150) {
          e.preventDefault()
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $dialog-fill: map-get($colors, 'dialog-fill');
  $primary: map-get($colors, 'primary');
  .v-data-table ::v-deep {
    tbody {
      tr:hover {
        background-color: map-get($colors, 'grey-4');
      }
    }
  }
  .error-wrap ::v-deep .v-messages {
    white-space: normal !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    line-height: 1.2;
    padding-top: 4px;
  }
  .guild-info-gackground {
    background: rgba(0, 0, 0, 0.12);
  }
  .guild-info-gackground-xs {
    background: $dialog-fill;
  }
  .guild-edit-dialog-height-vertical {
    height: calc(var(--vh, 1vh) * 100 - 333px) !important;
  }
  .v-tabs-items {
    background-color: transparent !important;
  }
  .v-tab--active {
    color: $primary !important;
  }
  .container {
    height: 100vh; /* 將容器的高度設置為視窗的高度 */
    height: calc(var(--vh, 1vh) * 100);
  }
  .h-100-percent {
    height: 100% !important;
  }
  #guild-edit-card {
    &.scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    &.scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable-sm {
        max-height: calc(90svh - 52px);
      }
      &.scrollable-xs {
        max-height: calc(100svh - 52px);
      }
    }
  }
</style>
