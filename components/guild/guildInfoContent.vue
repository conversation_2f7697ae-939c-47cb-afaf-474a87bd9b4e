<template>
  <v-container class="pa-0">
    <v-card
      :color="$UIConfig.stationPage.backGround"
      style="width: 100%"
      :elevation="$vuetify.breakpoint.smAndDown ? 0 : 4"
      :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }"
    >
      <v-card-title class="px-sm-6">
        <div class="d-flex align-center">
          <span class="custom-text-noto text-h5 default-content--text font-weight-bold">{{
            $t('guild_info')
          }}</span>
          <v-btn
            v-if="guildRank === 3 && !$vuetify.breakpoint.xsOnly"
            class="ml-5"
            color="primary"
            outlined
            max-height="28"
            width="51"
            @click="onEditGuildInfo()"
          >
            {{ $t('edit') }}
          </v-btn>
          <v-btn
            v-if="guildRank === 3 && $vuetify.breakpoint.xsOnly"
            icon
            color="default-concent"
            class="mx-2"
            @click="onEditGuildInfo()"
          >
            <span class="material-symbols-outlined default-concent--text"> edit </span>
          </v-btn>
        </div>
      </v-card-title>
      <v-card-text class="px-sm-6 pb-6 pb-md-4">
        <div
          v-if="
            $vuetify.breakpoint.smAndUp &&
            !isJoinOver24Hours &&
            $UIConfig.guildInfoPage.showLeaveCountDown
          "
          class="guild-info-background rounded-lg gradient-black-opacity-20-0 d-flex align-center py-1 px-2 mb-2"
        >
          <v-icon color="error" class="mr-1">mdi-clock-outline</v-icon>
          <span class="mr-1 white--text custom-text-noto text-body-2" v-html="guildFundText"></span>
        </div>
        <v-card
          flat
          class="guild-info-background"
          :style="$vuetify.breakpoint.xsOnly ? { background: 'none' } : null"
        >
          <!-- character -->
          <guildLobbyInfo is-info-page />
        </v-card>
        <v-divider class="my-7 my-sm-6"></v-divider>
        <v-row>
          <v-col class="mt-0 mt-sm-2" cols="12" sm="5" md="5" lg="5" xl="5">
            <span class="custom-text-noto text-subtitle-1 default-content--text font-weight-bold">{{
              $t('guild_member_list')
            }}</span>
          </v-col>
          <v-col cols="12" sm="7" md="7" lg="7" xl="7">
            <v-text-field
              v-model="searchWord"
              ref="searchWord"
              append-icon="mdi-magnify"
              clearable
              outlined
              rounded
              dense
              :rules="rules"
              :label="$t('find_member')"
              @click:clear="clearSearchEvent"
              @click:append="searchWordEvent"
              @keydown.enter="searchWordEvent"
            /> </v-col
        ></v-row>
        <!-- character-detail -->
        <guildInfoList
          is-info-page
          :items-per-page="10"
          :guild-info-name="guildName"
          :items-per-page-options="$UIConfig.guildInfoPage.infoListOptions"
          :guild-info-list="guildMemberFilterList"
        />
      </v-card-text>
      <guildEditDialog
        v-if="showGuilEditDialogStatus"
        :show-guild-edit-dialog-status.sync="showGuilEditDialogStatus"
      />
    </v-card>
  </v-container>
</template>

<script>
  import { mapGetters } from 'vuex'
  import images from '~/mixins/images'
  import guildMgr from '~/mixins/guildMgr'
  import convertTime from '~/utils/convertTime'
  import orientation from '@/mixins/orientation.js'
  const STATION = process.env.STATION
  export default {
    name: 'guildInfoContent',
    mixins: [guildMgr, images, orientation],
    components: {
      guildLobbyInfo: () => import(`~/components_station/${STATION}/guild/guildLobbyInfo`),
      guildInfoList: () => import(`~/components_station/${STATION}/guild/guildInfoList`),
      guildEditDialog: () => import(`~/components/guild/guildEditDialog`)
    },
    data() {
      return {
        userDetail: {},
        guildDetail: {},
        searchWord: '',
        guildMemberFilterList: [],
        showGuilEditDialogStatus: false,
        rules: [
          (val) =>
            !/[^\u4e00-\u9fa5a-zA-Z0-9]/.test(val) || this.$t('no_special_characters_allowed')
        ]
      }
    },
    async created() {
      await this.$store.dispatch('role/updateUserDetail')
      await this.$store.dispatch('guild/fetchSelfGuildDetail')
      await this.$store.dispatch('guild/fetchGuildRanking')

      const guildFundResponse = await this.getGuildFund()
      this.$store.commit('guild/SET_GUILD_FUND', guildFundResponse.fund)
      this.guildMemberFilterList = this.guildMemberInfoList
    },

    computed: {
      ...mapGetters('role', ['userName']),
      guildFundText() {
        const span = '<span class="error--text custom-text-noto text-body-2">{0}</span>'
        const text = this.$t('guild_countdown_leave', {
          year: span.format(this.futureYear),
          month: span.format(this.futureMonth),
          day: span.format(this.futureDay),
          gmt: convertTime.getGMTOffset(this.$UIConfig.timeStamp.timezone),
          hour: span.format(this.futureHour),
          minute: span.format(this.futureMinutes),
          second: span.format(this.futureSeconds)
        })
        return text
      }
    },
    watch: {
      guildMemberInfoList: {
        async handler() {
          this.searchWordEvent()
        }
      }
    },
    methods: {
      stringIsNullOrEmpty(str) {
        return str === '' || str === null || str === undefined
      },
      onEditGuildInfo() {
        this.showGuilEditDialogStatus = true
      },
      async searchWordEvent() {
        const ruleSuccess = this.$refs.searchWord.validate()
        if (!ruleSuccess) return
        if (this.stringIsNullOrEmpty(this.searchWord))
          this.guildMemberFilterList = this.guildMemberInfoList
        else {
          this.guildMemberFilterList = this.guildMemberInfoList.filter((item) => {
            return item.name.includes(this.searchWord)
          })
        }
        this.$refs.searchWord.blur()
      },
      async clearSearchEvent() {
        this.searchWord = ''
        this.guildMemberFilterList = this.guildMemberInfoList
      }
    }
  }
</script>

<style lang="scss" scoped>
  .v-data-table ::v-deep {
    tbody {
      tr:hover {
        background-color: map-get($colors, 'grey-4');
      }
    }
  }
  .gradient-black-opacity-20-0 {
    border-radius: 3px;
    background: var(
      --Grey-gradient-black-opacity-20-0,
      linear-gradient(90deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%)
    ) !important;
  }
  .guild-info-background {
    background: rgba(0, 0, 0, 0.12);
  }
  .countdown-banner {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 8px 16px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
  }

  .countdown-time {
    color: #ff4444;
    font-weight: 600;
    font-family: monospace;
    font-size: 16px;
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
