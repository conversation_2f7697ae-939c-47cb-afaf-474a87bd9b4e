<template>
  <v-container class="pa-0">
    <v-card
      :color="$UIConfig.guildListPage.backGroundColor"
      :class="['w-100 px-4 px-sm-6', { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }]"
      :elevation="$vuetify.breakpoint.smAndDown ? 0 : 4"
    >
      <v-card-title class="px-0 pt-6 pb-3">
        <span class="custom-text-noto text-h5 default-content--text font-weight-bold">{{
          $t('guild_list')
        }}</span>
      </v-card-title>
      <v-card-text class="px-0 pb-6 pb-md-4">
        <!-- description -->

        <div class="d-flex align-center">
          <span class="material-symbols-outlined pr-1"> nest_clock_farsight_analog </span>
          <span
            class="custom-text-noto text-body-2"
            :class="[$UIConfig.guildListPage.countDownTextSetting]"
            >{{ $t('guild_countdown') }}
          </span>
          <span
            class="custom-text-noto text-body-2"
            :class="[$UIConfig.guildListPage.countDownNumberColor]"
          >
            {{ countDownTime }}
          </span>
          <span class="custom-text-noto text-body-2">{{ $t('second') }}</span>
          <v-spacer v-if="!$vuetify.breakpoint.xsOnly" />

          <span
            v-if="!$vuetify.breakpoint.xsOnly && $UIConfig.guildListPage.showGuildRank"
            class="custom-text-noto text-caption grey-3--text"
            style="font-size: 12px !important"
          >
            {{ '＊' + convertMessage(guildRefreshTime) }}
          </span>
        </div>
        <span
          v-if="$vuetify.breakpoint.xsOnly && $UIConfig.guildListPage.showGuildRank"
          class="custom-text-noto text-caption grey-3--text"
          style="font-size: 12px !important"
        >
          {{ '＊' + convertMessage(guildRefreshTime) }}
        </span>
        <!-- rankingBanner -->
        <v-tooltip
          v-if="isGuildRankOn"
          content-class="tooltip-content-custom"
          bottom
          nudge-top="30px"
        >
          <template v-slot:activator="{ on, attrs }">
            <div
              class="d-flex align-center flex-wrap my-3 px-6"
              :class="[$UIConfig.guildListPage.guildBannerSetting]"
              v-bind="attrs"
              v-on="on"
            >
              <linearGradientVector class="pa-0 ma-0" />
              <v-row no-gutters>
                <v-col
                  cols="12"
                  sm="6"
                  align-self="center"
                  v-bind="attrs"
                  v-on="on"
                  :class="{ 'd-flex justify-center': $vuetify.breakpoint.xsOnly }"
                >
                  <div class="d-flex align-center">
                    <v-img
                      class="ml-5 mr-3"
                      :src="getImage('<EMAIL>')"
                      :srcset="getSrcset('coin')"
                      :max-width="$vuetify.breakpoint.xsOnly ? 16 : 24"
                      :max-height="$vuetify.breakpoint.xsOnly ? 16 : 24"
                    />
                    <span
                      class="primary-variant-3--text mr-3"
                      :class="{
                        'custom-text-noto text-h6': !$vuetify.breakpoint.xsOnly,
                        'text-body-1 text-h5': $vuetify.breakpoint.xsOnly
                      }"
                    >
                      {{ $t('total_reward') }}
                    </span>
                    <v-icon
                      :size="$vuetify.breakpoint.xsOnly ? '20px' : ''"
                      class="primary-variant-3--text"
                    >
                      mdi-help-circle-outline
                    </v-icon>
                  </div>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  align-self="center"
                  :class="{
                    'd-flex justify-center': $vuetify.breakpoint.xsOnly
                  }"
                  ><span
                    class="custom-text-noto text-h5 text-sm-h4 default-content--text font-weight-bold justify-center"
                    >{{ guildAward.toLocaleString() }}
                  </span>
                </v-col>
              </v-row>
              <linearGradientVector class="pa-0 ma-0" />
            </div>
          </template>
          <div>
            <span>{{ $t('guild_reward_noty') }}</span>
            <br />
            <span>{{ $t('guild_reward_noty1') }}</span>
          </div>
        </v-tooltip>

        <!-- rankingSearch -->
        <v-row class="mt-7 mb-0">
          <v-col cols="12" sm="6" md="6" lg="6" xl="6" class="py-0">
            <v-text-field
              v-model="searchWord"
              ref="searchWord"
              append-icon="mdi-magnify"
              clearable
              outlined
              rounded
              dense
              :rules="rules"
              :label="$t('find_guild')"
              @click:clear="clearSearchEvent"
              @click:append="searchWordEvent"
              @keydown.enter="searchWordEvent"
            />
          </v-col>
        </v-row>
        <!-- rankingList -->
        <rankingList
          :user-name="userName"
          is-info-page
          :items-per-page="10"
          :items-per-page-options="[10, 20, 30, -1]"
          :ranking-list="guildRankingFilterList"
        ></rankingList>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
  import { mapGetters } from 'vuex'
  import images from '~/mixins/images'
  import guildMgr from '~/mixins/guildMgr'
  import converter from '~/mixins/converter'
  import orientation from '~/mixins/orientation'
  const STATION = process.env.STATION

  export default {
    mixins: [orientation, guildMgr, images, converter],
    name: 'GuildListContent',
    components: {
      rankingList: () => import(`~/components_station/${STATION}/guild/rankingList`),
      linearGradientVector: () => import('~/components/linearGradientVector')
    },
    data() {
      return {
        searchWord: '',
        updateTime: 60,
        countDownTime: 0,
        timer: '',
        guildRankingFilterList: [],
        rules: [
          (val) =>
            !/[^\u4e00-\u9fa5a-zA-Z0-9]/.test(val) || this.$t('no_special_characters_allowed')
        ]
      }
    },

    async mounted() {
      await this.updateListInfo()
      this.countDownTime = this.updateTime
      this.guildRankingFilterList = this.guildRankingList
      this.timer = setInterval(async () => {
        if (this.countDownTime - 1 < 0) {
          await this.updateListInfo()
          this.countDownTime = this.updateTime
        } else this.countDownTime--
      }, 1000)
    },
    destroyed() {
      clearInterval(this.timer)
    },

    computed: {
      ...mapGetters('role', ['userName'])
    },
    watch: {
      orientation: {
        handler() {
          this.scrollToTop()
        }
      },
      guildRankingList: {
        handler() {
          const ruleSuccess = this.$refs.searchWord.validate()
          if (this.stringIsNullOrEmpty(this.searchWord) || !ruleSuccess) {
            this.guildRankingFilterList = this.guildRankingList
          } else {
            this.guildRankingFilterList = this.guildRankingList.filter((item) => {
              return item.guildName.includes(this.searchWord)
            })
          }
        }
      }
    },
    methods: {
      scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'auto' })
      },
      stringIsNullOrEmpty(str) {
        return str === '' || str === null || str === undefined
      },
      async updateListInfo() {
        await this.$store.dispatch('guild/fetchSelfGuildDetail')
        await this.$store.dispatch('guild/fetchGuildRanking')
      },
      async searchWordEvent() {
        const ruleSuccess = this.$refs.searchWord.validate()
        if (!ruleSuccess) return
        if (this.stringIsNullOrEmpty(this.searchWord))
          this.guildRankingFilterList = this.guildRankingList
        else {
          this.guildRankingFilterList = this.guildRankingList.filter((item) => {
            return item.guildName.includes(this.searchWord)
          })
        }
        this.$refs.searchWord.blur()
      },
      async clearSearchEvent() {
        this.searchWord = ''
        this.guildRankingFilterList = this.guildRankingList
      }
    }
  }
</script>
<style lang="scss">
  .guild-banner {
    height: 92px;
    background: linear-gradient(
      90deg,
      rgba(68, 1, 6, 0) 10%,
      rgba(68, 1, 6, 1) 15%,
      rgba(215, 116, 12, 1) 60%,
      rgba(138, 20, 29, 1) 80%,
      rgba(68, 1, 6, 0) 100%
    );
  }
  .guild-banner-wayi {
    height: 92px;
    background: linear-gradient(
      90deg,
      rgba(85, 48, 115, 0) 10%,
      rgba(85, 48, 115, 0.1) 15%,
      rgba(168, 78, 183, 0.9) 50%,
      rgba(100, 33, 94, 0.9) 70%,
      rgba(85, 48, 115, 0) 100%
    );
  }
  .text-wrap {
    display: block;
  }
</style>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-left {
      padding-left: calc(16px + env(safe-area-inset-left)) !important;
    }
    .notch-right {
      padding-right: calc(16px + env(safe-area-inset-right)) !important;
    }
    //sm
    @media (min-width: 600px) {
      .notch-left {
        padding-left: calc(24px + env(safe-area-inset-left)) !important;
      }
      .notch-right {
        padding-right: calc(24px + env(safe-area-inset-right)) !important;
      }
    }
  }
</style>
