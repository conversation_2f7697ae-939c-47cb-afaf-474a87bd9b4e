<template>
  <v-dialog v-model="showUploadTotemMethodDialogStatusTmp" width="358" persistent>
    <v-card color="transparent">
      <customDialogTitle :title="$t('custom_logo').toUpperCase()" @closeDialog="closeDialog" />
      <v-card-text class="pt-4">
        <!-- 選擇圖片 會隱藏 -->
        <v-file-input id="choosePhoto" accept="image/*" @change="setPhoto(image)" v-model="image">
        </v-file-input>
        <!-- 選擇圖片 -->
        <v-row no-gutters justify="center" align="center">
          <v-btn class="ma-2 w-100" outlined color="primary" @click="choosePhoto">
            <span class="material-symbols-outlined mr-3"> add </span>
            <span class="text-buttom custom-text-noto">
              {{ $t('choosse_photo') }}
            </span>
          </v-btn>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'uploadTotemMethodDialog',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },

    props: {
      showUploadTotemMethodDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        showUploadTotemMethodDialogStatusTmp: this.showUploadTotemMethodDialogStatus,
        //用於選擇圖片
        image: null
      }
    },

    watch: {
      showUploadTotemMethodDialogStatus: {
        handler(val) {
          this.showUploadTotemMethodDialogStatusTmp = val
        }
      }
    },
    computed: {},
    methods: {
      closeDialog() {
        this.showUploadTotemMethodDialogStatusTmp = false
        this.$emit('update:showUploadTotemMethodDialogStatus', false)
      },
      openChooseLocalPhotoDialog() {
        this.closeDialog()
      },
      showEditPhotoDialog() {
        this.closeDialog()
        this.$nuxt.$emit('root:showEditTotemDialogStatus', true)
      },
      setPhoto(img) {
        try {
          if (img.type.startsWith('image/') && !img.type.endsWith('gif')) {
            // 上傳文件類型是否為圖片
            this.$store.commit('role/SET_SELECT_PHOTO', img)
            this.showEditPhotoDialog()
          } else {
            this.$notify.error(this.$t('file_error'))
          }
        } catch {
          console.log()
        }
      },
      //選擇圖片
      choosePhoto() {
        document.getElementById('choosePhoto').click()
      }
    }
  }
</script>
<style scoped>
  .v-input {
    display: none;
  }
</style>
