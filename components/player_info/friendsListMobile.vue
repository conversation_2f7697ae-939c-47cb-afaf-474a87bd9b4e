<template>
  <tbody class="grey-6" v-show="$vuetify.breakpoint.xsOnly" height="auto">
    <tr
      v-for="item in list"
      :key="item.username"
      class="v-data-table__mobile-table-row body-classes"
    >
      <v-menu
        :close-on-content-click="false"
        offset-y
        absolute
        z-index="3"
        content-class="easyPlayer-custom-border"
      >
        <template v-slot:activator="{ on, attrs }">
          <div @click="$emit('select-player', item)" class="px-4" v-bind="attrs" v-on="on">
            <td v-for="header in headers" :key="header.text" class="v-data-table__mobile-row">
              <!--  header   -->
              <div class="v-data-table__mobile-row__header d-flex">
                <span class="content--text px-0 nowrap--text"> {{ header.text }} </span>
              </div>
              <!--  item   -->
              <slot :name="`mobile-${header.value}`" :item="item">
                {{ item[header.value] }}
              </slot>
            </td>
          </div>
        </template>
        <playerInfoCard
          report
          :player-info.sync="playerInfo"
          class="elevation-4"
          :style="$vuetify.breakpoint.smAndUp ? 'width: 340px' : 'width: 300px'"
          tile
          only-coin
          badge-type="relation"
          :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
        />
      </v-menu>
      <v-divider></v-divider>
    </tr>
  </tbody>
</template>

<script>
  export default {
    name: 'FriendsListMobile',
    components: {
      playerInfoCard: () => import('~/components/player_info/easyPlayerInfo')
    },
    props: {
      list: {
        type: Array,
        default: () => [],
        required: true
      },
      headers: {
        type: Array,
        default: () => [],
        required: true
      },
      playerInfo: {
        type: Object,
        default: () => {},
        required: true
      }
    }
  }
</script>
