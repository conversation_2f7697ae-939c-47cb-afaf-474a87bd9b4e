<template>
  <v-container class="pa-0" id="login-info-content-data-table">
    <v-card
      :color="$UIConfig.stationPage.backGround"
      :class="[
        'w-100 px-4 py-6 px-sm-6',
        { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }
      ]"
      :elevation="breakpoint.smAndDown ? 0 : 4"
    >
      <v-card-title class="px-0 pt-0">
        <span class="custom-text-noto text-h5 default-content--text font-weight-bold">{{
          $t('login_info')
        }}</span>
      </v-card-title>
      <v-card-text class="pa-0">
        <!-- info -->
        <v-row no-gutters>
          <span class="default-content--text custom-text-noto text-body-2"
            >{{ $t('binding_info_phone_number') + '：'
            }}{{ isBind ? this.phoneNumber.slice(0, -4) + '****' : $t('unBound') }}</span
          >
        </v-row>
        <!-- radio button -->
        <v-row class="mb-2" no-gutters align="center">
          <v-switch
            inset
            v-model="isEnableSafetyCodeTmp"
            @change="settingSafety(isEnableSafetyCodeTmp)"
            :disabled="!isBind"
            color="primary"
            class="default-content--text"
          >
            <template v-slot:label>
              <span class="default-content--text custom-text-noto text-body-1">{{
                $t('device_whitelist_sms_verification')
              }}</span>
            </template>
          </v-switch>
        </v-row>
        <v-divider></v-divider>
        <!-- login & white list detail -->
        <v-row no-gutters class="pt-6">
          <v-col cols="12">
            <v-tabs
              v-model="tab"
              class="pb-6"
              align-with-title
              show-arrows
              background-color="transparent"
              color="primary"
            >
              <v-tab v-for="tabItem in tabs" :key="tabItem.id">
                {{ $t(tabItem.title) }}
              </v-tab>
              <v-tooltip
                open-delay="50"
                close-delay="25"
                bottom
                z-index="10"
                :content-class="`tooltip-content-custom ${
                  $vuetify.breakpoint.xsOnly ? 'left-position' : ''
                }`"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-icon v-bind="attrs" v-on="on" class="ml-2">mdi-help-circle-outline </v-icon>
                </template>
                <span>{{ $t('login_info_tooltip') }}</span>
              </v-tooltip>
            </v-tabs>
            <v-tabs-items v-model="tab">
              <v-tab-item v-for="tabItem in tabs" :key="tabItem.id">
                <!-- 登入歷程 -->
                <template v-if="tabItem.id == 1">
                  <v-data-table
                    class="grey-6 default-content--text"
                    @pagination="scrollToTop"
                    @update:page="handleLoginInfoPageChange"
                    :hide-default-footer="breakpoint.xsOnly"
                    :items-per-page="loginInfoItemsPage"
                    :page="loginInfoCurrentPage"
                    :headers="loginHeaders"
                    :items="loginRecord"
                    :no-data-text="$t('no_data')"
                    :footer-props="{
                      'items-per-page-text': $t('items_per_page'),
                      'items-per-page-all-text': $t('all'),
                      'page-text': `{0}-{1} ${$t('total_page')} {2} ${$t('quantity')}`,
                      'items-per-page-options': [5, -1]
                    }"
                  >
                    <template v-if="loginRecord.length === 0" v-slot:body>
                      <tbody v-show="breakpoint.xsOnly">
                        <tr class="v-data-table__empty-wrapper d-flex justify-center">
                          <td class="d-flex align-center" colspan="3">
                            <span class="text-center">
                              {{ $t('no_data') }}
                            </span>
                          </td>
                        </tr>
                      </tbody>
                      <tbody v-show="!breakpoint.xsOnly">
                        <tr class="v-data-table__empty-wrapper">
                          <td colspan="4">
                            <span class="text-center">
                              {{ $t('no_data') }}
                            </span>
                          </td>
                        </tr>
                      </tbody>
                    </template>
                    <template v-if="breakpoint.xsOnly" v-slot:footer="{ props: { pagination } }">
                      <div class="v-data-footer">
                        <v-row no-gutters>
                          <v-col>
                            <div class="v-data-footer__select d-flex justify-start ml-3">
                              <span> {{ $t('items_per_page') }}</span>
                              <v-select
                                class="py-0 mt-3 mb-3"
                                v-model="loginInfoSelect"
                                hide-details
                                height="32"
                                @input="onLoginInfoSelect(loginRecord.length)"
                                :items="pagePaginationitem([5, -1])"
                              ></v-select>
                              <span class="v-data-footer__pagination">
                                {{ pagePagination(pagination) }}
                              </span>
                            </div>
                          </v-col>
                        </v-row>
                        <v-row no-gutters>
                          <v-col>
                            <v-btn
                              class="v-data-footer__icons-before"
                              icon
                              :disabled="pagination.pageStart === 0"
                              @click="
                                loginInfoCurrentPage =
                                  pagination.page - 1 === 0 ? 1 : pagination.page - 1
                              "
                            >
                              <v-icon dark> mdi-chevron-left </v-icon>
                            </v-btn>
                            <v-btn
                              class="v-data-footer__icons-after"
                              icon
                              :disabled="pagination.pageStop === pagination.itemsLength"
                              @click="
                                loginInfoCurrentPage =
                                  pagination.page + 1 === pagination.pageCount
                                    ? pagination.pageCount
                                    : pagination.page + 1
                              "
                            >
                              <v-icon dark> mdi-chevron-right </v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </div>
                    </template>
                  </v-data-table>
                </template>
                <!-- 裝置白名單 -->
                <template v-else-if="tabItem.id == 2">
                  <v-container class="default-content--text custom-text-noto text-body-2 pa-0">
                    <template v-if="!isBind">
                      <v-row no-gutters class="py-4">
                        <v-col cols="12" class="text-center">
                          <span class="grey-3--text custom-text-noto text-body-2">
                            {{ $t('unopened_device_whitelist_sms_verification') }}
                          </span>
                        </v-col>
                      </v-row>
                    </template>
                    <template v-else>
                      <v-data-table
                        class="grey-6 default-content--text"
                        @pagination="scrollToTop"
                        @update:page="handleWhiteDevicePageChange"
                        :hide-default-footer="breakpoint.xsOnly"
                        :items-per-page="whiteDeviceItemsPage"
                        :page="whiteDeviceCurrentPage"
                        :headers="whiteDevice"
                        :items="deviceList"
                        :no-data-text="$t('no_data')"
                        :footer-props="{
                          'items-per-page-text': $t('items_per_page'),
                          'items-per-page-all-text': $t('all'),
                          'page-text': `{0}-{1} ${$t('total_page')} {2} ${$t('quantity')}`,
                          'items-per-page-options': [10, 20, 30, -1]
                        }"
                      >
                        <template v-if="deviceList.length === 0" v-slot:body>
                          <tbody v-show="breakpoint.xsOnly">
                            <tr class="v-data-table__empty-wrapper d-flex justify-center">
                              <td class="d-flex align-center" colspan="2">
                                <span class="text-center">
                                  {{ $t('no_data') }}
                                </span>
                              </td>
                            </tr>
                          </tbody>
                          <tbody v-show="!breakpoint.xsOnly">
                            <tr class="v-data-table__empty-wrapper">
                              <td colspan="4">
                                <span class="text-center">
                                  {{ $t('no_data') }}
                                </span>
                              </td>
                            </tr>
                          </tbody>
                        </template>
                        <template v-slot:item.id="{ item }">
                          <v-btn
                            depressed
                            :color="$UIConfig.defaultBtnColor"
                            dark
                            class="button-content--text"
                            @click="
                              {
                                confirmRemoveDeviceWhiteStatus = true
                                selectedDeviceId = item.id
                              }
                            "
                            >{{ $t('remove') }}
                          </v-btn>
                        </template>
                        <template
                          v-if="breakpoint.xsOnly"
                          v-slot:footer="{ props: { pagination } }"
                        >
                          <div class="v-data-footer">
                            <v-row no-gutters>
                              <v-col>
                                <div class="v-data-footer__select d-flex justify-start ml-3">
                                  <span> {{ $t('items_per_page') }}</span>
                                  <v-select
                                    class="py-0 mt-3 mb-3 default-content--text"
                                    v-model="whiteDeviceSelect"
                                    hide-details
                                    height="32"
                                    @input="onWhiteDeviceSelect(deviceList.length)"
                                    :items="pagePaginationitem([10, 20, 30, -1])"
                                  ></v-select>
                                  <span class="v-data-footer__pagination">
                                    {{ pagePagination(pagination) }}
                                  </span>
                                </div>
                              </v-col>
                            </v-row>
                            <v-row no-gutters>
                              <v-col>
                                <v-btn
                                  class="v-data-footer__icons-before"
                                  icon
                                  :disabled="pagination.pageStart === 0"
                                  @click="
                                    whiteDeviceCurrentPage =
                                      pagination.page - 1 === 0 ? 1 : pagination.page - 1
                                  "
                                >
                                  <v-icon dark> mdi-chevron-left </v-icon>
                                </v-btn>
                                <v-btn
                                  class="v-data-footer__icons-after"
                                  icon
                                  :disabled="pagination.pageStop === pagination.itemsLength"
                                  @click="
                                    whiteDeviceCurrentPage =
                                      pagination.page + 1 === pagination.pageCount
                                        ? pagination.pageCount
                                        : pagination.page + 1
                                  "
                                >
                                  <v-icon dark> mdi-chevron-right </v-icon>
                                </v-btn>
                              </v-col>
                            </v-row>
                          </div>
                        </template>
                      </v-data-table>
                    </template>
                  </v-container>
                </template>
              </v-tab-item>
            </v-tabs-items>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-dialog
      v-model="confirmRemoveDeviceWhiteStatus"
      persistent
      max-width="290"
      content-class="rounded-lg"
    >
      <v-card color="transparent" class="pa-4 pa-sm-6">
        <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text pa-0">
          {{ $t('reminder') }}
        </v-card-title>
        <v-card-text class="text-body-2 default-content--text px-0 py-6">
          {{ $t('check_remove_white_device_desc') }}
        </v-card-text>
        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
              <v-btn
                :class="['text-button', breakpoint.xsOnly ? 'w-100' : '']"
                elevation="0"
                text
                @click="confirmRemoveDeviceWhiteStatus = false"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                :class="['black--text text-button', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                @click="
                  {
                    removeAllowedDevice(selectedDeviceId)
                    confirmRemoveDeviceWhiteStatus = false
                  }
                "
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
  import { mapGetters } from 'vuex'
  import cloneDeep from 'lodash/cloneDeep'
  import orientation from '@/mixins/orientation'
  import scssLoader from '@/mixins/scssLoader.js'
  import convertTime from '~/utils/convertTime'
  export default {
    mixins: [orientation, scssLoader],
    name: 'LoginInfoContent',
    props: {
      title: {
        type: String,
        default: ''
      },
      loginHeaders: {
        type: Array
      },
      whiteDevice: {
        type: Array
      }
    },
    async created() {
      await this.updateEnableSafetyCode()
    },
    data() {
      const tabs = [
        { title: 'login_record', id: 1 },
        { title: 'white_list', id: 2 }
      ]
      return {
        tab: 0,
        tabs: tabs,
        isEnableSafetyCodeTmp: false,
        confirmRemoveDeviceWhiteStatus: false,
        selectedDeviceId: '',
        loginInfoSelect: this.$t('all'),
        loginInfoItemsPage: -1,
        loginInfoCurrentPage: 1,
        whiteDeviceSelect: 10,
        whiteDeviceItemsPage: 10,
        whiteDeviceCurrentPage: 1
      }
    },
    watch: {
      isEnableSafetyCode: {
        handler: function (val) {
          this.isEnableSafetyCodeTmp = val
        },
        immediate: true
      },
      orientation: {
        handler() {
          this.scrollToTop()
        }
      }
    },
    computed: {
      ...mapGetters('role', [
        'userName',
        'phoneNumber',
        'deviceList',
        'isEnableSafetyCode',
        'isBind'
      ]),
      loginRecord({ $store }) {
        const record = $store.getters['role/loginRecord']
        const formatData = record.map((item) => ({
          ...item,
          date: convertTime
            .convertGMT8Time(
              item.date,
              this.$UIConfig.timeStamp.formatLogin,
              this.$UIConfig.timeStamp.timezone
            )
            .format('')
        }))
        return formatData
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      async updateEnableSafetyCode() {
        const res = await this.$store.dispatch('social/getUserDetail', this.userName)
        this.$store.commit('role/SET_IS_ENABLE_SAFETY_CODE', res.isEnableSafetyCode)
      },
      async removeAllowedDevice(id) {
        const reqData = this.$wsPacketFactory.removeAllowedDevice(id)
        this.$wsClient.send(reqData)
        this.$store.commit('role/REMOVE_DEVICE_LIST', id)
        this.$notify.success(this.$t('remove_device_white_noty'))
      },
      async settingSafety(status) {
        const reqData = this.$wsPacketFactory.settingSafety(status)
        this.$wsClient.send(reqData)
        if (status) this.$notify.info(this.$t('open_device_white_sms_noty'))
        else this.$notify.info(this.$t('close_device_white_sms_noty'))
      },
      scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'auto' })
      },
      pagePaginationitem(itemArray) {
        let newItemArray = cloneDeep(itemArray)
        newItemArray[newItemArray.findIndex((x) => x === -1)] = this.$t('all')
        return newItemArray
      },
      pagePagination(pagination) {
        return pagination.pageCount === 0
          ? '-'
          : `${pagination.pageStart + 1}-${pagination.pageStop} ${this.$t('total_page')}
          ${pagination.itemsLength} ${this.$t('quantity')}`
      },
      onLoginInfoSelect() {
        if (this.loginInfoSelect === this.$t('all')) this.loginInfoItemsPage = -1
        else this.loginInfoItemsPage = this.loginInfoSelect
      },
      onWhiteDeviceSelect() {
        if (this.whiteDeviceSelect === this.$t('all')) this.whiteDeviceItemsPage = -1
        else this.whiteDeviceItemsPage = this.whiteDeviceSelect
      },
      handleLoginInfoPageChange(page) {
        this.loginInfoCurrentPage = page
      },
      handleWhiteDevicePageChange(page) {
        this.whiteDeviceCurrentPage = page
      }
    }
  }
</script>

<style lang="scss">
  $grey-4: map-get($colors, 'grey-4');
  $grey-6: map-get($colors, 'grey-6');
  $default-content-color: map-get($colors, default-content);
  #login-info-content-data-table {
    color: $default-content-color;
    .v-input {
      &--selection-controls {
        color: $default-content-color;
        padding-top: 0;
      }
    }
    .v-data-table-header-mobile {
      th {
        background: $grey-4 !important;
      }
    }
    .v-data-table {
      tbody {
        tr {
          &:hover {
            background: $grey-4 !important;
          }
        }
      }
    }
    .v-icon {
      color: $default-content-color;
    }
    .v-data-footer {
      color: $default-content-color;
    }
    .v-select__selections {
      color: $default-content-color;
    }
  }
</style>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
