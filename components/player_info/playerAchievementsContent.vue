<template>
  <v-container class="pa-0">
    <v-card
      :color="$UIConfig.stationPage.backGround"
      :elevation="$vuetify.breakpoint.smAndDown ? 0 : 4"
      :class="[
        'w-100 px-4 py-6 px-sm-6',
        { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }
      ]"
    >
      <v-card-title class="px-0 pt-0">
        <span class="custom-text-noto text-h5 default-content--text font-weight-bold">{{
          $t('achievement')
        }}</span>
      </v-card-title>
      <v-card-text class="pa-0">
        <!-- description -->
        <v-row
          no-gutters
          class="custom-text-noto text-caption grey-3--text pb-4"
          style="font-size: 12px !important"
        >
          <v-col cols="12">
            <span>{{ $t('achievements_desc1') }}</span></v-col
          >
          <v-col cols="12">
            <i18n path="achievements_desc2_1" tag="span">
              <template v-slot:xinstar>
                <span
                  v-if="$UIConfig.lock.otherStarCityOnlinePlatformsReminderDisabled"
                  class="grey-3--text"
                  >{{ $t('daily_list_desc2_2') }}</span
                >
                <span
                  v-else
                  class="primary--text"
                  style="cursor: pointer"
                  @click="openRedirectDialog"
                  >{{ $t('daily_list_desc2_2') }}</span
                >
              </template>
            </i18n>
          </v-col>
        </v-row>
        <!-- Achievements -->
        <achievement
          :user-name="userName"
          is-info-page
          :items-per-page-options="[10, 20, 30, -1]"
        ></achievement>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
  import { mapGetters } from 'vuex'
  import orientation from '@/mixins/orientation'
  export default {
    mixins: [orientation],
    name: 'PlayerAchievementsContent',
    components: {
      achievement: () => import('~/components/player_info/achievement')
    },
    data() {
      return {}
    },
    computed: {
      ...mapGetters('role', ['userName'])
    },
    watch: {
      orientation: {
        handler() {
          this.scrollToTop()
        }
      }
    },
    async created() {},
    methods: {
      openRedirectDialog() {
        this.$nuxt.$emit('root:redirectDialogStatus', { show: true, drawAnalytics: false })
      },
      scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'auto' })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-left {
      padding-left: calc(16px + env(safe-area-inset-left)) !important;
    }
    .notch-right {
      padding-right: calc(16px + env(safe-area-inset-right)) !important;
    }
    //sm
    @media (min-width: 600px) {
      .notch-left {
        padding-left: calc(24px + env(safe-area-inset-left)) !important;
      }
      .notch-right {
        padding-right: calc(24px + env(safe-area-inset-right)) !important;
      }
    }
  }
</style>
