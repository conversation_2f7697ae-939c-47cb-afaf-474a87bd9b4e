<template>
  <v-img
    :src="imageSource('2x')"
    :srcset="`${imageSource('1x')} 1x , ${imageSource('2x')} 2x`"
    :width="width"
    :height="height"
    @error="handleImageError"
  />
</template>

<script>
  import vipLevel from '@/mixins/vipLevel.js'
  import images from '~/mixins/images'
  export default {
    name: 'vipLevelIcon',
    mixins: [vipLevel, images],
    props: {
      width: {
        type: String,
        required: true,
        default: ''
      },
      height: {
        type: String,
        required: true,
        default: ''
      },
      vipLevel: {
        type: Number,
        required: true,
        default: 0
      }
    },
    watch: {},
    data() {
      return { useWebp: true }
    },
    created() {},
    mounted() {},
    computed: {
      vipLevelImgFileName({ $store }) {
        return $store.getters['role/vipLevelImgFileName']
      },
      vipLevelImg3xFileName({ $store }) {
        return $store.getters['role/vipLevelImg3xFileName']
      },
      imageSource() {
        return (dppx) => {
          let fileName = ''
          switch (dppx) {
            case '2x':
              fileName = this.vipLevelImg3xFileName[this.getvipLevel(this.vipLevel)]
              break
            default:
              fileName = this.vipLevelImgFileName[this.getvipLevel(this.vipLevel)]
              break
          }
          const extension = this.useWebp ? 'webp' : 'png'
          return this.getImage(`vip_level_icon/${fileName}.${extension}`)
        }
      }
    },
    methods: {
      handleImageError() {
        if (this.useWebp) {
          this.useWebp = false
        }
      }
    }
  }
</script>
