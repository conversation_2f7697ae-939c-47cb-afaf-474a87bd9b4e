<template>
  <div>
    <v-dialog
      v-model="showLocalPlayerInventoryDialogStatus"
      max-width="600"
      :fullscreen="breakpoint.xsOnly"
      persistent
      transition="dialog-transition"
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card class="d-flex flex-column h-100-percent" color="transparent">
        <customDialogTitle
          :title="$t('backpack_item_desc').toUpperCase()"
          @closeDialog="closeDialog"
        />
        <v-card-text class="pa-4 pa-sm-6 flex-grow-1">
          <v-row
            class="h-100-percent"
            :class="breakpoint.xsOnly ? 'flex-column no-gutters' : 'gutters-16'"
          >
            <v-col class="flex-grow-0 pb-4 pb-sm-0" sm="4">
              <v-card class="item-card" flat>
                <div class="item-content pa-2 pb-1">
                  <v-img :src="localItem.imgSrc" @error="errorImgHandler(localItem)">
                    <span
                      v-if="localItem.betAmount"
                      class="custom-text-noto text-body-1 font-weight-bold bet-amount"
                      :class="localItem.coinType === 1 ? 'bet-amount-gold' : 'bet-amount-silver'"
                      >{{ localItem.betAmount }}</span
                    >
                  </v-img>
                </div>
              </v-card>
            </v-col>
            <v-col class="d-flex flex-column overflow-y-hidden" sm="8">
              <span class="custom-text-noto text-h6 default-content--text pb-4 d-block">{{
                localItem.name
              }}</span>
              <div
                class="d-flex flex-column flex-grow-1 scrollbar-thin pr-2 overflow-y-auto item-info"
              >
                <div v-for="(part, index) in localItem.description" :key="index">
                  <div
                    v-if="part.title"
                    class="custom-text-noto text-subtitle-2 primary--text pb-2"
                  >
                    {{ part.title }}
                  </div>
                  <div
                    v-if="part.content"
                    class="custom-text-noto text-body-2 default-content--text"
                    :class="{ 'pb-2': index !== localItem.description.length - 1 }"
                  >
                    {{ part.content }}
                  </div>
                </div>
              </div>
              <v-divider class="my-4"></v-divider>
              <span class="custom-text-noto text-body-2 primary--text">
                {{ $t('backpack_item_amount') + '：' + formatCountWithCommas(localItem.count) }}
              </span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions class="pb-12 pb-sm-2">
          <i18n
            path="item_use_hint"
            tag="span"
            class="text-body-2 grey-3--text custom-text-noto my-0 mx-auto"
          >
            <template v-slot:download>
              <span
                class="primary--text text-decoration-underline cursor-pointer"
                @click="openRedirectDialog"
              >
                {{ $t('go_download') }}
              </span>
            </template>
          </i18n>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
  import cloneDeep from 'lodash/cloneDeep'
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'

  export default {
    name: 'PlayerInventoryDialog',
    mixins: [hiddenScrollHtml],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    props: {
      showPlayerInventoryDialogStatus: {
        type: Boolean,
        default: false
      },
      item: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        localItem: null,
        defaultImgWebp: require('~/assets/image/inventory_card_default.webp'),
        defaultImgPng: require('~/assets/image/inventory_card_default.png')
      }
    },
    watch: {
      item: {
        immediate: true,
        handler(newVal) {
          this.localItem = cloneDeep(newVal)
        }
      }
    },
    computed: {
      showLocalPlayerInventoryDialogStatus: {
        get() {
          return this.showPlayerInventoryDialogStatus
        },
        set(val) {
          this.$emit('update:showPlayerInventoryDialogStatus', val)
        }
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      openRedirectDialog() {
        this.$nuxt.$emit('root:redirectDialogStatus', { show: true, drawAnalytics: false })
      },
      closeDialog() {
        this.showLocalPlayerInventoryDialogStatus = false
      },
      errorImgHandler(errorItem) {
        // 情況 1：第一次載入失敗（原始圖片）
        if (errorItem.imgSrc === this.item.imgSrc) {
          // 嘗試將圖片格式從 .webp 替換為 .png
          this.localItem.imgSrc = errorItem.imgSrc.replace('.webp', '.png')
          return
        }

        // 情況 2：若目前不是預設圖片，則使用預設 webp 圖片
        if (errorItem.imgSrc !== this.defaultImgWebp && errorItem.imgSrc !== this.defaultImgPng) {
          this.localItem.imgSrc = this.defaultImgWebp
        }
        // 情況 3：如果預設 webp 圖片加載失敗，則嘗試預設 png 圖片
        else if (errorItem.imgSrc === this.defaultImgWebp) {
          this.localItem.imgSrc = this.defaultImgPng
        }
      },
      formatCountWithCommas(count) {
        if (typeof count !== 'number' || isNaN(count)) return '-'
        return count.toLocaleString('en-US')
      }
    }
  }
</script>
<style lang="scss" scoped>
  $black-with-opacity-20-color: map-get($colors, black-with-opacity-20);

  .gutters-16 {
    margin: -8px;
    > div {
      padding: 8px;
    }
  }
  .v-card__actions {
    background-color: $black-with-opacity-20-color;
  }
  .item-card {
    background: transparent;
    position: relative;
    padding: 5px;
    width: 174px;
    margin: 0 auto;
    @media (min-width: 600px) {
      width: 100%;
    }
    &::after {
      content: '';
      position: absolute;
      inset: 0; /* 等同於 top: 0; right: 0; bottom: 0; left: 0; */
      border-radius: inherit;
      padding: 1px; /* 邊框寬度 */
      background: linear-gradient(to right bottom, #855036, #a57259, #f5d5b5, #a57259, #855036);
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      pointer-events: none;
    }
    .item-content {
      background: linear-gradient(
        to right top,
        rgba(#855036, 0.3),
        rgba(#d9924b, 0.3),
        rgba(#855036, 0.3)
      );
      .visibility-hidden {
        visibility: hidden;
      }
      .bet-amount {
        position: absolute;
        bottom: 0;
        left: 8%;
        width: 100%;
        aspect-ratio: 2.7;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px !important;
        &.bet-amount-gold {
          color: #830000;
        }
        &.bet-amount-silver {
          color: #00326c;
        }
      }
    }
  }
  .item-info {
    @media (min-width: 600px) {
      height: 166px;
    }
  }
</style>
