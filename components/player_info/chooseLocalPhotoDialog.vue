<template>
  <v-dialog
    v-model="showChooseLocalPhotoDialogStatusTmp"
    width="344"
    height="393"
    content-class="rounded-lg"
    persistent
  >
    <v-card color="transparent">
      <customDialogTitle
        :title="$t('choose_role_avatar').toUpperCase()"
        @closeDialog="closeDialog"
      />
      <div id="choose-local-photo-card" class="scrollable">
        <v-card-text id="choose-photo-content" class="pa-4 pa-sm-6">
          <v-row
            no-gutters
            justify="start"
            align="center"
            id="sticker-box"
            class="rounded grey-5 py-1 px-1 rounded-lg"
            style="height: 235px; overflow: auto"
          >
            <!-- imgs -->

            <v-col
              v-for="(sticker, index) in getStickers"
              :key="sticker"
              cols="3"
              class="d-flex justify-center align-center py-1 px-1 sticker-item"
            >
              <v-img
                :src="sticker"
                max-height="64px"
                max-width="64px"
                :class="[
                  'cursor-pointer',
                  { 'gradient-primary-border': stickerSelected === index }
                ]"
                @click="stickerSelected = index"
              />
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions class="pt-0 px-4 pb-4 px-sm-6 pb-sm-6">
          <v-spacer />
          <!-- 取消 -->

          <v-btn class="mx-2 primary--text" elevation="0" color="primary" outlined @click="cancel">
            {{ $t('cancel').toUpperCase() }}
          </v-btn>
          <!-- 確定 -->

          <v-btn
            class="ml-2 button-content--text"
            :color="$UIConfig.defaultBtnColor"
            elevation="0"
            @click="prepareUpdateAvatar"
          >
            {{ $t('confirm').toUpperCase() }}
          </v-btn>
        </v-card-actions>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
  import uploadPhoto from '@/mixins/uploadPhoto.js'
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import images from '~/mixins/images'
  import scssLoader from '@/mixins/scssLoader.js'
  export default {
    name: 'chooseLocalPhotoDialog',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    mixins: [uploadPhoto, hiddenScrollHtml, images, scssLoader],
    props: {
      showChooseLocalPhotoDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        showChooseLocalPhotoDialogStatusTmp: this.showChooseLocalPhotoDialogStatus,
        stickerSelected: 0
      }
    },
    async created() {},
    watch: {
      showChooseLocalPhotoDialogStatus: {
        handler(val) {
          this.showChooseLocalPhotoDialogStatusTmp = val
        },
        immediate: true
      }
    },
    computed: {
      userName({ $store }) {
        return $store.getters['role/userName']
      },

      userDefalutAvatar({ $store }) {
        return $store.getters['role/userDefalutAvatar']
      },
      getStickers() {
        const images = this.userDefalutAvatar
        const imageUrls = images.map((x) => this.getImage('photo_stickers/' + x))
        return imageUrls
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showChooseLocalPhotoDialogStatus', false)
      },
      //取消
      cancel() {
        this.closeDialog()
        this.$nuxt.$emit('root:showUploadPhotoMethodDialogStatus', true)
      },
      async prepareUpdateAvatar() {
        let message = ''
        // 如果沒有選擇照片
        if (!this.getStickers[this.stickerSelected])
          message = '<p class="mb-0 text-wrap">' + this.$t('role_sticker_error') + '</p>'
        else {
          //上傳照片
          this.$nuxt.$loading.start()
          const avatar = this.getStickers[this.stickerSelected]
          this.uploadImgRes(this.userName, avatar)
            .then(async (result) => {
              if (result === 1) {
                this.closeDialog()
                const userData = {
                  userName: this.userName,
                  getCache: false
                }
                const thumbUrl = await this.$store.dispatch('role/getThumbUrl', userData)
                this.$store.commit('role/SET_THUMBURL', thumbUrl)
                this.$nuxt.$loading.finish()
              }
            })
            .catch((err) => {
              console.error(err)
            })
        }

        if (message !== '') {
          const title = this.$t('reminder')
          this.showNotyDialog(title, message)
        }
      }
    }
  }
</script>

<style lang="scss">
  $grey-5: map-get($colors, 'grey-5');
  #choose-photo-content {
    #sticker-box {
      &::-webkit-scrollbar {
        width: 4px !important;
      }
      &::-webkit-scrollbar-button {
        border-radius: 6px;
        height: 4px;
      }
      &::-webkit-scrollbar-track-piece {
        background: $grey-5 !important;
      }
    }
  }
  #choose-local-photo-card {
    &.scrollable {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable {
        max-height: calc(90svh - 52px);
      }
    }
  }
</style>
