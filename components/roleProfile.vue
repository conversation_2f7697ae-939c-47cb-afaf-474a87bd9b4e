<template>
  <div>
    <v-menu v-model="menuIsOpen" :close-on-content-click="false" :attach="characterAvatar">
      <template v-slot:activator="{ on, attrs }">
        <div
          class="mx-2 mb-2 character-avatar cursor-pointer"
          ref="characterAvatar"
          v-bind="attrs"
          v-on="on"
        >
          <character-avatar small no-upload />
          <badge
            right="-8px"
            bottom="-8px"
            icon="arrow_drop_down"
            :class="['arrow_drop', { open: menuIsOpen }]"
          ></badge>
        </div>
      </template>
      <playerMenu class="player-menu-mobilebar" @item-click="handleItemClick"></playerMenu>
    </v-menu>
  </div>
</template>

<script>
  export default {
    components: {
      playerMenu: () => import('~/components/player_info/menu'),
      characterAvatar: () => import('~/components/avatar'),
      badge: () => import('~/components/badge')
    },
    props: {},
    data() {
      // two-tier menu
      const myPoints = [
        {
          icon: 'savings',
          title: this.$t('my_xin_coin'),
          enable: false,
          action: () => this.toPage()
        }
      ]

      const services = [
        {
          icon: 'logout',
          title: this.$t('logout'),
          enable: true,
          action: () => this.$nuxt.$emit('root:showLogoutDialogStatus', true)
        }
      ]
      return {
        myPoints,
        services,
        menuIsOpen: false,
        overlay: true,
        absolute: true,
        characterAvatar: null
      }
    },
    computed: {
      playerProfile() {
        return this.$store.getters['role/playerProfile']
      }
    },
    mounted() {
      this.characterAvatar = this.$refs.characterAvatar.parentNode
    },
    methods: {
      toPage(link) {
        this.$router.push(this.localePath(link))
      },
      handleItemClick() {
        this.menuIsOpen = false
      }
    }
  }
</script>
<style lang="scss" scoped>
  .character-avatar {
    position: relative;

    & + .v-menu {
      display: initial;

      .v-menu__content {
        min-width: fit-content !important;
        top: 100% !important;
        left: initial !important;
        right: 24px !important;

        @media screen and (min-width: 960px) {
          right: 32px !important;
        }

        @media screen and (min-width: 1264px) {
          right: 40px !important;
        }
      }
    }
  }
  .arrow_drop {
    transition: transform 0.3s ease-in-out;
    &.open {
      transform: rotate(180deg);
    }
  }
</style>
