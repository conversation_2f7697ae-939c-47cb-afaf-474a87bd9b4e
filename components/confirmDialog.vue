<template>
  <v-dialog v-model="dialog" persistent :max-width="dialogWidth" content-class="rounded-lg">
    <v-card tile color="dialog-fill pa-4 pa-sm-6">
      <v-card-title class="d-flex justify-center pa-0">
        <slot name="title">{{ $t('hint').toUpperCase() }}</slot>
      </v-card-title>
      <v-card-text class="px-0 py-6 text-body-2">
        <slot></slot>
      </v-card-text>
      <v-card-actions class="pa-0">
        <slot name="actions">
          <v-row no-gutters justify="end">
            <v-col v-if="showCancel" :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
              <v-btn
                :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                elevation="0"
                text
                @click="close"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col
              :cols="!showCancel && breakpoint.xsOnly ? '12' : breakpoint.xsOnly ? '6' : 'auto'"
              :class="!showCancel && breakpoint.xsOnly ? '' : 'pl-2'"
            >
              <v-btn
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                @click="confirm"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </slot>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'confirmDialog',
    props: {
      value: Boolean,
      showCancel: {
        type: Boolean,
        default: true
      },
      action: {
        type: Function,
        default: () => {}
      },
      dialogWidth: {
        value: Number,
        default: 400
      }
    },
    computed: {
      dialog: {
        get() {
          return this.value
        },
        set(value) {
          this.$emit('input', value)
        }
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      close() {
        this.dialog = false
      },
      confirm() {
        this.action()
        this.close()
      }
    }
  }
</script>
