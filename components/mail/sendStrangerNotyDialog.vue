<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog
    v-model="sendStrangerNotyDialogStatusTmp"
    persistent
    max-width="380"
    content-class="rounded-lg"
  >
    <v-card color="transparent">
      <v-card-title class="custom-text-noto text-subtitle-1 pb-0 grey-1--text">
        {{ $t('reminder') }}<v-spacer />
        <v-icon @click="closeDialog" color="default-content--text"> mdi-close </v-icon>
      </v-card-title>
      <v-card-text class="px-6 pt-10">
        <v-row no-gutters>
          <span class="custom-text-noto text-body-2 default-content--text">{{
            $t('send_email_to_stranger')
          }}</span>
        </v-row>
        <v-row no-gutters class="mt-4">
          <playerInfoCard
            report
            :player-info="playerInfoTmp"
            style="width: 100%"
            class="elevation-4"
          />
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
  export default {
    name: 'SendStrangerNotyDialog',
    props: {
      sendStrangerNotyDialogStatus: { type: Boolean, default: false },
      playerInfo: {
        type: Object,
        default: {
          username: '',
          level: 0,
          vipLevel: 0,
          money: 0,
          thumbUrl: '',
          online: false
        }
      }
    },
    components: {
      playerInfoCard: () => import('@/components/player_info/easyPlayerInfo')
    },
    data() {
      return {
        sendStrangerNotyDialogStatusTmp: this.sendStrangerNotyDialogStatus,
        playerInfoTmp: this.playerInfo
      }
    },
    watch: {
      sendStrangerNotyDialogStatus: {
        handler(status) {
          this.sendStrangerNotyDialogStatusTmp = status
        },
        immediate: true
      },
      playerInfo: {
        handler(val) {
          // 先獲取空數據，以免顯示上一個用戶資訊
          this.playerInfo = {
            username: '',
            level: 0,
            levelVip: 0,
            money: 0,
            thumbUrl: '',
            online: false,
            guildName: ''
          }
          this.playerInfoTmp = val
        },
        deep: true
      }
    },
    computed: {},
    mounted() {},
    beforeDestroy() {},
    methods: {
      closeDialog() {
        this.$emit('update:sendStrangerNotyDialogStatus', false)
      }
    }
  }
</script>
