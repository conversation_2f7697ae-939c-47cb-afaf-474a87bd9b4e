<template>
  <v-dialog
    v-model="showAttachmentDescriptionStatus"
    :fullscreen="breakpoint.xsOnly"
    width="460px"
    scrollable
    persistent
    transition="dialog-transition"
    :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
  >
    <v-card height="560px" id="attach-desc-card" color="transparent">
      <customDialogTitle
        :title="$t('appendix').toUpperCase() + $t('description').toUpperCase()"
        :class="breakpoint.xsOnly ? '' : 'card-title-border-radius'"
        @closeDialog="closeDialog"
      />
      <v-card-text
        flat
        tile
        elevation="0"
        :class="[
          'align-center transparent pa-4 pa-sm-6',
          breakpoint.xsOnly ? 'scrollable-xs' : 'scrollable-sm'
        ]"
      >
        <span class="text-body-1 default-content--text custom-text-noto">
          {{ $t('conditions_noty') }}：
        </span>
        <ol class="text-body-1 default-content--text custom-text-noto pt-4">
          <i18n path="send_mail_description1" tag="li">
            <template v-slot:vip_level>
              <span class="primary--text">{{ $t('vip_level') }}</span>
            </template>
            <template v-slot:gold>
              <span class="primary--text">{{ $t('gold') }}</span>
            </template>
          </i18n>
          <li>
            {{ $t('send_mail_description2') }}
            <p class="mb-0 error--text">※ {{ $t('send_mail_description3') }}</p>
            <p class="mb-0 error--text">※ {{ $t('send_mail_description8') }}</p>
          </li>
          <i18n path="send_mail_description4" tag="li">
            <template v-slot:balance> <span class="primary--text">10,000</span></template>
            <template v-slot:xin_coin>
              <span class="primary--text">{{ $t('xin_coin') }}</span>
            </template>
          </i18n>
          <i18n path="send_mail_description5" tag="li">
            <template v-slot:beard_attachment>
              <span class="primary--text">{{ $t('beard_attachment') }}</span>
            </template>
          </i18n>
        </ol>
        <v-divider class="my-4" />
        <span class="text-body-1 primary--text custom-text-noto">
          {{ $t('matters_needing_attention') }}：
        </span>

        <ol class="text-body-1 primary--text pt-4">
          <li>{{ $t('send_mail_description6') }}</li>
          <li>{{ $t('send_mail_description7') }}</li>
          <li>{{ $t('send_mail_description9') }}</li>
        </ol>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
  export default {
    name: 'xinAttachmentDescriptionDialog',
    props: {
      showAttachmentDescriptionStatus: {
        type: Boolean,
        default: false
      }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    data() {
      return {}
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showAttachmentDescriptionStatus', false)
      }
    }
  }
</script>
<style scoped lang="scss">
  #attach-desc-card {
    max-height: 90vh;
    .scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    .scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
  }

  @supports (height: 90svh) {
    #attach-desc-card {
      max-height: 90svh;
    }
    #attach-desc-card .scrollable-sm {
      max-height: calc(90svh - 52px);
    }
    #attach-desc-card .scrollable-xs {
      max-height: calc(100svh - 52px);
    }
  }
</style>
