<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog v-model="showRemindPhoneOrientationDialogStatusTmp" persistent max-width="380">
    <v-card color="transparent">
      <v-card-title class="custom-text-h6 grey-1--text justify-center py-6">
        {{ $t('reminder') }}
      </v-card-title>
      <v-card-text class="custom-text-body-2 default-content--text pb-6">
        {{ $t('plz_use_vertically') }}
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
  export default {
    name: 'NotyDialog',
    props: {
      showRemindPhoneOrientationDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {
        showRemindPhoneOrientationDialogStatusTmp: this.showRemindPhoneOrientationDialogStatus
      }
    },
    watch: {
      showRemindPhoneOrientationDialogStatus: {
        handler(status) {
          this.showRemindPhoneOrientationDialogStatusTmp = status
        },
        immediate: true
      }
    },
    methods: {
      closeDialog() {
        this.$nuxt.$emit('root:showRemindPhoneOrientationDialogStatus', false)
      }
    }
  }
</script>
