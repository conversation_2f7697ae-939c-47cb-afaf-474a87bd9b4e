<template>
  <v-dialog
    v-model="showAttachmentDescriptionStatus"
    :fullscreen="breakpoint.xsOnly"
    width="460px"
    scrollable
    persistent
    transition="dialog-transition"
    :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
  >
    <v-card min-height="560px" color="transparent">
      <customDialogTitle
        :title="$t('appendix').toUpperCase() + $t('description').toUpperCase()"
        @closeDialog="closeDialog"
      />
      <v-card-text class="align-center rounded-0 transparent pa-4 pa-sm-6" flat tile elevation="0">
        <span class="text-body-1 default-content--text custom-text-noto">
          {{ $t('conditions_noty') }}：
        </span>
        <ol class="text-body-1 default-content--text custom-text-noto pl-4 pt-4">
          <i18n path="send_mail_description1" tag="li">
            <template v-slot:vip_level>
              <span class="primary--text">{{ $t('vip_level') }}</span>
            </template>
            <template v-slot:gold>
              <span class="primary--text">{{ $t('gold') }}</span>
            </template>
          </i18n>

          <i18n path="mjs_rule_1" tag="li">
            <template v-slot:sub>
              <span class="primary--text">{{ $t('mjs_rule_1_sub') }}</span>
            </template>
            <template v-slot:mjs>
              <span class="primary--text">{{ $t('mahjong_star') }}</span>
            </template>
          </i18n>

          <i18n path="mjs_rule_3" tag="li">
            <template v-slot:num>
              <span class="primary--text">10,000</span>
            </template>
            <template v-slot:xinCoin>
              <span class="primary--text">{{ $t('xin_coin') }}</span>
            </template>
          </i18n>

          <li>
            {{ $t('mjs_rule_2') }}
          </li>
        </ol>
        <v-divider class="my-4" />
        <span class="text-body-1 primary--text custom-text-noto">
          {{ $t('matters_needing_attention') }}：
        </span>

        <ol class="text-body-1 primary--text custom-text-noto pl-4 pt-4">
          <li>{{ $t('mjs_notice_1') }}</li>
          <li>{{ $t('mjs_notice_2') }}</li>
        </ol>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
  export default {
    name: 'mjsAttachmentDescriptionDialog',
    props: {
      showAttachmentDescriptionStatus: {
        type: Boolean,
        default: false
      }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    data() {
      return {}
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showAttachmentDescriptionStatus', false)
      }
    }
  }
</script>
