<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog
    v-model="showConfirmToMjsDialogStatus"
    persistent
    max-width="380"
    content-class="rounded-lg"
  >
    <v-card class="dialog-fill pa-4 pa-sm-4">
      <v-card-text class="default-content--text text-body-2 custom-text-noto px-0 pt-0 pb-6">
        <v-row no-gutters justify="center">
          <span class="text-h6">{{ $t('reminder') }}</span>
        </v-row>
        <v-row no-gutters class="pt-6">
          <v-col cols="12">
            <i18n path="mjs_reminder_1" tag="span">
              <template v-slot:name>
                <span> {{ name }}</span>
              </template>
            </i18n>
          </v-col>
          <v-col cols="12">
            <i18n path="mjs_reminder_2" tag="span">
              <template v-slot:count>
                <span> {{ xinCoin }}</span>
              </template>
            </i18n></v-col
          >
          <v-col cols="12">
            <i18n path="mjs_reminder_3" tag="span">
              <template v-slot:count>
                <span> {{ mjsCoin }}</span>
              </template>
            </i18n>
          </v-col>
          <v-col cols="12">
            <span class="primary--text">※{{ $t('mjs_reminder_4') }} </span>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
            <v-btn
              text
              elevation="0"
              color="primary"
              :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              @click="closeDialog"
            >
              {{ $t('cancel').toUpperCase() }}
            </v-btn>
          </v-col>
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              :loading="btnLoading"
              :disabled="btnLoading"
              elevation="0"
              color="primary-variant-1"
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              @click="confirmSendMjs"
            >
              {{ $t('sure').toUpperCase() }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  export default {
    name: 'ConfirmToMjsDialog',
    mixins: [hiddenScrollHtml],
    props: {
      showConfirmToMjsDialogStatus: { type: Boolean, default: false },
      name: { type: String, default: '' },
      xinCoin: { type: String, default: '' },
      mjsCoin: { type: String, default: '' }
    },
    data() {
      return {
        btnLoading: false
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    mounted() {},
    methods: {
      closeDialog() {
        this.$emit('update:showConfirmToMjsDialogStatus', false)
      },
      async confirmSendMjs() {
        const reqData = this.$wsPacketFactory.xinToMahjong(this.xinCoin)
        this.$wsClient.send(reqData)
        this.btnLoading = true
        try {
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.MAHJONG)
          })
          if (res.state) {
            this.$notify.success(this.$t('msj_send_success'))
            this.closeParentDialog()
          } else {
            this.$notify.error(res.msg)
          }
        } catch (error) {
          //信件寄送失敗
          this.$store.dispatch('easyDialog/setDialog', {
            title: this.$t('reminder'),
            message: this.$t('msj_send_error')
          })
          this.$nuxt.$emit('root:showNotyDialogStatus', true)
          this.closeParentDialog()
        }
        this.btnLoading = false
        this.$emit('update:showConfirmToMjsDialogStatus', false)
      },
      closeParentDialog() {
        this.$emit('closeDialog')
      }
    }
  }
</script>
