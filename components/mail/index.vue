<template>
  <div>
    <v-dialog
      v-model="mailDialogStatusTmp.show"
      :fullscreen="$vuetify.breakpoint.xsOnly"
      width="800px"
      scrollable
      persistent
      transition="dialog-transition"
      content-class="mail-dialog"
    >
      <v-card class="dialog-fill" height="560px">
        <customDialogTitle :title="$t('mail').toUpperCase()" @closeDialog="closeDialog" />

        <!-- action bar for not mobile -->
        <v-card
          v-if="!$vuetify.breakpoint.xsOnly"
          class="d-flex rounded-b-0 dialog-fill-2"
          flat
          tile
          elevation="0"
          color="grey-6"
        >
          <v-card class="pa-4 rounded-0" color="transparent" outlined tile>
            <v-btn elevation="0" class="mx-2" outlined color="primary" @click="sendNewMail">
              <span>
                {{ $t('send_mail').toUpperCase() }}
              </span>
            </v-btn>
            <v-btn
              :disabled="delReadBtnStatus === false"
              elevation="0"
              class="mx-2"
              outlined
              color="primary"
              @click="confirmDeletionAllStatus = true"
            >
              <span>
                {{ $t('delete_read').toUpperCase() }}
              </span>
            </v-btn>
            <v-btn
              :disabled="allReceiveBtnStatus === false"
              elevation="0"
              class="mx-2"
              outlined
              color="primary"
              @click="doReceiveAll"
            >
              <span>
                {{ $t('receive_all').toUpperCase() }}
              </span>
            </v-btn>
          </v-card>
        </v-card>
        <!-- action bar for mobile -->
        <v-card
          v-else-if="!mobile.showMailContentstatus"
          class="d-flex rounded-b-0 dialog-fill-2"
          flat
          tile
          elevation="0"
        >
          <v-card class="pa-4 rounded-0" color="transparent" outlined tile>
            <v-btn
              :disabled="delReadBtnStatus === false"
              elevation="0"
              class="mx-2"
              outlined
              color="primary"
              @click="confirmDeletionAllStatus = true"
            >
              <span>
                {{ $t('delete_read').toUpperCase() }}
              </span>
            </v-btn>
            <v-btn
              :disabled="allReceiveBtnStatus === false"
              elevation="0"
              class="mx-2"
              outlined
              color="primary"
              @click="doReceiveAll"
            >
              <span>
                {{ $t('receive_all').toUpperCase() }}
              </span>
            </v-btn>
          </v-card>
        </v-card>
        <!-- list & content -->
        <v-container
          fill-height
          class="pa-0"
          :style="{ overflow: $vuetify.breakpoint.xsOnly ? 'hidden' : 'unset' }"
        >
          <v-row
            id="mail-list-mobile"
            no-gutters
            class="fill-height"
            :style="{
              'flex-wrap': $vuetify.breakpoint.xsOnly ? 'wrap' : 'nowrap',
              overflow: $vuetify.breakpoint.xsOnly ? 'auto' : ''
            }"
          >
            <!-- mail list -->
            <v-col
              v-if="!mobile.showMailContentstatus"
              sm="5"
              md="5"
              lg="5"
              id="mail-list-container"
              :style="{
                'min-width': $vuetify.breakpoint.xsOnly ? ' 100px' : 'unset'
              }"
            >
              <v-list
                v-if="
                  (showMailDataArrTmp && showMailDataArrTmp.length != 0) ||
                  !$vuetify.breakpoint.xsOnly
                "
                id="mail-list"
                two-line
                shaped
                color="dialog-fill-2"
                class="pa-0"
                :style="{
                  height: $vuetify.breakpoint.xsOnly ? '100%' : '436px',
                  'overflow-y': 'auto'
                }"
              >
                <!-- 避免在mandatory的屬性導致在未選擇信件的情況下硬是設定一筆，
                  mandatory屬性是為了不讓使用者點選同一筆第二次會取消active的狀態 -->
                <v-list-item-group
                  v-model="selection"
                  :mandatory="getSingleMail.content !== $t('please_select_letter')"
                  active-class="primary--text"
                >
                  <template v-for="item in showMailDataArrTmp">
                    <v-list-item
                      class="pl-2 pr-4 mr-3"
                      height="82px"
                      :key="item.mailId"
                      :value="item.mailId"
                      @click="doShowMailContent(item.mailId)"
                    >
                      <v-list-item-content>
                        <v-list-item-title class="pl-2">
                          <div class="custom-between">
                            <div class="mr-2">
                              <!-- 左側內容 -->
                              <v-badge
                                v-if="!item.isRead"
                                class="pt-1 pr-1"
                                :color="!item.isRead ? 'error' : 'transparent'"
                                dot
                                left
                              />
                            </div>
                            <div class="right text-left mail-list-icon-title">
                              <!-- 右側內容 -->
                              <span
                                class="custom-text-noto text-caption text-overflow"
                                :class="$UIConfig.mailIndex.mailTitleClass"
                              >
                                {{ item.itemType > 1 ? $t('mail_cant_get_noty1') : '' }}
                              </span>
                              <!--使用clac避免單純使用%數 會導致字被切到 text-overflow出不來-->
                              <span
                                class="text-overflow custom-text-body-1 mail-list-icon-title-text"
                              >
                                {{ convertMessage(item.from) }}
                              </span>
                            </div>
                          </div>
                        </v-list-item-title>
                        <v-list-item-subtitle
                          class="pl-4 custom-text-noto text-caption white--text mail-list-icon-sub-title"
                        >
                          {{ convertMessage(item.title) }}
                        </v-list-item-subtitle>
                      </v-list-item-content>
                      <v-list-item-action>
                        <v-list-item-action-text
                          :class="doTimeFormat(item.expire, item.isRead).style"
                        >
                          {{ doTimeFormat(item.expire, item.isRead).content }}
                        </v-list-item-action-text>
                        <v-list-item-action-text>
                          <span
                            v-if="item.itemType > 0 && !item.isRead"
                            class="material-symbols-outlined letter-info--text"
                          >
                            attach_file
                          </span>
                          <span
                            v-else-if="item.itemType > 0 && item.isRead"
                            class="material-symbols-outlined default-content--text mail-attach-file"
                          >
                            attach_file
                          </span>
                        </v-list-item-action-text>
                      </v-list-item-action>
                    </v-list-item>
                  </template>
                </v-list-item-group>
              </v-list>
              <v-row v-else no-gutters align="center" justify="center" class="fill-height">
                <span class="grey-3--text custom-text-noto text-body-2 no-mails-font">{{
                  $t('no_mails')
                }}</span>
              </v-row>
            </v-col>
            <!-- mail content -->
            <template v-if="!$vuetify.breakpoint.xsOnly || mobile.showMailContentstatus">
              <!-- mail action bar for mobile -->
              <v-col
                v-if="mobile.showMailContentstatus"
                cols="12"
                class="dialog-fill-2 mail-action-bar"
              >
                <v-row no-gutters align="center" class="mail-action-bar-min-height">
                  <v-col cols="8">
                    <v-row no-gutters>
                      <div class="d-flex align-center pl-3">
                        <span
                          class="material-symbols-outlined cursor-pointer default-content--text mail-action-bar-arrow"
                          @click="backToMailList()"
                        >
                          chevron_left
                        </span>
                      </div>
                    </v-row>
                  </v-col>
                  <v-col cols="4">
                    <v-row no-gutters justify="end" class="pr-4">
                      <div class="d-flex align-center">
                        <div
                          class="d-flex align-center cursor-pointer mx-2"
                          :class="{
                            'cursor-pointer': isFromOffical ? hasLink : true,
                            'default-content--text': isFromOffical ? hasLink : true,
                            'btn-disable--text': isFromOffical ? !hasLink : false
                          }"
                          @click="
                            if (isFromOffical) doRedirectPage(getSingleMail.link)
                            if (!isFromOffical) replyNewMail(getSingleMail.from)
                          "
                        >
                          <span
                            v-if="isFromOffical"
                            class="material-symbols-outlined mr-3 mail-action-button-icon"
                          >
                            open_in_new
                          </span>
                          <span
                            v-else
                            class="material-symbols-outlined mr-3 mail-action-button-icon"
                          >
                            reply
                          </span>
                          <span class="px-0 mx-0 mail-action-button-text">{{
                            this.$t(isFromOffical ? 'goto' : 'write_in_reply')
                          }}</span>
                        </div>
                        <div
                          class="d-flex align-center mx-2"
                          :class="{
                            'default-content--text': getSingleMail.isRead,
                            'btn-disable--text': !getSingleMail.isRead,
                            'cursor-pointer': getSingleMail.isRead
                          }"
                          @click="
                            () => {
                              if (
                                getSingleMail.isRead ||
                                $moment()
                                  .subtract(serverTimestamp, 'milliseconds')
                                  .isAfter(getSingleMail.expire)
                              )
                                confirmDeletionStatus = true
                            }
                          "
                        >
                          <span class="material-symbols-outlined mx-3 mail-action-button-icon">
                            delete
                          </span>
                          <span class="px-0 mx-0 mail-action-button-text">{{
                            this.$t('delete')
                          }}</span>
                        </div>
                      </div>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <!-- mail content -->
              <!-- no selected -->
              <v-col
                v-if="
                  getSingleMail.content === $t('please_select_letter') ||
                  getSingleMail.content === $t('no_mails')
                "
                :style="{
                  height: $vuetify.breakpoint.xsOnly ? '100%' : '436px'
                }"
                cols="12"
                sm="7"
                md="7"
                lg="7"
                class="d-flex pa-2 justify-center align-center"
              >
                <pre
                  class="grey-3--text custom-text-noto text-body-2 no-select-font"
                  v-text="convertMessage(getSingleMail.content)"
                />
              </v-col>
              <!-- selected -->
              <v-col
                v-else
                cols="12"
                sm="7"
                md="7"
                lg="7"
                class="px-6 py-4 px-sm-4 pb-sm-4 pt-sm-3 d-flex flex-column"
                :style="{
                  height: $vuetify.breakpoint.xsOnly ? 'calc(100% - 56px)' : '436px'
                }"
              >
                <!-- mail title && action bar-->
                <v-card
                  min-height="60px"
                  class="d-flex py-2 mail-title"
                  color="transparent"
                  flat
                  tile
                >
                  <!-- mail title -->
                  <div
                    class="flex-column mr-auto"
                    :style="{
                      width: mobile.showMailContentstatus ? '100%' : 'unset'
                    }"
                  >
                    <!-- mail action btn for not mobile-->
                    <template v-if="!mobile.showMailContentstatus">
                      <div class="d-flex align-center pb-2">
                        <div
                          class="d-flex align-center cursor-pointer mx-2"
                          :class="{
                            'cursor-pointer': isFromOffical ? hasLink : true,
                            'default-content--text': isFromOffical ? hasLink : true,
                            'btn-disable--text': isFromOffical ? !hasLink : false
                          }"
                          @click="
                            if (isFromOffical) doRedirectPage(getSingleMail.link)
                            if (!isFromOffical) replyNewMail(getSingleMail.from)
                          "
                        >
                          <span
                            v-if="isFromOffical"
                            class="material-symbols-outlined mr-3 mail-button-icon-size"
                          >
                            open_in_new
                          </span>
                          <span v-else class="material-symbols-outlined mr-3 mail-button-icon-size">
                            reply
                          </span>
                          <span class="px-0 mx-0 mail-button-text">{{
                            this.$t(isFromOffical ? 'goto' : 'write_in_reply')
                          }}</span>
                        </div>
                        <div
                          class="d-flex align-center mx-2"
                          :class="{
                            'default-content--text': getSingleMail.isRead,
                            'btn-disable--text': !getSingleMail.isRead,
                            'cursor-pointer': getSingleMail.isRead
                          }"
                          @click="
                            () => {
                              if (
                                getSingleMail.isRead ||
                                $moment()
                                  .subtract(serverTimestamp, 'milliseconds')
                                  .isAfter(getSingleMail.expire)
                              )
                                confirmDeletionStatus = true
                            }
                          "
                        >
                          <span class="material-symbols-outlined mx-3 mail-button-icon-size">
                            delete
                          </span>
                          <span class="px-0 mx-0 mail-button-text">{{ this.$t('delete') }}</span>
                        </div>
                      </div>
                    </template>
                    <div class="pa-0 my-1">
                      <span class="text--subtitle-1 primary--text">
                        {{ convertMessage(getSingleMail.title) }}
                      </span>
                    </div>
                    <div class="pa-0 my-1">
                      <span class="text-body-2 grey-3--text custom-text-noto">
                        {{ $t('sender') + ': ' + convertMessage(getSingleMail.from) }}
                      </span>
                    </div>
                    <div class="pa-0 my-1">
                      <span class="text-sm-body-2 warning--text">
                        {{ getRemainText(getSingleMail.expire) }}
                      </span>
                    </div>
                  </div>
                </v-card>
                <v-divider class="pa-0" />
                <!-- mail content -->
                <v-row class="py-2 mail-text mail-content" no-gutters>
                  <pre
                    class="default-content--text custom-text-noto text-body-2 mail-content-text mail-content-font-size"
                    v-text="convertMessage(getSingleMail.content)"
                  />
                </v-row>
                <v-divider class="pb-2" v-if="getSingleMail.itemType > 0" />
                <!-- gift notice -->
                <div v-if="checkMailCanOpen(getSingleMail)" class="d-flex pb-1 mail-file">
                  <span class="text-caption grey-3--text custom-text-noto">
                    {{ $t('remarks_on_sending_gifts').toUpperCase() }}
                  </span>
                </div>
                <!-- mail attachment -->
                <v-card
                  v-if="getSingleMail.itemType > 0"
                  :height="
                    checkMailCanOpen(getSingleMail)
                      ? '84px'
                      : $vuetify.breakpoint.xsOnly
                      ? '76px'
                      : '56px'
                  "
                  class="info-card mail-content-card-rounded"
                  flat
                >
                  <v-card-text class="pa-2 d-flex align-center fill-height">
                    <template v-if="checkMailCanOpen(getSingleMail)">
                      <div class="d-flex align-center">
                        <v-img
                          class="mr-2"
                          :src="getImage('<EMAIL>')"
                          :srcset="getSrcset('coin')"
                          width="24"
                          height="24"
                        />
                        <p class="text-lg-h mb-0 default-content--text">
                          {{ $t('xin_coin') }} <br />
                          <span class="primary--text">
                            {{ formatPrice(getSingleMail.count) }}
                          </span>
                        </p>
                      </div>
                      <div class="ml-auto">
                        <v-btn
                          :disabled="
                            getSingleMail.isRead ||
                            $moment()
                              .subtract(serverTimestamp, 'milliseconds')
                              .isAfter(getSingleMail.expire)
                          "
                          elevation="0"
                          rounded
                          class="mt-1 button-content--text"
                          :class="{
                            'font-weight-regular': $UIConfig.mailIndex.fontWeightRegular,
                            'gradient-primary':
                              !(getSingleMail.itemType > 0 && getSingleMail.isRead) &&
                              $moment()
                                .subtract(serverTimestamp, 'milliseconds')
                                .isBefore(getSingleMail.expire),
                            'grey-4': getSingleMail.itemType > 0 && getSingleMail.isRead
                          }"
                          @click="doReceive"
                        >
                          <span
                            v-if="
                              checkMailCanOpen(getSingleMail) &&
                              !getSingleMail.isRead &&
                              $moment()
                                .subtract(serverTimestamp, 'milliseconds')
                                .isBefore(getSingleMail.expire)
                            "
                            v-text="$t('claim')"
                          />
                          <span
                            v-else-if="
                              $moment()
                                .subtract(serverTimestamp, 'milliseconds')
                                .isAfter(getSingleMail.expire)
                            "
                            v-text="$t('expired')"
                          />
                          <span
                            v-else-if="getSingleMail.itemType > 0 && getSingleMail.isRead"
                            v-text="$t('claimed')"
                          />
                        </v-btn>
                      </div>
                    </template>
                    <template v-else>
                      <span
                        v-if="$UIConfig.lock.otherStarCityOnlinePlatformsReminderDisabled"
                        class="grey-3--text custom-text-noto text-subtitle-2 mail-content-font-size"
                      >
                        <i18n path="go_download_plz">
                          <template v-slot:mark>＊</template>
                          <template v-slot:download>
                            {{ $t('go_download') }}
                          </template>
                        </i18n>
                      </span>
                      <span
                        v-else
                        class="grey-3--text custom-text-noto text-subtitle-2 mail-content-font-size"
                      >
                        <i18n path="go_download_plz">
                          <template v-slot:mark>＊</template>
                          <template v-slot:download>
                            <a
                              class="mail-attachment-text-underline mail-content-link"
                              @click="goDownload"
                              >{{ $t('go_download') }}</a
                            >
                          </template>
                        </i18n>
                      </span>
                    </template>
                  </v-card-text>
                </v-card>
              </v-col>
            </template>
            <!-- send mail fab for mobile -->
            <v-btn
              v-if="$vuetify.breakpoint.xsOnly && !mobile.showMailContentstatus"
              class="mx-2 mail-send-mail-fab-mobile"
              elevation="0"
              fab
              dark
              :color="$UIConfig.mailIndex.mobileSendMailBtnColor"
              @click="sendNewMail"
            >
              <v-icon color="black">mdi-pencil-outline </v-icon>
            </v-btn>
          </v-row>
          <!-- 刪除已讀再次確認區域 -->
          <v-row justify="center">
            <v-dialog
              v-model="confirmDeletionAllStatus"
              persistent
              :max-width="$UIConfig.mailIndex.confirmDeleteDialogWidth"
            >
              <v-card color="transparent" class="py-2">
                <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text">
                  {{ $t('reminder') }}
                </v-card-title>
                <v-card-text class="px-5 pb-0 default-content--text">
                  {{ $t('confirm_deletion_warning1') }}
                </v-card-text>
                <v-card-text class="pl-5 pt-0 warning--text">
                  {{ $t('confirm_deletion_warning2') }}
                </v-card-text>
                <v-card-actions>
                  <v-spacer />
                  <v-btn
                    class="mx-2 default-content--text"
                    elevation="0"
                    text
                    @click="confirmDeletionAllStatus = false"
                  >
                    {{ $t('cancel').toUpperCase() }}
                  </v-btn>
                  <v-btn
                    class="mx-2 button-content--text"
                    :color="$UIConfig.mailIndex.delieteReadAllBtnColor"
                    elevation="0"
                    @click="doDeleteReadAll"
                  >
                    {{ $t('sure').toUpperCase() }}
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-dialog>
          </v-row>
        </v-container>
      </v-card>
    </v-dialog>
    <mailSendDialog
      v-if="showMailSendDialogStatus"
      :show-mail-send-dialog-status.sync="showMailSendDialogStatus"
      :mail-dialog-status.sync="mailDialogStatusTmp"
      :recipient="defaultRecipient"
    />
    <mailConfirmDeleteDialog
      v-if="confirmDeletionStatus"
      :confirm-deletion-status.sync="confirmDeletionStatus"
      @doDeleteRead="doDeleteRead"
    />
    <remindBindPhoneDialog
      v-if="showRemindBindPhoneDialogStatus"
      :show-remind-bind-phone-dialog-status.sync="showRemindBindPhoneDialogStatus"
    />
    <remindPhoneOrientationDialog
      v-if="showRemindPhoneOrientationDialogStatus"
      :show-remind-phone-orientation-dialog-status.sync="showRemindPhoneOrientationDialogStatus"
    />
  </div>
</template>
<script>
  import mailMixin from '~/mixins/mail'
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import relationship from '@/mixins/relationship.js'
  import translator from '~/mixins/translator'
  import images from '~/mixins/images'
  import converter from '~/mixins/converter'
  const STATION = process.env.STATION
  export default {
    name: 'MailIndex',
    mixins: [mailMixin, hiddenScrollHtml, relationship, translator, images, converter],
    components: {
      mailSendDialog: () => import(`~/components_station/${STATION}/mail/mailSendDialog`),
      mailConfirmDeleteDialog: () => import('~/components/mail/mailConfirmDeleteDialog'),
      remindBindPhoneDialog: () => import('~/components/mail/remindBindPhoneDialog.vue'),
      customDialogTitle: () => import('~/components/customDialogTitle'),
      remindPhoneOrientationDialog: () =>
        import('~/components/mail/remindPhoneOrientationDialog.vue')
    },
    props: {
      mailDialogStatus: { type: Object, default: { show: false, name: '' } }
    }
  }
</script>

<style lang="scss">
  .custom-between {
    display: flex;
  }

  .left {
    flex: 1;
  }

  .mail-content-card-rounded {
    gap: 10px;
  }

  .right {
    display: flex;
    flex-direction: column;
  }
  .mail-content-text {
    word-break: break-all;
    white-space: pre-wrap;
  }
  .mail-dialog {
    .mail-content {
      flex: 1;
      overflow: auto !important;
      // 會有預設樣式覆蓋overflow，故下!important
    }
    .mail-button-text {
      font-size: 15px;
      white-space: nowrap;
    }
    .mail-button-icon-size {
      font-size: 18px;
    }
    .mail-list-icon-title {
      width: 100%;
    }
    .mail-list-icon-title-text {
      line-height: 1.3rem;
      width: calc(100% - 1px);
    }
    .mail-list-icon-sub-title {
      opacity: 0.7;
    }
    .mail-attach-file {
      opacity: 0.3;
    }
    .no-mails-font {
      font-size: 14px !important;
    }
    .mail-action-bar {
      height: 56px;
    }
    .mail-action-bar-min-height {
      min-height: 56px;
    }
    .mail-action-bar-arrow {
      font-size: 25px;
    }
    .mail-action-button-icon {
      font-size: 18px;
    }
    .mail-action-button-text {
      font-size: 15px;
      white-space: nowrap;
    }
    .no-select-font {
      font-size: 14px !important;
    }
    .mail-attachment-text-underline {
      text-decoration: underline;
    }
    .mail-content-font-size {
      font-size: 14px !important;
    }
    .mail-content-link {
      padding: 0 2px;
    }
    .mail-send-mail-fab-mobile {
      position: absolute;
      bottom: 16px;
      right: 16px;
    }
  }
</style>
