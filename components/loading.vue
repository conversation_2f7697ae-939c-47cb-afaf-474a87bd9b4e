<template>
  <v-row class="components-loading" :class="{ 'loading-end': !loading }">
    <div class="loading-img" />
  </v-row>
</template>

<script>
  export default {
    data() {
      return {
        loading: true,
        percent: 0
      }
    },
    mounted() {
      this.loading = false
    },
    methods: {
      start() {
        this.loading = true
      },
      finish() {
        this.loading = false
      },
      fail() {
        this.loading = false
      },
      set(num) {
        this.loading = true
        this.percent = Math.min(100, Math.max(0, Math.floor(num)))
      }
    }
  }
</script>
