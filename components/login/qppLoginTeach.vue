<template>
  <v-dialog
    v-model="showQppLoginTechDialogTmp"
    :max-width="$vuetify.breakpoint.smAndUp ? '80vh' : '100vw'"
    persistent
    transition="dialog-transition"
    hide-overlay
  >
    <v-card max-height="100%">
      <div v-if="$vuetify.breakpoint.smAndUp">
        <v-img v-for="(item, i) in selectImg()" :key="i" :src="item">
          <v-card class="text-right pa-2 rounded-0" color="transparent" elevation="0">
            <v-btn icon @click="closeDialog">
              <v-icon color="black"> mdi-close </v-icon>
            </v-btn>
          </v-card>
        </v-img>
      </div>
      <div v-else>
        <v-carousel
          height="auto"
          cycle
          hide-delimiter-background
          show-arrows-on-hover
          hide-delimiters
        >
          <v-carousel-item v-for="(item, i) in selectImg()" :key="i" :src="item">
            <v-card class="text-right pa-2 rounded-0" color="transparent" elevation="0">
              <v-btn icon @click="closeDialog">
                <v-icon color="black"> mdi-close </v-icon>
              </v-btn>
            </v-card>
          </v-carousel-item>
        </v-carousel>
      </div>
    </v-card>
  </v-dialog>
</template>
<script>
  export default {
    name: 'QppLoginTeach',
    props: {
      showQppLoginTechDialog: { type: Boolean, default: false }
    },
    data() {
      return {
        showQppLoginTechDialogTmp: this.showQppLoginTechDialog,
        showImg: [],
        img: {
          lg: {
            'zh-tw': process.env.IMAGE_URL + '/qpp/zh-tw/qpp_login_lg.webp',
            'zh-cn': process.env.IMAGE_URL + 'qpp/zh-cn/qpp_login_lg.webp'
          },
          md: {
            'zh-tw': [
              process.env.IMAGE_URL + '/qpp/zh-tw/qpp_login_md_1.webp',
              process.env.IMAGE_URL + '/qpp/zh-tw/qpp_login_md_2.webp'
            ],
            'zh-cn': [
              process.env.IMAGE_URL + '/qpp/zh-cn/qpp_login_md_1.webp',
              process.env.IMAGE_URL + '/qpp/zh-cn/qpp_login_md_2.webp'
            ]
          }
        }
      }
    },
    computed: {},
    watch: {
      showQppLoginTechDialog: {
        handler(status) {
          this.showQppLoginTechDialogTmp = status
        }
      }
    },
    mounted() {},
    methods: {
      closeDialog() {
        this.$emit('update:showQppLoginTechDialog', false)
      },
      selectImg() {
        let img = []
        if (this.$vuetify.breakpoint.smAndUp) {
          img.push(this.img.lg[this.$i18n.locale])
        } else {
          img = this.img.md[this.$i18n.locale]
        }
        return img
      }
    }
  }
</script>
