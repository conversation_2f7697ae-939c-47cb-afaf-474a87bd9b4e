<!-- eslint-disable vue/valid-v-html -->
<!-- eslint-disable vue/no-v-html -->

<template>
  <v-dialog
    v-model="showDeviceWhiteDialogStatusTmp"
    persistent
    max-width="400px"
    content-class="rounded-lg"
  >
    <v-card class="transparent">
      <v-card-title class="gradient-primary-left">
        <v-row no-gutters justify="center">
          <span
            class="button-content--text custom-text-noto text-subtitle-1 font-weight-regular"
            style="font-size: 16px !important"
          >
            {{ $t('device_white_validate').toUpperCase() }}
          </span>
        </v-row>
      </v-card-title>
      <v-card-text class="pa-4 pa-sm-6">
        <v-row no-gutters class="align-center justify-left">
          <v-col cols="12">
            <v-text-field
              v-model="validateCode"
              v-validate="'required|validate_length:5'"
              type="number"
              name="verify_code"
              :error-messages="errors.first('verify_code')"
              :label="$t('verify_code') + '*'"
              :data-vv-as="$t('verify_code')"
              autocomplete="off"
              filled
              shaped
              hide-spin-buttons
            />
            <p v-if="companyPhoneNumber !== ''" class="text-sm-body-2 default-content--text">
              {{ $t('device_white_noty1', { phoneNumber: companyPhoneNumber }) }}
            </p>
            <p v-if="serviceMail !== ''" class="text-sm-body-2 default-content--text">
              {{ $t('device_white_noty3', { mail: serviceMail }) }}
            </p>
          </v-col>
        </v-row>
      </v-card-text>

      <v-card-actions class="pt-0 px-4 pb-4 px-sm-6 pb-sm-6">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
            ><v-btn
              :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              elevation="0"
              color="primary-variant-1"
              text
              @click="closeDialog"
            >
              {{ $t('cancel').toUpperCase() }}
            </v-btn></v-col
          >
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              :loading="loadingLoginBtnStatus"
              :disabled="disableLoginBtnStatus"
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              elevation="0"
              @click="loginHandler"
            >
              {{ $t('sure').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import analytics from '@/mixins/analytics.js'
  import preLoginAction from '@/mixins/preLoginAction.js'
  const STATION = process.env.STATION
  export default {
    name: 'DeviceWhiteDialog',
    mixins: [hiddenScrollHtml, analytics, preLoginAction],
    props: {
      showDeviceWhiteDialogStatus: { type: Boolean, required: true, default: false }
    },
    data() {
      return {
        showDeviceWhiteDialogStatusTmp: this.showDeviceWhiteDialogStatus,
        validateCode: null,
        disableLoginBtnStatus: false,
        loadingLoginBtnStatus: false
      }
    },
    computed: {
      phoneNumber({ $store }) {
        return $store.getters['role/phoneNumber']
      },
      companyPhoneNumber({ $store }) {
        return $store.getters[`${STATION}/companyInfo/phoneNumber`]
      },
      serviceMail({ $store }) {
        return $store.getters[`${STATION}/companyInfo/serviceMail`]
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showDeviceWhiteDialogStatus: {
        handler(status) {
          this.showDeviceWhiteDialogStatusTmp = status
        }
      },
      validateCode: {
        handler(val) {
          if (val.length === 5) {
            this.loginHandler()
          }
        }
      }
    },
    mounted() {},
    methods: {
      closeDialog() {
        this.validateCode = ''
        this.$validator.reset('verify_code')
        this.$nuxt.$emit('root:showDeviceWhiteDialogStatus', false)
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      async loginHandler() {
        const validate = await this.$validator.validate('verify_code')
        if (validate) {
          const title = this.$t('reminder')
          const validateCode = this.validateCode
          const reqData = this.$wsPacketFactory.verifyDevice(validateCode)
          // 避免剛登入時連續跳出 noty info 的設計，先透過該值關閉通知
          this.$store.commit('mail/SET_FIRST_RECIVE', true)
          let message = ''
          this.$wsClient.send(reqData)
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.LOGIN.ID)
          })

          this.loadingLoginBtnStatus = true
          this.disableLoginBtnStatus = true
          if (
            res.type === 0 &&
            res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.PLAYER_INFO.ID
          ) {
            // 先發送前三個服務的加入請求
            const initialServices = [
              this.$xinConfig.LOTTERY_SERVICE.ID, // 進入摸彩服務
              this.$xinConfig.GAME_SERVICE.ID, // 進入遊戲服務
              this.$xinConfig.SOCIAL_SERVICE.ID // 進入社群服務
            ]

            initialServices.forEach((serviceId) => {
              this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(serviceId))
              this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
                serviceId,
                enable: true,
                connected: true
              })
            })

            // 等待 SOCIAL_SERVICE 確認
            await this.$xinUtility
              .waitEvent(
                this.$wsClient.receivedListeners,
                (data) =>
                  data.protocolId === this.$xinConfig.PROTOCOL_ID.SERVICE &&
                  data.serviceId === this.$xinConfig.SOCIAL_SERVICE.ID
              )
              .then(() => {
                this.$wsClient.send(this.$wsPacketFactory.initMail())
                // 延遲五秒後開啟新信件通知
                setTimeout(() => {
                  this.$store.commit('mail/SET_FIRST_RECIVE', false)
                }, 5000)
              })
              .catch((err) => {
                console.log('SOCIAL_SERVICE ERROR:', err)
              })
            // 初始化角色資料
            await this.$store.dispatch('role/profileInit', res)
            // 再加入公會服務
            this.$wsClient.send(
              this.$wsPacketFactory.getServiceJoin(this.$xinConfig.GUILD_SERVICE.ID)
            )
            this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
              serviceId: this.$xinConfig.GUILD_SERVICE.ID,
              enable: true,
              connected: true
            })
            if (
              Object.prototype.hasOwnProperty.call(res, 'lastLogoutTime') &&
              res.lastLogoutTime === '1900/01/01 00:00:00'
            ) {
              this.createRoleAnalytics()
            }

            //取得與伺服器時間差
            await this.$store.dispatch('xinProtocol/getServerLocalTimeDiff')

            setTimeout(async () => {
              this.$nuxt.$emit('root:showRoleDialogStatus', false)
              this.$nuxt.$emit('root:showLoginDialogStatus', {
                show: false,
                onCancelNotify: () => {}
              })
              // 成功登入，執行使用者登入前動作
              this.callPreLoginAction()
              this.loadingLoginBtnStatus = false
              this.validateCode = ''
              this.$validator.reset('verify_code')
              this.closeDialog()
              // 站台差異註解
              if (this.$UIConfig.lock.getSlotIdentity) {
                const getSlotIdentity = await this.$waninNorthApi.user.getSlotIdentity({
                  PhoneNumber: this.phoneNumber
                })
                if (getSlotIdentity.status !== 200) {
                  this.$nuxt.$emit('root:showIdentityVerifiDialogStatus', true)
                }
              }
            }, 500)
          } else if (res.commandId === 136) {
            this.$notify.error(res.message)
            setTimeout(() => {
              this.loadingLoginBtnStatus = false
              this.disableLoginBtnStatus = false
              this.validateCode = ''
              this.$validator.reset('verify_code')
            }, 1000)
          } else if (res.action === 0 && res.commandId === 135) {
            message = res.message
            this.showNotyDialog(title, message)

            setTimeout(() => {
              this.$nuxt.$emit('root:showDeviceWhiteDialogStatus', false)
              this.loadingLoginBtnStatus = false
              this.validateCode = ''
              this.$validator.reset('verify_code')
            }, 1000)
          } else if (res.commandId === 135) {
            message = res.message
            this.showNotyDialog(title, message)
            this.loadingLoginBtnStatus = false
            this.validateCode = ''
            this.$validator.reset('verify_code')
          }
        }
      }
    }
  }
</script>
