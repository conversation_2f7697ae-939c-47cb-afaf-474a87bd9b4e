<template>
  <div>
    <div class="d-flex flex-column justify-center align-center">
      <div
        v-if="code !== '' && !maintainSystem[0].maintaining"
        class="d-flex justify-center align-center"
      >
        <figure class="qrcode">
          <qrcode :value="code" :options="{ width: 230 }" />

          <img
            v-if="$UIConfig.qpp.showQPPIcon"
            class="qrcode__image"
            :src="getImage('qpp_icon.png')"
            alt="qpp_icon"
          />
        </figure>
      </div>
      <div v-else>
        <div
          class="d-flex flex-column justify-center align-center"
          style="
            width: 230px;
            height: 230px;
            background: rgba(251, 251, 251, 0.08);
            border: 1px solid #4c3535;
          "
        >
          <v-icon color="default-content" size="36">
            {{ maintainSystem[0].maintaining ? 'mdi-hammer-wrench' : 'mdi-alert-octagon-outline' }}
          </v-icon>
          <span class="default-content--text text-body-2 custom-text-noto text-center pt-6">
            {{
              code !== '' && maintainSystem[0].maintaining
                ? $t('function_in_maintenance')
                : $t('get_qrcode_fail')
            }}
          </span>
        </div>
      </div>
      <div class="justify-center align-center pt-4">
        <v-icon class="mr-2" color="grey-2" size="20" @click="goToQppLoginTechDialog"
          >mdi-information-outline
        </v-icon>
        <span class="default-content--text text-body-2 custom-text-noto">
          {{ $t('qpp_login_comment1') }}
        </span>
      </div>
    </div>
    <qppLoginTeach :show-qpp-login-tech-dialog.sync="showQppLoginTechDialog" />
  </div>
</template>

<script>
  import images from '~/mixins/images'
  import analytics from '@/mixins/analytics'
  export default {
    name: 'QppCodeDialog',
    mixins: [images, analytics],
    components: {
      qppLoginTeach: () => import('~/components/login/qppLoginTeach')
    },
    props: {
      qppURL: {
        type: String,
        required: true,
        default: ''
      },
      deviceUUID: {
        type: String,
        required: true,
        default: ''
      }
    },
    data() {
      return {
        showQppLoginTechDialog: false,
        code: '',
        isStop: false
      }
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      phoneNumber({ $store }) {
        return $store.getters['role/phoneNumber']
      }
    },
    beforeDestroy() {
      this.isStop = true
      this.code = ''
      this.showQppLoginTechDialog = false
    },
    watch: {
      qppURL: {
        handler: async function (url) {
          if (url !== '') {
            this.code = ''
            this.code = url
            this.$nextTick(() => {
              this.checkQPPLogined()
            })
          }
        },
        immediate: true
      }
    },
    methods: {
      async checkQPPLogined() {
        let token = ''
        do {
          token = await this.checkingIoCode()
          if (token && token !== undefined) {
            const packet = this.$wsPacketFactory.loginWithIoCode({
              ioCode: token,
              device: this.$cookies.get('xAgent'),
              clientVersion: this.$xinServerConfig.clientVersion,
              serverVersion: this.$xinServerConfig.serverVersion,
              promote: this.$store.getters['social/promote']
            })
            this.$wsClient.send(packet)
            this.isStop = true
            const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) =>
              data.isFeature(this.$xinConfig.FEATURE.OTP.ID)
            )

            if (res.chars.length !== 0) {
              this.$store.commit('role/SET_ROLECOUNT', res.chars.length)
              this.$store.commit('role/SET_LIST', res.chars)
            }
            // 站台差異註解
            if (this.$UIConfig.lock.getSlotIdentity) {
              if (this.phoneNumber !== '') {
                const getSlotIdentity = await this.$waninNorthApi.user.getSlotIdentity({
                  PhoneNumber: this.phoneNumber
                })
                if (getSlotIdentity.status !== 200)
                  this.$nuxt.$emit('root:showIdentityVerifiDialogStatus', true)
                else this.$nuxt.$emit('root:showRoleDialogStatus', true)
              } else {
                this.$nuxt.$emit('root:showRoleDialogStatus', true)
              }
            } else {
              this.$nuxt.$emit('root:showRoleDialogStatus', true)
            }

            this.$nuxt.$emit('root:showLoginDialogStatus', {
              show: false,
              onCancelNotify: () => {}
            })
            this.$emit('closePhoneNumEvent')
            this.$store.commit('role/SET_LOGIN_TYPE', 7)
            return
          } else if (this.isStop) {
            return
          }

          await this.$xinUtility.delay(1000)
        } while (!this.isStop)
      },
      async checkingIoCode() {
        return await this.$qppApi.qpp.getIoCode(this.deviceUUID)
      },
      goToQppLoginTechDialog() {
        this.showQppLoginTechDialog = true
      }
    }
  }
</script>
<style scoped>
  .qrcode {
    display: inline-block;
    font-size: 0;
    margin-bottom: 0;
    position: relative;
  }

  .qrcode__image {
    background-color: #fff;
    border: 0.25rem solid #fff;
    border-radius: 0.25rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.25);
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 25%;
    height: 25%;
  }
</style>
