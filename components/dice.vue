//
<template>
  <div>
    <v-dialog
      v-model="showDiceStatus"
      :fullscreen="$vuetify.breakpoint.xsOnly"
      width="460px"
      height="580px"
      scrollable
      persistent
      transition="dialog-transition"
    >
      <p>
        <v-card
          class="d-flex justify-center rounded-b-0"
          width="460px"
          height="580px"
          elevation="0"
          flat
          tile
        >
          <div>
            <lottie :options="options" :width="400" :height="400" />
          </div>
        </v-card>
      </p>
    </v-dialog>
  </div>
</template>

<script>
  import Lottie from 'vue-lottie'
  import animationData from '~/assets/animation.json'
  export default {
    name: 'Di<PERSON>',
    components: {
      Lottie
    },
    data() {
      return {
        showDiceStatus: false,
        options: {
          animationData: animationData,
          loop: true,
          autoplay: true
        }
      }
    },
    mounted() {},
    methods: {}
  }
</script>
