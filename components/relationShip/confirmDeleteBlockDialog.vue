<template>
  <div>
    <v-dialog
      v-model="confirmDeleteBlockDialogStatusTmp.show"
      persistent
      max-width="400px"
      content-class="rounded-lg"
    >
      <v-card elevation="0" tile color="dialog-fill" class="pa-4 pa-sm-6">
        <v-card-title
          class="d-flex justify-center align-center text-h6 font-weight-regular custom-text-noto grey-1--text pa-0"
        >
          {{ $t('hint') }}
        </v-card-title>
        <v-card-text class="default-content--text text-body-2 custom-text-noto px-0 py-6">
          <span v-html="text"></span>
        </v-card-text>
        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
              ><v-btn
                :class="['text-button', breakpoint.xsOnly ? 'w-100' : '']"
                elevation="0"
                text
                @click="closeDialog"
              >
                {{ $t('cancel') }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                :class="['button-content--text text-button', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                @click="deleteBlock(confirmDeleteBlockDialogStatusTmp.name, true)"
              >
                {{ $t('sure') }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
  import relationship from '@/mixins/relationship.js'
  export default {
    name: 'confirmDeleteBlockDialog',
    mixins: [relationship],
    props: {
      confirmDeleteBlockDialogStatus: { type: Object, default: { show: false, name: '' } }
    },
    data() {
      return {
        disabledTopUpBtnStatus: false,
        confirmDeleteBlockDialogStatusTmp: this.confirmDeleteBlockDialogStatus
      }
    },
    computed: {
      text() {
        return this.$t('remove_from_blacklist_noty', {
          username: `<span class="primary--text custom-text-noto"
              >${this.confirmDeleteBlockDialogStatusTmp.name}</span
            >`
        })
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      confirmDeleteBlockDialogStatus(val) {
        this.confirmDeleteBlockDialogStatusTmp = val
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:confirmDeleteBlockDialogStatus', { show: false, name: '' })
      }
    }
  }
</script>
