<template>
  <v-menu offset-y top>
    <template v-slot:activator="{ on, attrs }">
      <v-btn :elevation="0" color="footer-item" v-bind="attrs" v-on="on">
        <v-img
          :src="getImage('icon/' + getLocale(locale).src)"
          height="24"
          max-width="24"
          contain
          class="d-inline-block"
        />
        <div>
          <span class="white--text mx-2 font-weight-regular">
            {{ getLocale(locale).text }}
            <v-icon color="white">mdi-menu-down</v-icon>
          </span>
        </div>
      </v-btn>
    </template>
    <v-list color="footer-item" class="locale-selection">
      <v-list-item
        v-for="localeItem in localeList"
        :key="localeItem.code"
        :class="{ 'locale-selected': locale === localeItem.code }"
        class="locale-selection-item"
        @click.prevent="locale = localeItem.code"
      >
        <v-list-item-content>
          <v-list-item-title class="d-contents">
            <v-row no-gutters align="center">
              <v-row no-gutters align="center">
                <v-img
                  :src="getImage('icon/' + localeItem.src)"
                  height="24"
                  max-width="24"
                  contain
                  class="d-inline-block"
                />
                <span class="mx-3 vertical-super white--text font-weight-regular">{{
                  localeItem.text
                }}</span>
              </v-row>
              <v-spacer />
              <v-icon v-show="locale === localeItem.code" color="white" class="vertical-top" right>
                mdi-check
              </v-icon>
            </v-row>
          </v-list-item-title>
        </v-list-item-content>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script>
  const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
  const STATION = process.env.STATION
  const stationConfig = require(`@/station/${STATION}/${NUXT_ENV}`).default

  import images from '~/mixins/images'

  export default {
    name: 'Locale',
    mixins: [images],
    data() {
      const localeList = stationConfig.customLocaleList

      return {
        localeList,
        locale: this.$i18n.locale
      }
    },
    watch: {
      locale: {
        handler(code) {
          this.$i18n.setLocale(code)
        }
      }
    },
    methods: {
      getLocale(code) {
        let result = {}
        const target = this.localeList.find((locale) => locale.code === code)
        if (target) {
          result = target
        }

        return result
      }
    }
  }
</script>
