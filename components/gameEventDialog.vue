<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog
    v-model="showGameEventDialogTmp"
    persistent
    :max-width="imgStyle.imgWidth"
    :max-height="imgStyle.imgHeight"
    content-class="transparent elevation-0"
  >
    <!-- img  -->
    <div class="img-block">
      <!-- close button -->
      <v-btn fab text class="close-btn" @click="handleClick">
        <span class="material-symbols-outlined"> close </span>
      </v-btn>
      <div @click="handleEventRedirect" class="img-btn">
        <v-img
          contain
          :width="imgStyle.imgWidth"
          :height="imgStyle.imgHeight"
          :src="getImage(imgStyle.imgSrc)"
          @error="errorCustomImgHandler(imgStyle)"
        />
      </div>
    </div>
    <!-- checkbox -->
  </v-dialog>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import images from '~/mixins/images'
  export default {
    name: 'gameEventDialog',
    mixins: [hiddenScrollHtml, images],
    props: {
      showGameEventDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {
        showGameEventDialogTmp: this.showGameEventDialogStatus,
        todayDontDisplay: false,
        imageLoadingError: false,
        eventRedirectUrl: '',
        eventImg: {
          xl: '',
          lg: '',
          md: '',
          sm: '',
          xs: ''
        }
      }
    },
    watch: {
      showGameEventDialogStatus: {
        handler(status) {
          this.showGameEventDialogTmp = status
        }
      }
    },
    created() {
      this.fetchEventInfo()
    },
    mounted() {
      this.$nextTick(() => {
        const storageGameEvent = this.$localStorage.get('localStorageGameEvent').expired
        if (storageGameEvent && !this.$moment(storageGameEvent).isBefore(this.$moment(), 'day'))
          this.$emit('update:showGameEventDialogStatus', false)
        // 檢查是否為活動轉址
        const isEventRedirect = this.$localStorage.get('isEventRedirect')
        if (isEventRedirect) {
          this.$localStorage.remove('isEventRedirect')
          requestAnimationFrame(() => {
            this.$emit('update:showGameEventDialogStatus', false)
          })
          return
        }
      })
    },

    methods: {
      async fetchEventInfo() {
        const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
        const STATION = process.env.STATION
        const stationConfig = require(`~/station/${STATION}/${NUXT_ENV}`).default
        const pathID = stationConfig.client_id
        const random = Math.floor(Math.random() * 100) + 1
        const eventInfo = `${process.env.IMAGE_URL}/event_redirect/${pathID}/event_redirect.json?v=${random}`
        const res = await fetch(eventInfo)
        const { url, image } = await res.json()
        this.eventRedirectUrl = url
        const path = 'celebration/'
        for (const key in image) {
          this.eventImg[key] = path + image[key]
        }
      },
      handleClick() {
        this.$emit('update:showGameEventDialogStatus', false)
        if (this.todayDontDisplay) {
          const today = this.$moment()
          this.$localStorage.set('localStorageGameEvent', { expired: today })
        }
      },
      async handleEventRedirect() {
        if (this.eventRedirectUrl) {
          this.$nuxt.$loading.start()
          // 設定轉址標記
          this.$localStorage.set('isEventRedirect', true)
          window.location.href = this.eventRedirectUrl
        } else {
          this.handleClick()
        }
      },
      errorCustomImgHandler() {
        this.imageLoadingError = true
      }
    },
    computed: {
      imgStyle() {
        const { innerWidth, innerHeight } = window
        const isLandscape = innerWidth > innerHeight
        const { isDesktop, isTablet } = this.$device
        const breakpoint = this.$vuetify.breakpoint.name
        const deviceConfig = {
          desktop: {
            xl: { width: 800, height: 640, src: this.eventImg.xl },
            lg: { width: 800, height: 640, src: this.eventImg.lg },
            md: { width: 800, height: 640, src: this.eventImg.md },
            sm: { width: 800, height: 640, src: this.eventImg.sm },
            xs: { width: 400, height: 667, src: this.eventImg.xs }
          },
          tablet: {
            landscape: { width: 600, height: 480, src: this.eventImg.lg },
            portrait: { width: 500, height: 834, src: this.eventImg.xs }
          },
          mobile: {
            landscape: { width: 270, height: 216, src: this.eventImg.lg },
            portrait: { width: 327, height: 545, src: this.eventImg.xs }
          }
        }
        let config
        if (isDesktop) config = deviceConfig.desktop[breakpoint]
        else if (isTablet) config = deviceConfig.tablet[isLandscape ? 'landscape' : 'portrait']
        else config = deviceConfig.mobile[isLandscape ? 'landscape' : 'portrait']
        return {
          imgWidth: config.width,
          imgHeight: config.height,
          imgSrc: this.imageLoadingError ? config.src.slice(0, -5) + '.jpg' : config.src
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .img-block {
    position: relative;
  }
  .close-btn {
    position: absolute;
    right: 16px;
    top: 16px;
    z-index: 10;
  }
  .img-btn {
    cursor: pointer;
  }
</style>
