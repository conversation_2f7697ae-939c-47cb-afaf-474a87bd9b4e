<template>
  <v-dialog
    v-model="showLogoutDialogStatusTmp"
    persistent
    max-width="380"
    transition="dialog-transition"
    content-class="rounded-lg"
  >
    <v-card color="dialog-fill" class="pa-4 pa-sm-6">
      <v-card-title
        class="custom-text-noto text-h6 font-weight-regular grey-1--text justify-center pa-0"
      >
        {{ $t('reminder').toUpperCase() }}
      </v-card-title>
      <v-card-text class="px-0 py-6"
        ><p class="custom-text-noto text-body-2 default-content--text ma-0">
          {{ $t('logout_confirm') }}
        </p>
      </v-card-text>
      <v-card-actions class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
            <v-btn
              elevation="0"
              :class="breakpoint.xsOnly ? 'w-100' : ''"
              text
              @click="closeDialog"
            >
              <span class="custom-text-noto default-content--text">
                {{ $t('cancel').toUpperCase() }}
              </span>
            </v-btn></v-col
          >
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              elevation="0"
              @click="logout"
            >
              {{ $t('sure').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import urlQueryGameType from '@/mixins/urlQueryGameType'
  export default {
    name: 'logoutDialog',
    mixins: [hiddenScrollHtml, urlQueryGameType],
    props: {
      showLogoutDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {
        showLogoutDialogStatusTmp: this.showLogoutDialogStatus
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      },
      showGameModeStatus({ $store }) {
        return $store.getters['menu/showGameModeStatus']
      }
    },
    watch: {
      showLogoutDialogStatus: {
        handler(status) {
          this.showLogoutDialogStatusTmp = status
        }
      }
    },
    methods: {
      closeDialog() {
        this.$nuxt.$emit('root:showLogoutDialogStatus', false)
      },
      async logout() {
        this.$nuxt.$emit('root:showLogoutDialogStatus', false)
        this.$nuxt.$emit('root:showRoleDialogStatus', false)
        this.$nuxt.$emit('root:showLoginDialogStatus', { show: false, onCancelNotify: () => {} })
        // 清除導連
        this.$nuxt.$emit('root:clearRedirect')
        setTimeout(() => {
          const reqData = this.$wsPacketFactory.logout()
          this.$wsClient.send(reqData)
          this.$wsClient.disconnect()
          this.$nuxt.$emit('root:showLoginDialogStatus', {
            show: true,
            onCancelNotify:
              this.showGameModeStatus && this.$route.params.mode === 'play'
                ? () => this.$router.push(this.localePath('/'))
                : () => {}
          })
          this.$store.dispatch('clear')
          this.$cookies.remove('xinToken', { path: '/' })
        }, 100)
      }
    }
  }
</script>
