<template>
  <client-only>
    <v-snackbar v-model="GET_SHOW" :timeout="GET_TIME" top right shaped :class="[bgColor]">
      <!-- content -->
      <v-container fluid :class="[textColor, 'px-0']">
        <v-row no-gutters>
          <v-col cols="1">
            <v-icon :color="color">mdi-alert</v-icon>
          </v-col>
          <v-col cols="11" class="pl-2">
            <div v-if="GET_TITLE && GET_TITLE.length != 0">
              <v-row no-gutters class="font-body-1" style="font-size: 16px !important">
                {{ GET_TITLE }}</v-row
              >
            </div>
            <div v-if="GET_MESSAGE1 && GET_MESSAGE1.length != 0">
              <v-row no-gutters class="font-body-1" style="font-size: 16px !important">
                {{ GET_MESSAGE1 }}</v-row
              >
            </div>
            <v-divider
              v-if="GET_MESSAGE2 && GET_MESSAGE2.length != 0"
              :style="{ color: bdColor }"
              class="my-4"
            ></v-divider>
            <div v-if="GET_MESSAGE2 && GET_MESSAGE2.length != 0">
              <v-row no-gutters class="font-body-1" style="font-size: 14px !important">
                {{ GET_MESSAGE2 }}</v-row
              >
            </div>
          </v-col>
        </v-row>
      </v-container>
      <!-- close icon -->
      <template v-slot:action="{ attrs }">
        <v-btn :color="color" icon v-bind="attrs" @click="closeSnackbar">
          <v-icon>mdi-close-circle </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
  </client-only>
</template>

<script>
  import { mapGetters } from 'vuex'

  export default {
    name: 'NotySnackbar',
    props: {
      showNotySnackbarStatus: { type: Boolean, default: true }
    },
    data() {
      return {
        showNotySnackbarStatusTmp: this.showNotySnackbarStatus
      }
    },
    watch: {
      showNotySnackbarStatus: {
        handler(status) {
          this.showNotySnackbarStatusTmp = status
        }
      }
    },
    mounted() {},
    computed: {
      ...mapGetters('easySnackbar', [
        'GET_TYPE',
        'GET_TITLE',
        'GET_MESSAGE1',
        'GET_MESSAGE2',
        'GET_SHOW',
        'GET_TIME'
      ]),
      bgColor() {
        let color = ''
        if (this.GET_TYPE == 'warning') color = 'snackbar--warning'
        else if (this.GET_TYPE == 'sucess') color = 'snackbar--sucess'
        return color
      },
      textColor() {
        let color = ''
        if (this.GET_TYPE == 'warning') color = 'warning--text'
        else if (this.GET_TYPE == 'sucess') color = 'sucess--text'
        return color
      },
      color() {
        let color = ''
        if (this.GET_TYPE == 'warning') color = 'warning'
        else if (this.GET_TYPE == 'sucess') color = 'sucess'
        return color
      },
      bdColor() {
        let color = ''
        if (this.GET_TYPE == 'warning') color = 'rgba(255, 121, 23, 0.22)'
        return color
      }
    },
    methods: {
      closeSnackbar() {
        this.$store.commit('easySnackbar/RESET')
      }
    }
  }
</script>
<style lang="scss" scoped>
  .snackbar--warning::v-deep {
    .v-snack__wrapper {
      background: linear-gradient(0deg, rgba(255, 121, 23, 0.12), rgba(255, 121, 23, 0.12)),
        linear-gradient(0deg, #ffffff, #ffffff);
    }
  }

  .snackbar--sucess::v-deep {
    .v-snack__wrapper {
      background: linear-gradient(0deg, rgba(76, 175, 80, 0.12), rgba(76, 175, 80, 0.12)), #ffffff;
    }
  }
</style>
