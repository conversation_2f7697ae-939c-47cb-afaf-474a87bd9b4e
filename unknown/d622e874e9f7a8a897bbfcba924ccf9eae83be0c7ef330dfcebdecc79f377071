export default class News {
  constructor(axios, ctx) {
    this.axios = axios
    this.ctx = ctx
  }

  getNews({ type, lang, limit, offset }) {
    let params = {
      type,
      lang,
      limit,
      offset
    }
    return this.axios.get('/api/client/announcement/list', {
      params: params
    })
  }

  getMarqueeTitle({ type, lang, limit, offset }) {
    let params = {
      type,
      lang,
      limit,
      offset
    }
    return this.axios.get('/api/client/marquee/list', {
      params: params
    })
  }
}
