export default [
  {
    key: 'phone_number',
    icon: 'cell',
    text: 'sns_title.phone_number',
    label: '',
    operate: false,
    order: 1,
    link: null,
    regexp: null,
    validate: ''
  },
  {
    key: 'email',
    icon: 'email',
    text: 'sns_title.email',
    label: '',
    operate: false,
    order: 2,
    link: null,
    regexp: null,
    validate: ''
  },
  {
    key: 'mail',
    icon: 'mail_2',
    text: 'sns_title.mail',
    label: 'email',
    operate: true,
    order: 3,
    link: null,
    regexp: null,
    validate: 'email'
  },
  {
    key: 'facebook',
    icon: 'facebook',
    text: 'sns_title.facebook',
    label: 'enter_username',
    operate: true,
    order: 4,
    link: 'https://facebook.com/{{link}}',
    regexp: /^[a-zA-Z0-9.]{0,50}$/,
    validate: 'max:50|sns:facebook'
  },
  {
    key: 'facebook_group',
    icon: 'facebook_group',
    text: 'sns_title.facebook_group',
    label: 'enter_league_id',
    operate: true,
    order: 5,
    link: 'https://www.facebook.com/groups/{{link}}',
    regexp: /^[0-9]{0,50}$/,
    validate: 'max:15|sns:facebook_group'
  },
  {
    key: 'flickr',
    icon: 'flickr',
    text: 'sns_title.flickr',
    label: 'enter_username',
    operate: true,
    order: 6,
    link: 'https://www.flickr.com/people/{{link}}',
    regexp: /^[a-zA-Z0-9@_-]{0,32}$/,
    validate: 'max:32|sns:flickr'
  },
  {
    key: 'instagram',
    icon: 'instagram',
    text: 'sns_title.instagram',
    label: 'enter_username',
    operate: true,
    order: 7,
    link: 'https://www.instagram.com/{{link}}',
    regexp: /^[a-zA-Z0-9_.]{8,100}$/,
    validate: 'min:8|max:100|sns:instagram'
  },
  {
    key: 'line',
    icon: 'line',
    text: 'sns_title.line',
    label: 'enter_id',
    operate: true,
    order: 8,
    link: 'https://line.me/ti/p/{{link}}',
    regexp: /^[a-zA-Z0-9@_-]{0,11}$/,
    validate: 'max:11|sns:line'
  },
  {
    key: 'line_group',
    icon: 'line_group',
    text: 'sns_title.line_group',
    label: 'enter_id',
    operate: true,
    order: 9,
    link: 'https://line.me/ti/g/{{link}}',
    regexp: /^[a-zA-Z0-9_-]{0,10}$/,
    validate: 'max:10|sns:line_group'
  },
  {
    key: 'linkedin',
    icon: 'linkedin',
    text: 'sns_title.linkedin',
    label: 'enter_account',
    operate: true,
    order: 10,
    link: 'https://www.linkedin.com/in/{{link}}',
    regexp: /^[a-zA-Z0-9-]{3,100}$/,
    validate: 'min:3|max:100|sns:linkedin'
  },
  {
    key: 'skype',
    icon: 'skype',
    text: 'sns_title.skype',
    label: 'enter_id',
    operate: true,
    order: 11,
    link: 'https://join.skype.com/invite/{{link}}',
    regexp: /^[a-zA-Z0-9]{0,12}$/,
    validate: 'max:12|sns:skype'
  },
  {
    key: 'skype_group',
    icon: 'skype_group',
    text: 'sns_title.skype_group',
    label: 'enter_id',
    operate: true,
    order: 12,
    link: 'https://join.skype.com/{{link}}',
    regexp: /^[a-zA-Z0-9]{0,12}$/,
    validate: 'max:12|sns:skype_group'
  },
  {
    key: 'telegram',
    icon: 'telegram',
    text: 'sns_title.telegram',
    label: 'enter_username',
    operate: true,
    order: 13,
    link: 'https://t.me/{{link}}',
    regexp: /^[a-zA-Z0-9_]{5,32}$/,
    validate: 'min:5|max:32|sns:telegram'
  },
  {
    key: 'telegram_group',
    icon: 'telegram_group',
    text: 'sns_title.telegram_group',
    label: 'enter_id',
    operate: true,
    order: 14,
    link: 'https://t.me/joinchat/{{link}}',
    regexp: /^[a-zA-Z0-9]{5,32}$/,
    validate: 'min:5|max:32|sns:telegram_group'
  },
  {
    key: 'tumblr',
    icon: 'tumblr',
    text: 'sns_title.tumblr',
    label: 'enter_username',
    operate: true,
    order: 15,
    link: 'https://{{link}}.tumblr.com/',
    regexp: /^[a-zA-Z0-9-]{0,32}$/,
    validate: 'max:32|sns:tumblr'
  },
  {
    key: 'twitter',
    icon: 'twitter',
    text: 'sns_title.twitter',
    label: 'enter_username',
    operate: true,
    order: 16,
    link: 'https://twitter.com/{{link}}',
    regexp: /^[a-zA-Z0-9_]{0,15}$/,
    validate: 'max:15|sns:twitter'
  },
  {
    key: 'wechat',
    icon: 'wechat',
    text: 'sns_title.wechat',
    label: 'enter_id_or_phone_number',
    operate: true,
    order: 17,
    link: null,
    regexp: /^[a-zA-Z0-9_-]{6,20}$/,
    validate: 'min:6|max:20|sns:wechat'
  },
  {
    key: 'whatsapp',
    icon: 'whatsapp',
    text: 'sns_title.whatsapp',
    label: 'enter_phone_number',
    operate: true,
    order: 18,
    link: 'https://wa.me/{{link}}',
    regexp: /^[1-9][0-9]{0,15}$/,
    validate: 'max:15|no_zero_start|sns:whatsapp'
  },
  {
    key: 'whatsapp_group',
    icon: 'whatsapp_group',
    text: 'sns_title.whatsapp_group',
    label: 'enter_group_id',
    operate: true,
    order: 19,
    link: 'https://chat.whatsapp.com/{{link}}',
    regexp: /^[a-zA-Z0-9]{0,22}$/,
    validate: 'max:22|sns:whatsapp_group'
  },
  {
    key: 'youtube',
    icon: 'youtube',
    text: 'sns_title.youtube',
    label: 'enter_channel_id',
    operate: true,
    order: 20,
    link: 'https://www.youtube.com/channel/{{link}}',
    regexp: /^[a-zA-Z0-9_]{0,24}$/,
    validate: 'max:24|sns:youtube'
  }
]
