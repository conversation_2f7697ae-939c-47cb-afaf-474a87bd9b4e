{"v": "5.9.0", "fr": 24, "ip": 0, "op": 48, "w": 300, "h": 300, "nm": "music_note_selected", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "圖層 3/icon Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0], "e": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 3, "s": [0], "e": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [10], "e": [-10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [-10], "e": [3]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21, "s": [3], "e": [0]}, {"t": 24}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [150.5, 244.3, 0], "e": [150.5, 240.3, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [150.5, 240.3, 0], "e": [150.5, 244.3, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 45}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [67.45, 187.85, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 40, "s": [100, 100, 100], "e": [100, 102.665, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 43, "s": [100, 102.665, 100], "e": [100, 92.271, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 45, "s": [100, 92.271, 100], "e": [100, 100, 100]}, {"t": 47}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -93.8], [34.75, -93.8], [46.75, -93.8], [57.923, -93.8], [67.2, -84.523], [67.2, -58.277], [57.923, -49], [49, -49], [40.75, -49], [32, -49], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -97.3], [34.75, -98.55], [46.75, -98.55], [57.923, -97.3], [67.2, -88.023], [67.2, -61.777], [57.923, -52.5], [49, -53.75], [40.75, -53.75], [32, -52.5], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -97.3], [34.75, -98.55], [46.75, -98.55], [57.923, -97.3], [67.2, -88.023], [67.2, -61.777], [57.923, -52.5], [49, -53.75], [40.75, -53.75], [32, -52.5], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -90.675], [34.75, -88.8], [46.75, -88.175], [57.923, -89.3], [67.2, -80.023], [67.2, -53.777], [57.923, -44.5], [49, -43.375], [40.75, -44], [32, -45.875], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -90.675], [34.75, -88.8], [46.75, -88.175], [57.923, -89.3], [67.2, -80.023], [67.2, -53.777], [57.923, -44.5], [49, -43.375], [40.75, -44], [32, -45.875], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -97.3], [34.75, -98.55], [46.75, -98.55], [57.923, -97.3], [67.2, -88.023], [67.2, -61.777], [57.923, -52.5], [49, -53.75], [40.75, -53.75], [32, -52.5], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -97.3], [34.75, -98.55], [46.75, -98.55], [57.923, -97.3], [67.2, -88.023], [67.2, -61.777], [57.923, -52.5], [49, -53.75], [40.75, -53.75], [32, -52.5], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -90.675], [34.75, -89.8], [46.75, -89.738], [57.923, -90.863], [67.2, -81.585], [67.2, -55.339], [57.923, -46.063], [49, -44.938], [40.75, -45.563], [32, -46.875], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -90.675], [34.75, -89.8], [46.75, -89.738], [57.923, -90.863], [67.2, -81.585], [67.2, -55.339], [57.923, -46.063], [49, -44.938], [40.75, -45.563], [32, -46.875], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -95.863], [34.75, -96.675], [46.75, -96.675], [57.923, -95.425], [67.2, -86.148], [67.2, -59.902], [57.923, -50.625], [49, -51.875], [40.75, -51.875], [32, -51.062], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -95.863], [34.75, -96.675], [46.75, -96.675], [57.923, -95.425], [67.2, -86.148], [67.2, -59.902], [57.923, -50.625], [49, -51.875], [40.75, -51.875], [32, -51.062], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -93.8], [34.75, -93.8], [46.75, -93.8], [57.923, -93.8], [67.2, -84.523], [67.2, -58.277], [57.923, -49], [49, -49], [40.75, -49], [32, -49], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -93.8], [34.75, -93.8], [46.75, -93.8], [57.923, -93.8], [67.2, -84.523], [67.2, -58.277], [57.923, -49], [49, -49], [40.75, -49], [32, -49], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -93.8], [34.75, -93.8], [46.75, -93.8], [57.923, -93.8], [67.2, -84.523], [67.2, -58.277], [57.923, -49], [49, -49], [40.75, -49], [32, -49], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -93.8], [34.75, -93.8], [46.75, -93.8], [57.923, -93.8], [67.2, -84.523], [67.2, -58.277], [57.923, -49], [49, -49], [40.75, -49], [32, -49], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -97.3], [34.75, -98.55], [46.75, -98.55], [57.923, -97.3], [67.2, -88.023], [67.2, -61.777], [57.923, -52.5], [49, -53.75], [40.75, -53.75], [32, -52.5], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -97.3], [34.75, -98.55], [46.75, -98.55], [57.923, -97.3], [67.2, -88.023], [67.2, -61.777], [57.923, -52.5], [49, -53.75], [40.75, -53.75], [32, -52.5], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -90.675], [34.75, -88.8], [46.75, -88.175], [57.923, -89.3], [67.2, -80.023], [67.2, -53.777], [57.923, -44.5], [49, -43.375], [40.75, -44], [32, -45.875], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -90.675], [34.75, -88.8], [46.75, -88.175], [57.923, -89.3], [67.2, -80.023], [67.2, -53.777], [57.923, -44.5], [49, -43.375], [40.75, -44], [32, -45.875], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -97.3], [34.75, -98.55], [46.75, -98.55], [57.923, -97.3], [67.2, -88.023], [67.2, -61.777], [57.923, -52.5], [49, -53.75], [40.75, -53.75], [32, -52.5], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -97.3], [34.75, -98.55], [46.75, -98.55], [57.923, -97.3], [67.2, -88.023], [67.2, -61.777], [57.923, -52.5], [49, -53.75], [40.75, -53.75], [32, -52.5], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -90.675], [34.75, -89.8], [46.75, -89.738], [57.923, -90.863], [67.2, -81.585], [67.2, -55.339], [57.923, -46.063], [49, -44.938], [40.75, -45.563], [32, -46.875], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -90.675], [34.75, -89.8], [46.75, -89.738], [57.923, -90.863], [67.2, -81.585], [67.2, -55.339], [57.923, -46.063], [49, -44.938], [40.75, -45.563], [32, -46.875], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -95.863], [34.75, -96.675], [46.75, -96.675], [57.923, -95.425], [67.2, -86.148], [67.2, -59.902], [57.923, -50.625], [49, -51.875], [40.75, -51.875], [32, -51.062], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -95.863], [34.75, -96.675], [46.75, -96.675], [57.923, -95.425], [67.2, -86.148], [67.2, -59.902], [57.923, -50.625], [49, -51.875], [40.75, -51.875], [32, -51.062], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -93.8], [34.75, -93.8], [46.75, -93.8], [57.923, -93.8], [67.2, -84.523], [67.2, -58.277], [57.923, -49], [49, -49], [40.75, -49], [32, -49], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -93.8], [34.75, -93.8], [46.75, -93.8], [57.923, -93.8], [67.2, -84.523], [67.2, -58.277], [57.923, -49], [49, -49], [40.75, -49], [32, -49], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}], "e": [{"i": [[12.32, 0], [8.773, 8.773], [0, 12.32], [-8.773, 8.773], [-12.32, 0], [-3.64, -1.027], [-3.36, -2.053], [0, 0], [-5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.124], [0, 0], [5.124, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.773, -8.773]], "o": [[-12.32, 0], [-8.773, -8.773], [0, -12.32], [8.773, -8.773], [4.293, 0], [3.64, 1.027], [0, 0], [0, -5.124], [0, 0], [0, 0], [0, 0], [0, 0], [5.124, 0], [0, 0], [0, 5.124], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 12.32], [-8.773, 8.773]], "v": [[-22.4, 93.8], [-54.04, 80.64], [-67.2, 49], [-54.04, 17.36], [-22.4, 4.2], [-10.5, 5.74], [0, 10.36], [0, -84.523], [9.277, -93.8], [23.5, -93.8], [34.75, -93.8], [46.75, -93.8], [57.923, -93.8], [67.2, -84.523], [67.2, -58.277], [57.923, -49], [49, -49], [40.75, -49], [32, -49], [22.4, -49], [22.4, 49], [9.24, 80.64]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.913725490196, 0.725490196078, 0.313725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [67.45, 94.05], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 48, "st": 0, "bm": 0}], "markers": []}