export default class payment {
  constructor(axios) {
    this.axios = axios
  }

  convertPoints({ points = 0, amount = 0 }) {
    const formData = new FormData()

    formData.set('points', points)
    formData.set('amount', amount)

    return true

    // return this.axios.$post('/api/client/wallet/platform/deposit', formData)
  }

  checkTopUpCardAndPwd({ userId = '', cardNumber = '', cardPwd = '' }) {
    const formData = new FormData()

    formData.set('userId', userId)
    formData.set('cardNumber', cardNumber)
    formData.set('cardPwd', cardPwd)

    return { errorcode: 1, price: 150, points: 150 }

    // return this.axios.$post('/api/client/wallet/platform/deposit', formData)
  }

  checkTopUpCardAndSerialNumber({ userId = '', cardSerialNumber = '' }) {
    const formData = new FormData()

    formData.set('userId', userId)
    formData.set('cardSerialNumber', cardSerialNumber)

    return { errorcode: 3 }

    // return this.axios.$post('/api/client/wallet/platform/deposit', formData)
  }

  topUp({ userId = '' }) {
    const formData = new FormData()

    formData.set('userId', userId)

    return { errorcode: 1, first: true }

    // return this.axios.$post('/api/client/wallet/platform/deposit', formData)
  }

  sendAnalytics({ username, type, point }) {
    return this.axios.$post('/api/client/record/deposited', { username, type, point })
  }
}
