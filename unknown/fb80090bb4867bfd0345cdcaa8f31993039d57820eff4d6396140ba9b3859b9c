<template>
  <v-container v-if="isGrandPrizeNotyShow" fluid class="pa-0">
    <!-- grand prize noty -->
    <v-row no-gutters align="center">
      <span class="liveDot mr-3 rounded-circle"></span>
      <span class="font-weight-bold text-h5 gradient-primary--text custom-text-noto mr-4">
        {{ $t('grand_prize_noty') }}
      </span>
      <gradientDivider />
    </v-row>
    <div :class="$UIConfig.grandPrizeNoty.pageClass">
      <v-row no-gutters align="center" class="grand-prize-cards">
        <template v-if="grandPrizeListTmp.length === 0">
          <v-col class="grand-prize-card flex-grow-0" :style="cardStyle">
            <v-card class="rounded-0" color="transparent" elevation="0">
              <v-img
                :src="gameDefaultImg"
                :aspect-ratio="gameImgAspectRatio"
                :style="imageStyle()"
                @error="setGameImg()"
              ></v-img>
              <v-card-title class="pa-0 pt-1 justify-center">
                <span
                  class="font-weight-bold text-caption custom-text-noto text-truncate text-game"
                >
                  {{ $t('grand_prize_game_name') }}
                </span>
              </v-card-title>
              <v-card-subtitle class="grand-prize-card-subtitle mt-0 pa-0 text-truncate">
                {{ $t('grand_prize_player_name') }}
              </v-card-subtitle>
              <v-card-actions class="grand-prize-card-actions justify-center pa-0">
                <v-img
                  :src="getImage('<EMAIL>')"
                  :srcset="getSrcset('coin')"
                  width="10"
                  height="10"
                  contain
                  class="flex-grow-0"
                />
                <span class="font-weight-bold text-caption custom-text-noto">???</span>
              </v-card-actions>
            </v-card>
          </v-col>
        </template>
        <template v-else>
          <v-col
            v-for="item in grandPrizeListTmp"
            :key="item.idx"
            class="grand-prize-card flex-grow-0"
            :style="cardStyle"
          >
            <v-card
              color="transparent"
              elevation="0"
              :class="$UIConfig.grandPrizeNoty.grandPrizeItem.class"
              @click="goDownload"
            >
              <v-img
                :src="checkGameImg(item)"
                :aspect-ratio="gameImgAspectRatio"
                @error="setGameImg(item)"
                :style="imageStyle()"
              ></v-img>
              <v-card-title class="pa-0 pt-1 justify-center">
                <span
                  class="font-weight-bold text-caption custom-text-noto text-truncate text-game"
                >
                  {{ item.gameName }}
                </span>
              </v-card-title>
              <v-card-subtitle class="grand-prize-card-subtitle mt-0 pa-0 text-truncate">
                {{ item.playerName }}
              </v-card-subtitle>
              <v-card-actions class="grand-prize-card-actions justify-center pa-0">
                <v-img
                  :src="getImage('<EMAIL>')"
                  :srcset="getSrcset('coin')"
                  width="10"
                  height="10"
                  contain
                  class="flex-grow-0"
                />
                <span class="font-weight-bold text-caption custom-text-noto">
                  {{ formatScorePoint(item.scorePoint) }}
                </span>
              </v-card-actions>
            </v-card>
          </v-col>
        </template>
      </v-row>
    </div>
    <confirmGoAppDialog :confirm-go-app-dialog-status.sync="confirmGoAppDialogStatus" />
  </v-container>
</template>
<script>
  import analytics from '@/mixins/analytics.js'
  import images from '~/mixins/images'

  export default {
    name: 'GrandPrizeNoty',
    mixins: [analytics, images],
    components: {
      gradientDivider: () => import('~/components/gradientDivider.vue'),
      confirmGoAppDialog: () => import('~/components/grandPrize/confirmGoAppDialog.vue')
    },
    data() {
      return {
        grandPrizeListTmp: [],
        // 用來防止圖片載入時(圖片高度為 0)，底下文字會往上偏移
        gameImgAspectRatio: 97 / 104,
        gameDefaultImg: this.$UIConfig.grandPrizeNoty.defaultImg,
        gameDefaultImgPng: this.$UIConfig.grandPrizeNoty.defaultImgPng,
        confirmGoAppDialogStatus: false
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      isPlaying({ $store }) {
        return $store.getters['menu/showGameModeStatus']
      },
      isGrandPrizeNotyShow() {
        return this.isLogin && this.$UIConfig.lock.grandPrizeNoty
      },
      grandPrizeList({ $store }) {
        const list = $store.getters['grandPrize/grandPrizeList']
        const { mdAndUp, smOnly } = this.$vuetify.breakpoint
        const max = mdAndUp ? 12 : smOnly ? 8 : 5
        return list.slice(0, max)
      },
      cardStyle() {
        const breakpoint = this.$vuetify.breakpoint
        const width = breakpoint.mdAndUp
          ? 'calc(100% / 12)'
          : breakpoint.smOnly
          ? 'calc(100% / 8)'
          : '20%'
        return { 'flex-basis': width, 'max-width': width }
      }
    },
    watch: {
      // 若玩家非遊玩狀態，實時更新拉彩資料(grandPrizeListTmp)，若在遊玩狀態，則暫停更新
      grandPrizeList: {
        handler(val) {
          if (!this.isPlaying) this.grandPrizeListTmp = val
        }
      },
      // 若玩家剛結束遊玩，立即更新拉彩資料(grandPrizeListTmp)
      isPlaying: {
        handler(val) {
          if (!val) this.grandPrizeListTmp = this.grandPrizeList
        }
      }
    },
    mounted() {
      this.grandPrizeListTmp = this.grandPrizeList
    },
    methods: {
      imageStyle() {
        return this.$UIConfig.grandPrizeNoty.imgStyle
      },
      goDownload() {
        if (!this.$UIConfig.grandPrizeNoty.grandPrizeItem.hasDownLoad) return
        const handleAnalytics = (device) => {
          if (device === 'lotteryandroid' || device === 'lotteryios') {
            this.confirmGoAppDialogStatus = true
          } else {
            this.$router.push({ path: this.localePath('/downloads'), hash: '#pc' })
          }
          this.grandPrizeAnalytics(device)
        }

        if (this.$device.isAndroid) {
          handleAnalytics('lotteryandroid')
        } else if (this.$device.isIos || this.$device.isMacOS) {
          handleAnalytics('lotteryios')
        } else {
          handleAnalytics('lotterypc')
        }
      },
      formatScorePoint(scorePoint) {
        // 超過1億，無條件捨去小數點
        if (scorePoint >= 100000000) {
          return Math.floor(scorePoint / 1000000) + 'M'
        }
        // 超過百萬，保留小數點後一位，無條件捨去小數點後二位
        else if (scorePoint >= 1000000) {
          const millions = Math.floor((scorePoint / 1000000) * 10) / 10
          return millions + 'M'
        }
        // 超過1000，顯示為K，無條件捨去小數點
        else if (scorePoint >= 1000) {
          return Math.floor(scorePoint / 1000) + 'K'
        }
        // 若小於1000，直接顯示
        return scorePoint
      },
      checkGameImg(item) {
        // 主要處理 item.gameImg 是空字串，不會觸發 @error 改為預設圖的情況
        if (!item.gameImg) {
          this.$store.commit('grandPrize/SET_GRAND_PRIZE_IMG', {
            idx: item.idx,
            newUrl: this.gameDefaultImg
          })
        }
        return item.gameImg
      },
      setGameImg(item) {
        // 無拉彩資料時，預設圖有問題，將檔案從 webp 改為 png
        if (!item) {
          this.gameDefaultImg = this.gameDefaultImgPng
          return
        }

        const newUrl = item.gameImg.includes(this.gameDefaultImg)
          ? this.gameDefaultImgPng
          : this.gameDefaultImg

        this.$store.commit('grandPrize/SET_GRAND_PRIZE_IMG', {
          idx: item.idx,
          newUrl
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
  $primary: map-get($colors, 'primary');
  $primary-variant-2: map-get($colors, 'primary-variant-2');

  .text-caption {
    height: 1.25rem;
  }
  .liveDot {
    width: 0.5rem;
    aspect-ratio: 1;
    background-color: $primary;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      aspect-ratio: 1;
      background-color: $primary;
      border-radius: 50%;
      animation: liveDotPing 1s cubic-bezier(0, 0, 0.35, 1) infinite;
    }
  }
  .grand-prize-cards-wrapper {
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.08);
    .grand-prize-cards {
      margin-inline: -6px;
      .grand-prize-card {
        padding-inline: 6px;
        .v-card:focus::before {
          opacity: 0;
        }
        .text-game {
          color: $primary-variant-2;
        }
        .grand-prize-card-subtitle {
          font-size: 10px;
          line-height: 20px;
          height: 20px;
          letter-spacing: 0.4px;
        }
        .grand-prize-card-actions {
          color: $primary;
          .v-image {
            margin-right: 3px;
          }
        }
      }
    }
  }
  @keyframes liveDotPing {
    70%,
    to {
      transform: scale(2.5);
      opacity: 0;
    }
  }
</style>
