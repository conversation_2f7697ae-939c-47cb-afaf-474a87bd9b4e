export default class user {
  constructor(axios) {
    this.axios = axios
  }
  async getSlotIdentity({ PhoneNumber, XinKey }) {
    const params =
      PhoneNumber === undefined || PhoneNumber === ''
        ? {
            params: { Xin<PERSON>ey: <PERSON><PERSON><PERSON><PERSON> }
          }
        : {
            params: { PhoneNumber: PhoneNumber }
          }
    return await this.axios
      .get('/api/SlotIdentity', params, { timeout: 30000 })
      .then((response) => {
        return response
      })
      .catch((err) => {
        return err.response
      })
  }

  async postSlotIdentity({ PhoneNumber, Identity, Name, Birthday }) {
    await this.axios
      .post(
        '/api/SlotIdentity',
        JSON.stringify({
          PhoneNumber,
          Identity,
          Name,
          Birthday
        }),
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
      .then((response) => {
        return response
      })
      .catch((err) => {
        return err
      })
  }
}
