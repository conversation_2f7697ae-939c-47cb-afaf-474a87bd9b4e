import encrypt from '~/utils/encrypt'
const STATION = process.env.STATION
const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
const loadConfig = require(`~/station/${STATION}/${NUXT_ENV}.js`).default
export default class Maintain {
  constructor(axios, ctx) {
    this.axios = axios
    this.ctx = ctx
  }

  async getMaintainList(headerData) {
    //傳送 x-secure-token
    const encryptionKey = encrypt.getEncryptionKey({
      clientId: loadConfig.client_id,
      sercetKey: loadConfig.secret_key
    })
    const token = await encrypt.encryptAndSend({
      alias: headerData.alias,
      username: headerData.username,
      encryptionKey
    })
    const head = {
      'X-Secure-Token': token
    }
    let body = {}
    if (
      this.ctx.route.params.qcStation &&
      Object.keys(this.ctx.route.params.qcStation).length > 0
    ) {
      body.stationName = this.ctx.route.params.qcStation
    }

    let maintainres = await this.axios.get('/api/maintain/list', { headers: head, params: body })
    return maintainres
  }
}
